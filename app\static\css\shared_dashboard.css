/* Shared styles across dashboard components */

/* Card styling */
.card {
    border-radius: 12px;
    border: none;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem 1.25rem;
    border-radius: 12px 12px 0 0 !important;
}

.card-header h5 {
    font-weight: 600;
    color: #212529;
}

/* Status badges */
.status-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-weight: bold;
    display: inline-block;
    min-width: 80px;
    text-align: center;
}

.status-badge.free {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
    border: 1px solid #198754;
}

.status-badge.occupied {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid #dc3545;
}

/* Stats section */
.stats {
    display: flex;
    justify-content: space-between;
    text-align: center;
    margin-top: 10px;
}

.stat {
    flex: 1;
}

.stat h6 {
    margin-bottom: 5px;
    font-weight: bold;
    color: #6c757d;
}

.stat p {
    font-size: 1.25rem;
    margin-bottom: 0;
    font-weight: bold;
}

/* Occupancy meter */
.occupancy-meter {
    margin-top: -1px;
    background: #fff;
    padding: 0;
}

.occupancy-meter .progress {
    height: 8px;
    border-radius: 0;
    background-color: rgba(220, 53, 69, 0.1);
    box-shadow: none;
    overflow: hidden;
    margin: 0;
}

.occupancy-meter .progress-bar {
    background: linear-gradient(90deg, #198754 0%, #dc3545 100%);
    transition: width 0.5s ease;
}

.occupancy-meter .progress-label {
    text-align: center;
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.5rem;
    font-weight: 500;
    padding: 0.5rem;
}

/* Action buttons */
.action-buttons {
    opacity: 0.8;
}

.action-buttons .btn {
    min-width: 120px;
}

.action-buttons .btn i {
    margin-right: 0.25rem;
}

/* Parking slot styling */
.parking-slot {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0.7;
    pointer-events: all;
    transform-origin: 0 0;
    margin: 0;
    padding: 0;
    z-index: 2;
}

.parking-slot .label {
    position: absolute;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 0.9rem;
    z-index: 3;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    pointer-events: none;
    white-space: nowrap;
}

.parking-slot.free {
    background-color: rgba(40, 167, 69, 0.3);
    border: 2px solid rgba(40, 167, 69, 0.8);
}

.parking-slot.occupied {
    background-color: rgba(220, 53, 69, 0.3);
    border: 2px solid rgba(220, 53, 69, 0.8);
}

/* Animation for slot status changes */
@keyframes slot-status-change {
    0% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.3); opacity: 0.9; }
    100% { transform: scale(1); opacity: 0.7; }
}

.parking-slot.status-changed {
    animation: slot-status-change 1000ms cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
    z-index: 4;
    pointer-events: none;
    box-shadow: 0 0 25px currentColor;
}

/* Event status badges */
.status-change {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-change.to-occupied {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.2);
}

.status-change.to-free {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
    border: 1px solid rgba(25, 135, 84, 0.2);
}

.status-change.to-neutral {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.2);
}

/* Source badges in event log */
.source-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.source-badge.source-detection {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
    border: 1px solid rgba(13, 110, 253, 0.2);
}

.source-badge.source-manual {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.2);
}

.source-badge.source-system {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
    border: 1px solid rgba(25, 135, 84, 0.2);
}

.source-badge.source-stream {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-img-container {
        height: 160px;
    }
}