/* Lot dashboard specific styles */

/* Parking slot styles (same as zone editor) */
.parking-slot {
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    width: 100% !important;
    height: 100% !important;
    pointer-events: none;
    z-index: 10;
    transform-origin: 0 0;
}

.parking-slot.free {
    background-color: rgba(40, 167, 69, 0.3);
    border: 2px solid rgba(40, 167, 69, 0.8);
}

.parking-slot.occupied {
    background-color: rgba(220, 53, 69, 0.3);
    border: 2px solid rgba(220, 53, 69, 0.8);
}

.slot-label {
    position: absolute;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    white-space: nowrap;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 11;
}

/* Stats section */
.stats-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    align-items: center;
}

/* Override hover animations specifically for lot dashboard */
.lot-dashboard .stat-box {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transform: none !important; /* Prevent hover transform */
    transition: none !important; /* Disable transitions */
}

.lot-dashboard .stat-box:hover {
    transform: none !important; /* Ensure no transform on hover */
}

.stat-box {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.stat-box.free {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
    border: 2px solid rgba(25, 135, 84, 0.2);
}

.stat-box.occupied {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 2px solid rgba(220, 53, 69, 0.2);
}

.stat-box.free .stat-icon {
    color: #198754;
}

.stat-box.occupied .stat-icon {
    color: #dc3545;
}

.stat-icon {
    font-size: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
}

.stat-content {
    flex: 1;
}

.stat-box h2 {
    font-size: 2.5rem;
    margin: 0;
    font-weight: bold;
    line-height: 1.2;
}

.stat-box p {
    font-size: 1rem;
    margin: 4px 0 0;
    opacity: 0.9;
}

/* Parking visualization */
.parking-visualization {
    position: relative;
    width: 100%;
    margin: 0;
    padding: 0;
}

.zoom-container {
    position: relative;
    width: 100%;
    height: auto;
    min-height: 200px; /* Add minimum height */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Image container positioning (same as zone editor) */
#image-container,
.img-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* Default aspect ratio 16:9, will be updated by JS */
    overflow: hidden;
}

#parking-image {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: contain;
    max-height: 80vh;
    display: block;
    margin: 0;
    padding: 0;
    z-index: 1;
    transform-origin: center center; /* Add transform origin */
    transition: transform 0.1s ease-out; /* Smooth transform transitions */
}

#parking-slots-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 2;
}

.parking-slots-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 2;
}

/* Individual parking slot styles */
.parking-slot {
    position: absolute;
    opacity: 0.7;
    pointer-events: all;
    z-index: 2;
}

.parking-slot.free {
    background-color: rgba(40, 167, 69, 0.3);
    border: 2px solid rgba(40, 167, 69, 0.8);
}

.parking-slot.occupied {
    background-color: rgba(220, 53, 69, 0.3);
    border: 2px solid rgba(220, 53, 69, 0.8);
}

.parking-slot .label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 0.9rem;
    z-index: 3;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Event log styles */
.event-log-card {
    height: 100%;
    margin-bottom: 0;
    border-radius: 12px;
    overflow: hidden;
}

.event-log-card .table-responsive {
    max-height: calc(100vh - 500px);
    overflow-y: auto;
}

.event-log-card .table {
    margin: 0;
    font-size: 0.875rem;
    border-collapse: collapse;
}

.event-log-card .table th {
    background-color: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 1;
    font-weight: 600;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.event-log-card .table td {
    vertical-align: middle;
    padding: 0.5rem 1rem;
    border: none;
}

.event-log-card .table tr {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.event-log-card .table tr:last-child {
    border-bottom: none;
}

/* Animation for slot status changes */
@keyframes slot-status-change {
    0% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.3); opacity: 0.9; }
    100% { transform: scale(1); opacity: 0.7; }
}

.parking-slot.status-changed {
    animation: slot-status-change 1000ms cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
    z-index: 4;
    pointer-events: none;
    box-shadow: 0 0 25px currentColor;
}

/* Event status badges */
.status-change {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-change.to-occupied {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.2);
}

.status-change.to-free {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
    border: 1px solid rgba(25, 135, 84, 0.2);
}

.status-change.to-neutral {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.2);
}

/* Source badges in event log */
.source-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.source-badge.source-detection {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
    border: 1px solid rgba(13, 110, 253, 0.2);
}

.source-badge.source-manual {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.2);
}

.source-badge.source-system {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
    border: 1px solid rgba(25, 135, 84, 0.2);
}

.source-badge.source-stream {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.2);
}