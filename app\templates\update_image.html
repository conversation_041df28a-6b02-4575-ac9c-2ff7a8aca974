{% extends "base_layout.html" %}

{% block title %}Update Image - {{ lot.name }}{% endblock %}

{% block extra_css %}
<link href="/static/css/dashboard.css" rel="stylesheet">
<link href="/static/css/viewpoint-selector.css" rel="stylesheet">
<style>
    .current-image-container {
        min-height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: 0.375rem;
    }
    .current-image-container img {
        max-height: 400px;
        max-width: 100%;
        object-fit: contain;
    }
    .no-image-placeholder {
        text-align: center;
        color: #6c757d;
    }

    /* Fix dropdown visibility issues by preventing transforms on parent containers */

    /* Disable card hover transforms that interfere with dropdown positioning */
    .card:has(viewpoint-selector) {
        transform: none !important;
        transition: none !important;
    }

    .card:has(viewpoint-selector):hover {
        transform: none !important;
        transition: none !important;
    }

    /* Fallback for browsers that don't support :has() */
    .card-body:has(viewpoint-selector) {
        transform: none !important;
        transition: none !important;
    }

    /* Disable fluent effects on cards containing viewpoint selector */
    .card:has(viewpoint-selector) .fluent-hover-effect {
        display: none !important;
    }

    /* Ensure viewpoint selector container doesn't get transforms */
    viewpoint-selector {
        transform: none !important;
        transition: none !important;
        position: relative;
        z-index: 1000;
    }

    /* Disable all transforms within viewpoint selector */
    viewpoint-selector * {
        transform: none !important;
        transition: none !important;
    }

    viewpoint-selector *:hover {
        transform: none !important;
        transition: none !important;
    }

    /* Create isolation for the dropdown */
    viewpoint-selector {
        isolation: isolate;
    }

    /* Additional fallback rules for specific card classes */
    .card.mb-4:hover {
        transform: none !important;
    }

    /* Disable any image container effects in this page */
    .card-img-container {
        transform: none !important;
        transition: none !important;
    }

    .card-img-container:hover {
        transform: none !important;
        transition: none !important;
    }

    .card-img-container::after {
        display: none !important;
    }

    /* Prevent fluent effects from being initialized on this page */
    .fluent-hover-effect {
        display: none !important;
    }

    /* Ensure no transforms are applied to any cards on this page */
    body .card {
        transform: none !important;
        transition: none !important;
    }

    body .card:hover {
        transform: none !important;
        transition: none !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Update Image for {{ lot.name }}</h1>
    <a href="/dashboard/{{ lot_id }}" class="btn btn-secondary">Back to Dashboard</a>
</div>

<!-- Viewpoint Selection -->
<div class="card mb-4">
    <div class="card-header">
        <h5>Select Viewpoint to Update</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <label class="form-label">Choose Viewpoint:</label>
                <viewpoint-selector
                    id="viewpoint-selector"
                    lot-id="{{ lot_id }}">
                </viewpoint-selector>

                <!-- Debug info -->
                <div id="debug-info" style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                    <strong>Debug Info:</strong><br>
                    Lot ID: {{ lot_id }}<br>
                    <span id="debug-status">Loading...</span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    <strong>Select a viewpoint</strong> to update its image. The default viewpoint image is also used as the main lot image.
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 id="current-image-title">Current Image - Main Lot</h5>
            </div>
            <div class="card-body">
                <div class="current-image-container" id="current-image-container">
                    <img src="/static/{{ lot.image_path }}"
                         class="img-fluid"
                         alt="{{ lot.name }}"
                         id="current-image">
                </div>
            </div>
            <div class="card-footer">
                <small class="text-muted" id="image-info">
                    This parking lot has {{ lot.slots|length }} configured slot(s).
                    Updating the main image will maintain all existing slot positions.
                </small>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 id="upload-form-title">Upload New Image - Main Lot</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="upload-form">
                    <!-- Hidden field for selected viewpoint -->
                    <input type="hidden" id="viewpoint-id" name="viewpoint_id" value="">

                    <div class="mb-3">
                        <label for="file" class="form-label">Select New Image File</label>
                        <input type="file" class="form-control" id="file" name="file" accept="image/jpeg,image/png" required>
                        <div class="form-text">Upload a JPG or PNG file.</div>
                    </div>

                    <div class="alert alert-warning" id="warning-message">
                        <strong>Warning:</strong> This action will replace the main parking lot image.
                        All configured parking slots will be maintained, but you may need to
                        adjust them if the camera angle or position has changed.
                    </div>

                    <div class="text-end">
                        <button type="submit" class="btn btn-primary" id="submit-button">
                            Update Main Image
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Include viewpoint selector component -->
<script src="/static/js/viewpoint-selector.js"></script>
<script src="/static/js/register_components.js"></script>

<!-- Prevent fluent effects from interfering with dropdown -->
<script>
// Override fluent effect initialization for this page
if (typeof FluentEffect !== 'undefined') {
    // Disable the automatic initialization
    const originalAddEventListener = document.addEventListener;
    document.addEventListener = function(type, listener, options) {
        if (type === 'DOMContentLoaded' && listener.toString().includes('card-img-container')) {
            // Skip fluent effect initialization
            console.log('Skipping fluent effect initialization on update image page');
            return;
        }
        return originalAddEventListener.call(this, type, listener, options);
    };
}
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const lotId = '{{ lot_id }}';
    let viewpoints = [];
    let selectedViewpointId = '';

    // Get references to form elements
    const viewpointIdInput = document.getElementById('viewpoint-id');
    const currentImageTitle = document.getElementById('current-image-title');
    const uploadFormTitle = document.getElementById('upload-form-title');
    const warningMessage = document.getElementById('warning-message');
    const submitButton = document.getElementById('submit-button');
    const currentImageContainer = document.getElementById('current-image-container');
    const imageInfo = document.getElementById('image-info');
    const viewpointSelector = document.getElementById('viewpoint-selector');

    console.log('Image Upload: Page loaded, viewpoint selector element:', viewpointSelector);
    console.log('Image Upload: Lot ID:', lotId);

    // Check if viewpoint selector is properly initialized
    if (viewpointSelector) {
        console.log('Image Upload: Viewpoint selector found, lot-id attribute:', viewpointSelector.getAttribute('lot-id'));

        // Add debugging for component lifecycle
        setTimeout(() => {
            console.log('Image Upload: Checking viewpoint selector after 1 second...');
            console.log('Image Upload: Shadow root exists:', !!viewpointSelector.shadowRoot);
            if (viewpointSelector.shadowRoot) {
                const button = viewpointSelector.shadowRoot.querySelector('.selector-button');
                const dropdown = viewpointSelector.shadowRoot.querySelector('.dropdown');
                console.log('Image Upload: Button found:', !!button);
                console.log('Image Upload: Dropdown found:', !!dropdown);
                if (dropdown) {
                    console.log('Image Upload: Dropdown content:', dropdown.innerHTML);
                }
            }
        }, 1000);
    } else {
        console.error('Image Upload: Viewpoint selector element not found!');
    }

    // Prevent fluent effects from being applied to cards containing viewpoint selector
    // This ensures dropdown positioning isn't affected by hover transforms
    const cardsWithViewpointSelector = document.querySelectorAll('.card:has(viewpoint-selector), .card-body:has(viewpoint-selector)');
    cardsWithViewpointSelector.forEach(card => {
        // Remove any existing fluent effects
        const fluentEffect = card.querySelector('.fluent-hover-effect');
        if (fluentEffect) {
            fluentEffect.remove();
        }

        // Add a class to prevent future fluent effects
        card.classList.add('no-fluent-effects');
    });

    // Fallback: Remove fluent effects from any card that contains viewpoint-selector
    setTimeout(() => {
        document.querySelectorAll('.card').forEach(card => {
            if (card.querySelector('viewpoint-selector')) {
                const fluentEffect = card.querySelector('.fluent-hover-effect');
                if (fluentEffect) {
                    fluentEffect.remove();
                }
                card.style.transform = 'none';
                card.style.transition = 'none';
            }
        });
    }, 100);

    // Listen for viewpoint changes from the selector component
    viewpointSelector.addEventListener('viewpoint-changed', function(event) {
        const { viewpointId, viewpoint } = event.detail;
        console.log('Image Upload: Viewpoint changed to:', viewpoint);

        selectedViewpointId = viewpointId;

        // If we have the viewpoint data directly from the event, use it
        if (viewpoint) {
            // Update the viewpoints array if this viewpoint isn't in it yet
            const existingIndex = viewpoints.findIndex(v => v.id === viewpointId);
            if (existingIndex === -1) {
                viewpoints.push(viewpoint);
            } else {
                viewpoints[existingIndex] = viewpoint;
            }
        }

        updateFormAndDisplay();
    });

    // Listen for viewpoints loaded event to get the viewpoints data
    viewpointSelector.addEventListener('viewpoints-loaded', function(event) {
        viewpoints = event.detail.viewpoints || [];
        console.log('Image Upload: Viewpoints loaded:', viewpoints);

        // If we already have a selected viewpoint, update the display
        if (selectedViewpointId) {
            updateFormAndDisplay();
        }
    });

    function updateFormAndDisplay() {
        console.log('Image Upload: updateFormAndDisplay called with:', {
            selectedViewpointId,
            viewpointsCount: viewpoints.length,
            viewpoints: viewpoints.map(v => ({ id: v.id, name: v.name }))
        });

        // Update hidden form field
        viewpointIdInput.value = selectedViewpointId;

        if (!selectedViewpointId) {
            console.log('Image Upload: No viewpoint selected yet');
            return;
        }

        // Find the selected viewpoint
        const viewpoint = viewpoints.find(v => v.id === selectedViewpointId);
        if (viewpoint) {
            console.log('Image Upload: Found viewpoint:', viewpoint);

            currentImageTitle.textContent = `Current Image - ${viewpoint.name}`;
            uploadFormTitle.textContent = `Upload New Image - ${viewpoint.name}`;
            submitButton.textContent = `Update ${viewpoint.name} Image`;

            warningMessage.innerHTML = `
                <strong>Warning:</strong> This action will replace the image for viewpoint "${viewpoint.name}".
                ${viewpoint.is_default ? 'This is the default viewpoint, so it will also update the main lot image.' : ''}
                Slots associated with this viewpoint may need adjustment if the camera angle changes.
            `;

            imageInfo.innerHTML = `
                This viewpoint has slots associated with it.
                Updating the image will maintain existing slot positions for this viewpoint.
            `;

            // Show viewpoint image or placeholder
            if (viewpoint.image_path) {
                console.log('Image Upload: Updating image to:', viewpoint.image_path);
                currentImageContainer.innerHTML = `
                    <img src="/static/${viewpoint.image_path}"
                         class="img-fluid"
                         alt="${viewpoint.name}">
                `;
            } else {
                console.log('Image Upload: No image path for viewpoint, showing placeholder');
                currentImageContainer.innerHTML = `
                    <div class="no-image-placeholder">
                        <i class="bi bi-camera" style="font-size: 3rem; color: #dee2e6;"></i>
                        <p class="mt-2">No image uploaded for this viewpoint</p>
                    </div>
                `;
            }
        } else {
            console.warn('Image Upload: Viewpoint not found in viewpoints array:', {
                selectedViewpointId,
                availableViewpoints: viewpoints.map(v => v.id)
            });
        }
    }
});
</script>
{% endblock %}
