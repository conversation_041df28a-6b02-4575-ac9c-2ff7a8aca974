"""
Tests for slot grouping logic and viewport independence
"""
import pytest
import uuid

from app.src.models.parking import <PERSON>ing<PERSON>ot, ParkingSlot


class TestSlotGroupingLogic:
    """Tests for the slot grouping logic that ensures viewport independence"""

    def test_slot_grouping_basic_scenario(self):
        """Test basic slot grouping with duplicate names across viewpoints"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Test Lot")
        
        # Real-world scenario: same slot names in different viewpoints
        slots = [
            ParkingSlot(id="slot1", slot_name="643", is_occupied=False, viewpoint_id="default"),
            ParkingSlot(id="slot2", slot_name="643", is_occupied=False, viewpoint_id="alt"),
            ParkingSlot(id="slot3", slot_name="639", is_occupied=False, viewpoint_id="default"),
        ]
        
        for slot in slots:
            lot.slots.append(slot)
        
        # Act
        stats = lot.get_grouped_slot_stats()
        
        # Assert
        assert stats['total_groups'] == 2  # "643" and "639"
        assert stats['occupied_groups'] == 0
        assert stats['available_groups'] == 2
        assert stats['occupancy_rate'] == 0.0
        
        # Verify group details
        assert stats['group_details']['643']['instances'] == 2
        assert stats['group_details']['639']['instances'] == 1
        assert set(stats['group_details']['643']['viewpoints']) == {"default", "alt"}
        assert stats['group_details']['639']['viewpoints'] == ["default"]

    def test_slot_grouping_any_occupied_rule(self):
        """Test that a group is considered occupied if ANY instance is occupied"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Test Lot")
        
        slots = [
            # Group "A1" - one occupied, one free -> group should be occupied
            ParkingSlot(id="slot1", slot_name="A1", is_occupied=True, viewpoint_id="vp1"),
            ParkingSlot(id="slot2", slot_name="A1", is_occupied=False, viewpoint_id="vp2"),
            # Group "A2" - all free -> group should be free
            ParkingSlot(id="slot3", slot_name="A2", is_occupied=False, viewpoint_id="vp1"),
            ParkingSlot(id="slot4", slot_name="A2", is_occupied=False, viewpoint_id="vp2"),
            # Group "A3" - all occupied -> group should be occupied
            ParkingSlot(id="slot5", slot_name="A3", is_occupied=True, viewpoint_id="vp1"),
            ParkingSlot(id="slot6", slot_name="A3", is_occupied=True, viewpoint_id="vp2"),
        ]
        
        for slot in slots:
            lot.slots.append(slot)
        
        # Act
        stats = lot.get_grouped_slot_stats()
        
        # Assert
        assert stats['total_groups'] == 3
        assert stats['occupied_groups'] == 2  # A1 and A3
        assert stats['available_groups'] == 1  # A2
        assert stats['occupancy_rate'] == pytest.approx(66.7, abs=0.1)
        
        # Verify specific group statuses
        assert stats['group_details']['A1']['is_occupied'] is True  # Any instance occupied
        assert stats['group_details']['A2']['is_occupied'] is False  # All instances free
        assert stats['group_details']['A3']['is_occupied'] is True  # All instances occupied

    def test_slot_grouping_viewport_independence(self):
        """Test that grouping results are independent of viewpoint configuration"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Multi-Viewport Lot")
        
        # Complex scenario with varying viewpoint distributions
        slots = [
            # "SHARED" appears in 4 viewpoints
            ParkingSlot(id="slot1", slot_name="SHARED", is_occupied=True, viewpoint_id="front"),
            ParkingSlot(id="slot2", slot_name="SHARED", is_occupied=False, viewpoint_id="back"),
            ParkingSlot(id="slot3", slot_name="SHARED", is_occupied=False, viewpoint_id="left"),
            ParkingSlot(id="slot4", slot_name="SHARED", is_occupied=False, viewpoint_id="right"),
            # "PARTIAL" appears in 2 viewpoints
            ParkingSlot(id="slot5", slot_name="PARTIAL", is_occupied=False, viewpoint_id="front"),
            ParkingSlot(id="slot6", slot_name="PARTIAL", is_occupied=False, viewpoint_id="back"),
            # "SINGLE" appears in 1 viewpoint only
            ParkingSlot(id="slot7", slot_name="SINGLE", is_occupied=True, viewpoint_id="front"),
        ]
        
        for slot in slots:
            lot.slots.append(slot)
        
        # Act
        stats = lot.get_grouped_slot_stats()
        
        # Assert
        assert stats['total_groups'] == 3
        assert stats['occupied_groups'] == 2  # SHARED and SINGLE
        assert stats['available_groups'] == 1  # PARTIAL
        
        # Verify viewpoint tracking
        shared_viewpoints = set(stats['group_details']['SHARED']['viewpoints'])
        partial_viewpoints = set(stats['group_details']['PARTIAL']['viewpoints'])
        single_viewpoints = set(stats['group_details']['SINGLE']['viewpoints'])
        
        assert shared_viewpoints == {"front", "back", "left", "right"}
        assert partial_viewpoints == {"front", "back"}
        assert single_viewpoints == {"front"}

    def test_slot_grouping_edge_cases(self):
        """Test edge cases in slot grouping"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Edge Case Lot")
        
        slots = [
            # Same slot name with None viewpoint_id
            ParkingSlot(id="slot1", slot_name="NO_VP", is_occupied=False, viewpoint_id=None),
            ParkingSlot(id="slot2", slot_name="NO_VP", is_occupied=True, viewpoint_id="vp1"),
            # Empty string slot name (edge case)
            ParkingSlot(id="slot3", slot_name="", is_occupied=False, viewpoint_id="vp1"),
            # Very long slot name
            ParkingSlot(id="slot4", slot_name="A" * 100, is_occupied=True, viewpoint_id="vp1"),
        ]
        
        for slot in slots:
            lot.slots.append(slot)
        
        # Act
        stats = lot.get_grouped_slot_stats()
        
        # Assert
        assert stats['total_groups'] == 3  # NO_VP, "", and "A"*100
        assert stats['occupied_groups'] == 2  # NO_VP and "A"*100
        assert stats['available_groups'] == 1  # ""
        
        # Verify None viewpoint handling
        no_vp_viewpoints = stats['group_details']['NO_VP']['viewpoints']
        assert None in no_vp_viewpoints or "vp1" in no_vp_viewpoints

    def test_slot_grouping_performance_with_many_duplicates(self):
        """Test slot grouping performance with many duplicate slot names"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Large Lot")
        
        # Create many slots with the same name across many viewpoints
        slots = []
        for i in range(100):  # 100 viewpoints
            for j in range(10):  # 10 slot names per viewpoint
                slot_name = f"SLOT_{j}"
                is_occupied = (i + j) % 3 == 0  # Some pattern of occupancy
                viewpoint_id = f"viewpoint_{i}"
                
                slot = ParkingSlot(
                    id=f"slot_{i}_{j}",
                    slot_name=slot_name,
                    is_occupied=is_occupied,
                    viewpoint_id=viewpoint_id
                )
                slots.append(slot)
        
        for slot in slots:
            lot.slots.append(slot)
        
        # Act
        stats = lot.get_grouped_slot_stats()
        
        # Assert
        assert stats['total_groups'] == 10  # SLOT_0 through SLOT_9
        assert len(stats['group_details']) == 10
        
        # Each group should have 100 instances (one per viewpoint)
        for slot_name, details in stats['group_details'].items():
            assert details['instances'] == 100
            assert len(details['viewpoints']) == 100

    def test_slot_grouping_consistency_with_traditional_stats(self):
        """Test that grouped stats are consistent with traditional slot counting"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Consistency Test Lot")
        
        # Create a scenario where traditional and grouped stats should differ
        slots = [
            # Traditional counting would count these as 3 slots
            # Grouped counting should count as 2 groups
            ParkingSlot(id="slot1", slot_name="A1", is_occupied=True, viewpoint_id="vp1"),
            ParkingSlot(id="slot2", slot_name="A1", is_occupied=False, viewpoint_id="vp2"),
            ParkingSlot(id="slot3", slot_name="A2", is_occupied=False, viewpoint_id="vp1"),
        ]
        
        for slot in slots:
            lot.slots.append(slot)
        
        # Act
        traditional_total = len(lot.slots)
        traditional_occupied = lot.get_busy_slot_count()
        traditional_free = lot.get_free_slot_count()
        
        grouped_stats = lot.get_grouped_slot_stats()
        grouped_total = grouped_stats['total_groups']
        grouped_occupied = grouped_stats['occupied_groups']
        grouped_free = grouped_stats['available_groups']
        
        # Assert - Traditional vs Grouped should differ
        assert traditional_total == 3  # 3 individual slots
        assert traditional_occupied == 1  # 1 individual slot occupied
        assert traditional_free == 2  # 2 individual slots free
        
        assert grouped_total == 2  # 2 slot groups
        assert grouped_occupied == 1  # 1 group occupied (A1)
        assert grouped_free == 1  # 1 group free (A2)
        
        # The key difference: traditional counts instances, grouped counts unique names
        assert traditional_total != grouped_total
        assert traditional_free != grouped_free

    def test_slot_grouping_mathematical_properties(self):
        """Test mathematical properties of slot grouping"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Math Test Lot")
        
        slots = [
            ParkingSlot(id="slot1", slot_name="A", is_occupied=True, viewpoint_id="vp1"),
            ParkingSlot(id="slot2", slot_name="A", is_occupied=False, viewpoint_id="vp2"),
            ParkingSlot(id="slot3", slot_name="B", is_occupied=False, viewpoint_id="vp1"),
            ParkingSlot(id="slot4", slot_name="C", is_occupied=True, viewpoint_id="vp1"),
        ]
        
        for slot in slots:
            lot.slots.append(slot)
        
        # Act
        stats = lot.get_grouped_slot_stats()
        
        # Assert mathematical properties
        # Total = Occupied + Available
        assert stats['total_groups'] == stats['occupied_groups'] + stats['available_groups']
        
        # Occupancy rate calculation
        expected_rate = (stats['occupied_groups'] / stats['total_groups'] * 100) if stats['total_groups'] > 0 else 0
        assert stats['occupancy_rate'] == pytest.approx(expected_rate, abs=0.1)
        
        # Group details count should match total
        assert len(stats['group_details']) == stats['total_groups']
        
        # Sum of instances should equal total slots
        total_instances = sum(details['instances'] for details in stats['group_details'].values())
        assert total_instances == len(lot.slots)
