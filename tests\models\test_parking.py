"""
Unit tests for the parking domain models
"""
import pytest
from unittest.mock import Mock, patch
import uuid
from datetime import datetime

from app.src.models.parking import Parking<PERSON>ot, ParkingSlot
from app.src.models.geometry import Polygon, Point
from app.src.models.detection import DetectionEngineConfig
from app.src.models.events import (
    ParkingEvent, BaseEvent, ParkingLotRenamed, ParkingLotImageUpdated,
    ParkingSlotAdded, ParkingSlotRemoved, ParkingSlotZoneUpdated,
    ParkingSlotThresholdUpdated, ParkingSlotOccupancyChanged,
    DetectionConfigCreated, DetectionConfigUpdated, DetectionConfigDeleted
)

class TestParkingSlot:
    """Tests for the ParkingSlot domain model"""
    
    def test_initialization(self):
        """Test ParkingSlot initialization with default values"""
        # Arrange
        slot_id = str(uuid.uuid4())
        slot_name = "Test Slot"
        
        # Act
        slot = ParkingSlot(id=slot_id, slot_name=slot_name)
        
        # Assert
        assert slot.id == slot_id
        assert slot.slot_name == slot_name
        assert slot.detection_zone is None
        assert slot.is_occupied is False
        assert slot.overlap_threshold == 0.3
    
    def test_initialization_with_values(self):
        """Test ParkingSlot initialization with provided values"""
        # Arrange
        slot_id = str(uuid.uuid4())
        slot_name = "Test Slot A1"
        points = [
            Point(x=10, y=10), Point(x=20, y=10), 
            Point(x=20, y=20), Point(x=10, y=20)
        ]
        zone = Polygon(points=points)
        
        # Act
        slot = ParkingSlot(
            id=slot_id,
            slot_name=slot_name,
            detection_zone=zone,
            is_occupied=True,
            overlap_threshold=0.5
        )
        
        # Assert
        assert slot.id == slot_id
        assert slot.slot_name == slot_name
        assert slot.detection_zone is zone
        assert slot.is_occupied is True
        assert slot.overlap_threshold == 0.5
    
    def test_to_dict(self):
        """Test conversion of ParkingSlot to dictionary"""
        # Arrange
        slot_id = str(uuid.uuid4())
        slot_name = "Test Slot B2"
        points = [
            Point(x=10, y=10), Point(x=20, y=10), 
            Point(x=20, y=20), Point(x=10, y=20)
        ]
        zone = Polygon(points=points)
        slot = ParkingSlot(
            id=slot_id,
            slot_name=slot_name,
            detection_zone=zone,
            is_occupied=True,
            overlap_threshold=0.5
        )
        
        # Act
        result = slot.to_dict()
        
        # Assert
        assert result["id"] == slot_id
        assert result["slot_name"] == slot_name
        assert result["is_occupied"] is True
        assert result["overlap_threshold"] == 0.5
        assert "detection_zone" in result
        assert "points" in result["detection_zone"]
        assert len(result["detection_zone"]["points"]) == 4
    
    def test_to_dict_without_zone(self):
        """Test conversion of ParkingSlot without detection zone to dictionary"""
        # Arrange
        slot_id = str(uuid.uuid4())
        slot_name = "Test Slot C3"
        slot = ParkingSlot(id=slot_id, slot_name=slot_name)
        
        # Act
        result = slot.to_dict()
        
        # Assert
        assert result["id"] == slot_id
        assert result["slot_name"] == slot_name
        assert result["detection_zone"] is None
        assert result["is_occupied"] is False
        assert result["overlap_threshold"] == 0.3
    
    def test_from_dict(self):
        """Test creating ParkingSlot from dictionary"""
        # Arrange
        slot_id = str(uuid.uuid4())
        slot_dict = {
            "id": slot_id,
            "slot_name": "Slot D4",
            "detection_zone": {
                "points": [
                    {"x": 10, "y": 10},
                    {"x": 20, "y": 10},
                    {"x": 20, "y": 20},
                    {"x": 10, "y": 20}
                ]
            },
            "is_occupied": True,
            "overlap_threshold": 0.5
        }
        
        # Act
        slot = ParkingSlot.from_dict(slot_dict)
        
        # Assert
        assert slot.id == slot_id
        assert slot.slot_name == "Slot D4"
        assert slot.is_occupied is True
        assert slot.overlap_threshold == 0.5
        assert slot.detection_zone is not None
        assert len(slot.detection_zone.points) == 4
        assert slot.detection_zone.points[0].x == 10
        assert slot.detection_zone.points[0].y == 10
    
    def test_from_dict_minimal(self):
        """Test creating ParkingSlot from minimal dictionary"""
        # Arrange
        slot_id = str(uuid.uuid4())
        slot_dict = {"id": slot_id, "slot_name": "Minimal Slot"}
        
        # Act
        slot = ParkingSlot.from_dict(slot_dict)
        
        # Assert
        assert slot.id == slot_id
        assert slot.slot_name == "Minimal Slot"
        assert slot.detection_zone is None
        assert slot.is_occupied is False
        assert slot.overlap_threshold == 0.3


class TestParkingLot:
    """Tests for the ParkingLot domain model"""
    
    def test_initialization(self):
        """Test ParkingLot initialization with minimal values"""
        # Arrange
        lot_id = str(uuid.uuid4())
        name = "Test Parking Lot"
        
        # Act
        lot = ParkingLot(id=lot_id, name=name)
        
        # Assert
        assert lot.id == lot_id
        assert lot.name == name
        assert lot.image_path is None
        assert lot.slots == []
        assert lot.detection_config is None
        assert lot.events == []
        assert lot.viewpoints == []
        assert lot.default_viewpoint_id is None
    
    def test_initialization_with_slots(self):
        """Test ParkingLot initialization with slots"""
        # Arrange
        lot_id = str(uuid.uuid4())
        name = "Test Parking Lot"
        slots = [
            ParkingSlot(id=str(uuid.uuid4()), slot_name="Slot 1"),
            ParkingSlot(id=str(uuid.uuid4()), slot_name="Slot 2")
        ]
        
        # Act
        lot = ParkingLot(id=lot_id, name=name, slots=slots)
        
        # Assert
        assert lot.id == lot_id
        assert lot.name == name
        assert lot.slots == slots
        assert len(lot.slots) == 2
    
    @patch('app.src.models.parking.emit_parking_event')
    def test_add_event(self, mock_emit):
        """Test adding an event to a parking lot"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Test Lot")
        event_type = "TEST_EVENT"
        description = "Test event description"
        
        # Act
        event = lot.add_event(event_type, description)
        
        # Assert
        assert len(lot.events) == 1
        assert lot.events[0] is event
        assert event.lot_id == lot.id
        assert event.event_type == event_type
        assert event.description == description
        assert event.timestamp is not None
        mock_emit.assert_called_once_with(lot.id, event)
    
    def test_apply_parking_lot_renamed(self):
        """Test applying ParkingLotRenamed event"""
        # Arrange
        lot_id = str(uuid.uuid4())
        lot = ParkingLot(id=lot_id, name="Old Name")
        new_name = "New Name"
        event = ParkingLotRenamed(
            lot_id=lot_id,
            payload={"new_name": new_name}
        )
        
        # Act
        lot.apply(event)
        
        # Assert
        assert lot.name == new_name
    
    def test_apply_parking_lot_image_updated(self):
        """Test applying ParkingLotImageUpdated event"""
        # Arrange
        lot_id = str(uuid.uuid4())
        lot = ParkingLot(id=lot_id, name="Test Lot")
        new_image_path = "/path/to/new_image.jpg"
        event = ParkingLotImageUpdated(
            lot_id=lot_id,
            payload={"new_image_path": new_image_path}
        )
        
        # Act
        lot.apply(event)
        
        # Assert
        assert lot.image_path == new_image_path
    
    def test_apply_parking_slot_added(self):
        """Test applying ParkingSlotAdded event"""
        # Arrange
        lot_id = str(uuid.uuid4())
        lot = ParkingLot(id=lot_id, name="Test Lot")
        slot_id = str(uuid.uuid4())
        slot_name = "Added Slot"
        event = ParkingSlotAdded(
            lot_id=lot_id,
            payload={
                "slot_id": slot_id,
                "slot_name": slot_name,
                "overlap_threshold": 0.4,
                "zone_points": [
                    {"x": 10, "y": 10},
                    {"x": 20, "y": 10},
                    {"x": 20, "y": 20},
                    {"x": 10, "y": 20}
                ]
            }
        )
        
        # Act
        lot.apply(event)
        
        # Assert
        assert len(lot.slots) == 1
        slot = lot.slots[0]
        assert slot.id == slot_id
        assert slot.slot_name == slot_name
        assert slot.overlap_threshold == 0.4
        assert slot.detection_zone is not None
        assert len(slot.detection_zone.points) == 4
    
    def test_apply_parking_slot_removed(self):
        """Test applying ParkingSlotRemoved event"""
        # Arrange
        lot_id = str(uuid.uuid4())
        slot1_id = str(uuid.uuid4())
        slot2_id = str(uuid.uuid4())
        lot = ParkingLot(
            id=lot_id, 
            name="Test Lot",
            slots=[
                ParkingSlot(id=slot1_id, slot_name="Slot 1"),
                ParkingSlot(id=slot2_id, slot_name="Slot 2")
            ]
        )
        event = ParkingSlotRemoved(
            lot_id=lot_id,
            payload={"slot_id": slot1_id}
        )
        
        # Act
        lot.apply(event)
        
        # Assert
        assert len(lot.slots) == 1
        assert lot.slots[0].id == slot2_id
    
    def test_apply_parking_slot_zone_updated(self):
        """Test applying ParkingSlotZoneUpdated event"""
        # Arrange
        lot_id = str(uuid.uuid4())
        slot_id = str(uuid.uuid4())
        lot = ParkingLot(
            id=lot_id, 
            name="Test Lot",
            slots=[ParkingSlot(id=slot_id, slot_name="Update Zone Slot")]
        )
        event = ParkingSlotZoneUpdated(
            lot_id=lot_id,
            payload={
                "slot_id": slot_id,
                "new_zone_points": [
                    {"x": 15, "y": 15},
                    {"x": 25, "y": 15},
                    {"x": 25, "y": 25},
                    {"x": 15, "y": 25}
                ]
            }
        )
        
        # Act
        lot.apply(event)
        
        # Assert
        assert lot.slots[0].detection_zone is not None
        assert lot.slots[0].detection_zone.points[0].x == 15
        assert lot.slots[0].detection_zone.points[0].y == 15
    
    def test_apply_parking_slot_threshold_updated(self):
        """Test applying ParkingSlotThresholdUpdated event"""
        # Arrange
        lot_id = str(uuid.uuid4())
        slot_id = str(uuid.uuid4())
        lot = ParkingLot(
            id=lot_id, 
            name="Test Lot",
            slots=[ParkingSlot(id=slot_id, slot_name="Threshold Slot", overlap_threshold=0.3)]
        )
        event = ParkingSlotThresholdUpdated(
            lot_id=lot_id,
            payload={
                "slot_id": slot_id,
                "new_overlap_threshold": 0.6
            }
        )
        
        # Act
        lot.apply(event)
          # Assert
        assert lot.slots[0].overlap_threshold == 0.6

    def test_apply_parking_slot_occupancy_changed(self):
        """Test applying ParkingSlotOccupancyChanged event"""
        # Arrange
        lot_id = str(uuid.uuid4())
        slot_id = str(uuid.uuid4())
        slot_name = "Occupancy Slot"
        lot = ParkingLot(
            id=lot_id,
            name="Test Lot",
            slots=[ParkingSlot(id=slot_id, slot_name=slot_name)]
        )
        event = ParkingSlotOccupancyChanged(
            lot_id=lot_id,
            payload={
                "slot_id": slot_id,  # Fixed: Use slot.id as the identifier
                "is_occupied": True
            }
        )
        
        # Act
        lot.apply(event)
        
        # Assert
        assert lot.slots[0].is_occupied is True
    
    def test_apply_detection_config_created(self):
        """Test applying DetectionConfigCreated event"""
        # Arrange
        lot_id = str(uuid.uuid4())
        lot = ParkingLot(id=lot_id, name="Test Lot")
        event = DetectionConfigCreated(
            lot_id=lot_id,
            payload={
                "engine_type": "yolo",
                "is_enabled": True,
                "update_interval": 30,
                "parameters": {"threshold": 0.5}
            }
        )
        
        # Act
        lot.apply(event)
        
        # Assert
        assert lot.detection_config is not None
        assert lot.detection_config.engine_type == "yolo"
        assert lot.detection_config.is_enabled is True
        assert lot.detection_config.update_interval == 30
        assert lot.detection_config.parameters == {"threshold": 0.5}
    
    def test_apply_detection_config_updated(self):
        """Test applying DetectionConfigUpdated event"""
        # Arrange
        lot_id = str(uuid.uuid4())
        lot = ParkingLot(id=lot_id, name="Test Lot")
        lot.detection_config = DetectionEngineConfig(
            engine_type="mock",
            is_enabled=False,
            update_interval=60
        )
        event = DetectionConfigUpdated(
            lot_id=lot_id,
            payload={
                "engine_type": "yolo",
                "is_enabled": True,
                "update_interval": 30,
                "parameters": {"threshold": 0.7}
            }
        )
        
        # Act
        lot.apply(event)
        
        # Assert
        assert lot.detection_config.engine_type == "yolo"
        assert lot.detection_config.is_enabled is True
        assert lot.detection_config.update_interval == 30
        assert lot.detection_config.parameters == {"threshold": 0.7}
    
    def test_apply_detection_config_deleted(self):
        """Test applying DetectionConfigDeleted event"""
        # Arrange
        lot_id = str(uuid.uuid4())
        lot = ParkingLot(id=lot_id, name="Test Lot")
        lot.detection_config = DetectionEngineConfig(
            engine_type="yolo",
            is_enabled=True,
            update_interval=30
        )
        event = DetectionConfigDeleted(
            lot_id=lot_id,
            payload={}
        )
        
        # Act
        lot.apply(event)
        
        # Assert
        assert lot.detection_config is None
    
    def test_get_events_pagination(self):
        """Test pagination of events"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Test Lot")
        # Add some test events manually (since add_event includes emit call)
        for i in range(25):
            event = ParkingEvent(
                lot_id=lot.id,
                event_type=f"TEST_EVENT_{i}",
                description=f"Test event {i}",
                timestamp=datetime.now()
            )
            lot.events.append(event)
        
        # Act
        events, total = lot.get_events(page=1, per_page=10)
        
        # Assert
        assert len(events) == 10
        assert total == 25
        # Events should be sorted by timestamp, reverse order (newest first)
        # Assuming timestamp ordering works correctly
    
    def test_get_available_slots(self):
        """Test getting available slots"""
        # Arrange
        lot = ParkingLot(
            id=str(uuid.uuid4()),
            name="Test Lot",
            slots=[
                ParkingSlot(id=str(uuid.uuid4()), slot_name="Slot 1", is_occupied=False),
                ParkingSlot(id=str(uuid.uuid4()), slot_name="Slot 2", is_occupied=True),
                ParkingSlot(id=str(uuid.uuid4()), slot_name="Slot 3", is_occupied=False)
            ]
        )
        
        # Act
        available = lot.get_available_slots()
        
        # Assert
        assert len(available) == 2
        assert all(not slot.is_occupied for slot in available)
    
    def test_get_occupied_slots(self):
        """Test getting occupied slots"""
        # Arrange
        lot = ParkingLot(
            id=str(uuid.uuid4()),
            name="Test Lot",
            slots=[
                ParkingSlot(id=str(uuid.uuid4()), slot_name="Slot 1", is_occupied=False),
                ParkingSlot(id=str(uuid.uuid4()), slot_name="Slot 2", is_occupied=True),
                ParkingSlot(id=str(uuid.uuid4()), slot_name="Slot 3", is_occupied=False)
            ]
        )
        
        # Act
        occupied = lot.get_occupied_slots()
        
        # Assert
        assert len(occupied) == 1
        assert all(slot.is_occupied for slot in occupied)
    
    def test_get_occupancy_rate(self):
        """Test calculating occupancy rate"""
        # Arrange
        lot = ParkingLot(
            id=str(uuid.uuid4()),
            name="Test Lot",
            slots=[
                ParkingSlot(id=str(uuid.uuid4()), slot_name="Slot 1", is_occupied=False),
                ParkingSlot(id=str(uuid.uuid4()), slot_name="Slot 2", is_occupied=True),
                ParkingSlot(id=str(uuid.uuid4()), slot_name="Slot 3", is_occupied=False),
                ParkingSlot(id=str(uuid.uuid4()), slot_name="Slot 4", is_occupied=True)
            ]
        )
        
        # Act
        rate = lot.get_occupancy_rate()
        
        # Assert
        assert rate == 0.5
    
    def test_get_occupancy_rate_empty_lot(self):
        """Test calculating occupancy rate for empty lot"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Empty Lot")
        
        # Act
        rate = lot.get_occupancy_rate()
        
        # Assert
        assert rate == 0.0
    
    def test_get_slot(self):
        """Test getting a specific slot by ID"""
        # Arrange
        slot_id = str(uuid.uuid4())
        lot = ParkingLot(
            id=str(uuid.uuid4()),
            name="Test Lot",
            slots=[
                ParkingSlot(id=slot_id, slot_name="Target Slot"),
                ParkingSlot(id=str(uuid.uuid4()), slot_name="Other Slot")
            ]
        )
        
        # Act
        found_slot = lot.get_slot(slot_id)
        
        # Assert
        assert found_slot is not None
        assert found_slot.id == slot_id
        assert found_slot.slot_name == "Target Slot"
    
    def test_get_slot_not_found(self):
        """Test getting a non-existent slot"""
        # Arrange
        lot = ParkingLot(
            id=str(uuid.uuid4()),
            name="Test Lot",
            slots=[ParkingSlot(id=str(uuid.uuid4()), slot_name="Only Slot")]
        )
        
        # Act
        found_slot = lot.get_slot("non-existent-id")

        # Assert
        assert found_slot is None

    def test_grouped_slot_stats_basic(self):
        """Test basic grouped slot statistics functionality"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Test Lot")

        # Create slots with duplicate names across different viewpoints
        slots = [
            ParkingSlot(id="slot1", slot_name="A1", is_occupied=True, viewpoint_id="vp1"),
            ParkingSlot(id="slot2", slot_name="A1", is_occupied=False, viewpoint_id="vp2"),  # Same name, different viewpoint
            ParkingSlot(id="slot3", slot_name="A2", is_occupied=False, viewpoint_id="vp1"),
            ParkingSlot(id="slot4", slot_name="A3", is_occupied=True, viewpoint_id="vp1"),
            ParkingSlot(id="slot5", slot_name="A3", is_occupied=False, viewpoint_id="vp2"),  # Same name, different viewpoint
        ]

        for slot in slots:
            lot.slots.append(slot)

        # Act
        stats = lot.get_grouped_slot_stats()

        # Assert
        assert stats['total_groups'] == 3  # A1, A2, A3
        assert stats['occupied_groups'] == 2  # A1 (any instance occupied), A3 (any instance occupied)
        assert stats['available_groups'] == 1  # A2 (no instances occupied)
        assert stats['occupancy_rate'] == pytest.approx(66.7, abs=0.1)

        # Check group details
        assert 'A1' in stats['group_details']
        assert 'A2' in stats['group_details']
        assert 'A3' in stats['group_details']

        assert stats['group_details']['A1']['is_occupied'] is True  # Any instance occupied
        assert stats['group_details']['A1']['instances'] == 2
        assert stats['group_details']['A2']['is_occupied'] is False  # No instances occupied
        assert stats['group_details']['A2']['instances'] == 1
        assert stats['group_details']['A3']['is_occupied'] is True  # Any instance occupied
        assert stats['group_details']['A3']['instances'] == 2

    def test_grouped_slot_stats_empty_lot(self):
        """Test grouped slot statistics with no slots"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Empty Lot")

        # Act
        stats = lot.get_grouped_slot_stats()

        # Assert
        assert stats['total_groups'] == 0
        assert stats['occupied_groups'] == 0
        assert stats['available_groups'] == 0
        assert stats['occupancy_rate'] == 0.0
        assert stats['group_details'] == {}

    def test_grouped_slot_stats_all_occupied(self):
        """Test grouped slot statistics when all groups are occupied"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Test Lot")

        slots = [
            ParkingSlot(id="slot1", slot_name="A1", is_occupied=True, viewpoint_id="vp1"),
            ParkingSlot(id="slot2", slot_name="A1", is_occupied=True, viewpoint_id="vp2"),
            ParkingSlot(id="slot3", slot_name="A2", is_occupied=True, viewpoint_id="vp1"),
        ]

        for slot in slots:
            lot.slots.append(slot)

        # Act
        stats = lot.get_grouped_slot_stats()

        # Assert
        assert stats['total_groups'] == 2
        assert stats['occupied_groups'] == 2
        assert stats['available_groups'] == 0
        assert stats['occupancy_rate'] == 100.0

    def test_grouped_slot_stats_all_free(self):
        """Test grouped slot statistics when all groups are free"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Test Lot")

        slots = [
            ParkingSlot(id="slot1", slot_name="A1", is_occupied=False, viewpoint_id="vp1"),
            ParkingSlot(id="slot2", slot_name="A1", is_occupied=False, viewpoint_id="vp2"),
            ParkingSlot(id="slot3", slot_name="A2", is_occupied=False, viewpoint_id="vp1"),
        ]

        for slot in slots:
            lot.slots.append(slot)

        # Act
        stats = lot.get_grouped_slot_stats()

        # Assert
        assert stats['total_groups'] == 2
        assert stats['occupied_groups'] == 0
        assert stats['available_groups'] == 2
        assert stats['occupancy_rate'] == 0.0

    def test_grouped_free_slot_count(self):
        """Test get_grouped_free_slot_count method"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Test Lot")

        slots = [
            ParkingSlot(id="slot1", slot_name="A1", is_occupied=True, viewpoint_id="vp1"),
            ParkingSlot(id="slot2", slot_name="A1", is_occupied=False, viewpoint_id="vp2"),
            ParkingSlot(id="slot3", slot_name="A2", is_occupied=False, viewpoint_id="vp1"),
        ]

        for slot in slots:
            lot.slots.append(slot)

        # Act
        free_count = lot.get_grouped_free_slot_count()

        # Assert
        assert free_count == 1  # Only A2 group is free

    def test_grouped_busy_slot_count(self):
        """Test get_grouped_busy_slot_count method"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Test Lot")

        slots = [
            ParkingSlot(id="slot1", slot_name="A1", is_occupied=True, viewpoint_id="vp1"),
            ParkingSlot(id="slot2", slot_name="A1", is_occupied=False, viewpoint_id="vp2"),
            ParkingSlot(id="slot3", slot_name="A2", is_occupied=False, viewpoint_id="vp1"),
        ]

        for slot in slots:
            lot.slots.append(slot)

        # Act
        busy_count = lot.get_grouped_busy_slot_count()

        # Assert
        assert busy_count == 1  # Only A1 group is occupied

    def test_grouped_slot_stats_viewpoint_tracking(self):
        """Test that grouped stats correctly track viewpoints for each group"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Test Lot")

        slots = [
            ParkingSlot(id="slot1", slot_name="A1", is_occupied=False, viewpoint_id="vp1"),
            ParkingSlot(id="slot2", slot_name="A1", is_occupied=False, viewpoint_id="vp2"),
            ParkingSlot(id="slot3", slot_name="A1", is_occupied=False, viewpoint_id="vp3"),
            ParkingSlot(id="slot4", slot_name="A2", is_occupied=False, viewpoint_id="vp1"),
        ]

        for slot in slots:
            lot.slots.append(slot)

        # Act
        stats = lot.get_grouped_slot_stats()

        # Assert
        a1_viewpoints = set(stats['group_details']['A1']['viewpoints'])
        a2_viewpoints = set(stats['group_details']['A2']['viewpoints'])

        assert a1_viewpoints == {"vp1", "vp2", "vp3"}
        assert a2_viewpoints == {"vp1"}
        assert stats['group_details']['A1']['instances'] == 3
        assert stats['group_details']['A2']['instances'] == 1

    def test_grouped_slot_stats_single_instance_groups(self):
        """Test grouped stats with groups that have only single instances"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Test Lot")

        slots = [
            ParkingSlot(id="slot1", slot_name="A1", is_occupied=True, viewpoint_id="vp1"),
            ParkingSlot(id="slot2", slot_name="A2", is_occupied=False, viewpoint_id="vp1"),
            ParkingSlot(id="slot3", slot_name="A3", is_occupied=True, viewpoint_id="vp1"),
        ]

        for slot in slots:
            lot.slots.append(slot)

        # Act
        stats = lot.get_grouped_slot_stats()

        # Assert
        assert stats['total_groups'] == 3
        assert stats['occupied_groups'] == 2  # A1 and A3
        assert stats['available_groups'] == 1  # A2
        assert stats['occupancy_rate'] == pytest.approx(66.7, abs=0.1)

        # Each group should have exactly 1 instance
        for group_name, details in stats['group_details'].items():
            assert details['instances'] == 1
