/**
 * ZoomManager - Handles image zooming and panning functionality
 */
class ZoomManager {
    constructor(imageContainer, imageElement) {
        this.imageContainer = imageContainer;
        this.imageElement = imageElement;
        this.zoomLevel = 1.0; // 100%
        this.maxZoom = 5.0;   // 500%
        this.minZoom = 0.5;   // 50%
        this.zoomStep = 0.1;  // 10% per step
        this.isDragging = false;
        this.lastX = 0;
        this.lastY = 0;
        this.offsetX = 0;
        this.offsetY = 0;
        
        // Elements
        this.zoomInBtn = document.getElementById('zoom-in-btn');
        this.zoomOutBtn = document.getElementById('zoom-out-btn');
        this.zoomResetBtn = document.getElementById('zoom-reset-btn');
        this.zoomLevelDisplay = document.getElementById('zoom-level');
        this.minimapContainer = document.getElementById('minimap-container');
        this.minimapViewport = document.getElementById('minimap-viewport');
        this.minimapToggle = document.getElementById('minimap-toggle');

        // Setup zoom wrapper
        this.setupZoomContainer();
        
        this.init();
    }
    
    setupZoomContainer() {
        // Get or create the wrapper
        if (!this.imageContainer.parentElement.classList.contains('zoom-wrapper')) {
            // Create a wrapper to contain the zoomed content
            const wrapper = document.createElement('div');
            wrapper.className = 'zoom-wrapper';
            
            // Insert wrapper in DOM
            this.imageContainer.parentNode.insertBefore(wrapper, this.imageContainer);
            wrapper.appendChild(this.imageContainer);
        }
        
        // Store reference to wrapper
        this.wrapper = this.imageContainer.parentElement;
        
        // Ensure wrapper has proper overflow setting
        this.wrapper.style.overflow = 'hidden';
        
        // Set position to ensure image fills container properly
        this.imageContainer.style.position = 'relative';
        this.imageElement.style.position = 'absolute';
        this.imageElement.style.width = '100%';
        this.imageElement.style.height = '100%';
        this.imageElement.style.objectFit = 'contain';

        // Set initial dimensions based on image load state
        if (this.imageElement.complete) {
            this.updateContainerDimensions();
            // this.updateWrapperDimensions(); // REMOVED: Wrapper should size naturally
        } else {
            this.imageElement.onload = () => {
                this.updateContainerDimensions();
                // this.updateWrapperDimensions(); // REMOVED: Wrapper should size naturally
            };
        }

        // Reset padding and margin
        this.imageElement.style.margin = '0';
        this.imageElement.style.padding = '0';
        this.imageElement.style.display = 'block';
    }

    init() {
        // Initialize the image container for zooming
        this.imageContainer.classList.add('zoom-container');
        
        // Set initial dimensions for the container
        this.updateContainerDimensions();
        
        // Center the image initially to eliminate whitespace
        this.centerImageInitially();
        
        // Apply initial transform
        this.applyTransform();
        
        // Add event listeners for zoom controls
        this.zoomInBtn.addEventListener('click', () => this.zoomIn());
        this.zoomOutBtn.addEventListener('click', () => this.zoomOut());
        this.zoomResetBtn.addEventListener('click', () => this.resetZoom());
        
        // Add mouse wheel event for zooming
        this.wrapper.addEventListener('wheel', (e) => {
            e.preventDefault();
            if (e.deltaY < 0) {
                this.zoomIn(e.clientX, e.clientY);
            } else {
                this.zoomOut(e.clientX, e.clientY);
            }
        });
        
        // Add mouse events for panning - starting with mousedown on the image container
        this.imageContainer.addEventListener('mousedown', (e) => {
            // Only enable panning when zoomed in
            if (this.zoomLevel > 1.0) {
                this.startDrag(e);
            }
        });
        
        // But track mouse movement and release anywhere in the document
        document.addEventListener('mousemove', (e) => {
            if (this.isDragging) {
                this.drag(e);
            }
        });
        
        document.addEventListener('mouseup', () => {
            if (this.isDragging) {
                this.stopDrag();
            }
        });
        
        // Also stop dragging if mouse leaves the window
        document.addEventListener('mouseleave', () => {
            if (this.isDragging) {
                this.stopDrag();
            }
        });
        
        // Handle window resize
        window.addEventListener('resize', () => this.handleResize());

        // Add minimap toggle event listener
        if (this.minimapToggle) {
            this.minimapToggle.addEventListener('change', () => {
                this.updateMinimapVisibility();
            });
        }

        // Initialize minimap
        this.updateMinimapVisibility();
        this.updateMinimapViewport();
    }
    
    // New method to center the image initially
    centerImageInitially() {
        // This ensures the image is properly centered in the container
        const wrapperRect = this.wrapper.getBoundingClientRect();
        const imageRect = this.imageElement.getBoundingClientRect();
        
        // Calculate offsets to center the image (if it's larger than the viewport)
        if (imageRect.height > wrapperRect.height) {
            // If image is taller than container, center it vertically
            this.offsetY = (wrapperRect.height - imageRect.height) / 2;
        }
        
        if (imageRect.width > wrapperRect.width) {
            // If image is wider than container, center it horizontally
            this.offsetX = (wrapperRect.width - imageRect.width) / 2;
        }
    }
    
    zoomIn(clientX, clientY) {
        if (this.zoomLevel >= this.maxZoom) return;
        
        // Calculate the position relative to the image before zooming
        const beforeZoomPos = this.getCursorPositionRelativeToImage(clientX, clientY);

        // Store old zoom level for offset calculation
        const oldZoomLevel = this.zoomLevel;
        
        // Apply zoom
        this.zoomLevel = Math.min(this.zoomLevel + this.zoomStep, this.maxZoom);
        
        // Adjust offset to keep cursor point fixed
        if (beforeZoomPos) {
            this.adjustOffsetToKeepPointFixed(beforeZoomPos, oldZoomLevel);
        }
        
        this.applyTransform();
        this.updateZoomControls();

        // Update minimap when zooming in
        this.updateMinimapVisibility();
        this.updateMinimapViewport();
    }
    
    zoomOut(clientX, clientY) {
        if (this.zoomLevel <= this.minZoom) return;
        
        // Calculate the position relative to the image before zooming
        const beforeZoomPos = this.getCursorPositionRelativeToImage(clientX, clientY);

        // Store old zoom level for offset calculation
        const oldZoomLevel = this.zoomLevel;
        
        // Apply zoom
        this.zoomLevel = Math.max(this.zoomLevel - this.zoomStep, this.minZoom);
        
        // Adjust offset to keep cursor point fixed
        if (beforeZoomPos) {
            this.adjustOffsetToKeepPointFixed(beforeZoomPos, oldZoomLevel);
        } else {
            // When zooming out without a specific point, ensure image stays in bounds
            this.constrainOffset();
        }
        
        this.applyTransform();
        this.updateZoomControls();
        
        // Update minimap when zooming out
        this.updateMinimapVisibility();
        this.updateMinimapViewport();

        // If we're back to 100% or less, reset position
        if (this.zoomLevel <= 1.0) {
            this.resetPosition();
        }
    }
    
    resetZoom() {
        this.zoomLevel = 1.0;
        this.resetPosition();
        this.applyTransform();
        this.updateZoomControls();
        // Force a refresh of zone positions
        this.refreshZonePositions();

        // Update minimap when resetting zoom
        this.updateMinimapVisibility();
        this.updateMinimapViewport();
    }
    
    resetPosition() {
        this.offsetX = 0;
        this.offsetY = 0;
    }
    
    startDrag(e) {
        if (e.button !== 0) return; // Only left mouse button
        
        this.isDragging = true;
        this.lastX = e.clientX;
        this.lastY = e.clientY;
        this.imageContainer.classList.add('panning');
        // Prevent default behavior to avoid text selection while dragging
        e.preventDefault();
    }
    
    drag(e) {
        if (!this.isDragging) return;
        
        const deltaX = e.clientX - this.lastX;
        const deltaY = e.clientY - this.lastY;
        
        // Apply movement directly to offset
        this.offsetX += deltaX;
        this.offsetY += deltaY;
        
        // Keep image within bounds
        this.constrainOffset();
        
        // Apply the new transform
        this.applyTransform();
        
        // Update minimap viewport when panning
        this.updateMinimapViewport();

        // Update last position for next move
        this.lastX = e.clientX;
        this.lastY = e.clientY;
        
        // Prevent default to avoid interference with dragging
        e.preventDefault();
    }
    
    stopDrag() {
        this.isDragging = false;
        this.imageContainer.classList.remove('panning');
    }
    
    constrainOffset() {
        // Get the wrapper (viewport) dimensions
        const wrapperRect = this.wrapper.getBoundingClientRect();
        
        // Use the actual image dimensions
        const imageWidth = this.imageElement.offsetWidth;
        const imageHeight = this.imageElement.offsetHeight;
        
        // Calculate the dimensions of the zoomed image
        const zoomedWidth = imageWidth * this.zoomLevel;
        const zoomedHeight = imageHeight * this.zoomLevel;
        
        // Calculate the maximum offsets that would allow seeing the entire image
        // We need to allow panning from one edge all the way to the other
        const minOffsetX = -(zoomedWidth - wrapperRect.width);
        const minOffsetY = -(zoomedHeight - wrapperRect.height);
        
        // Apply constraints - allow full movement between bounds
        if (zoomedWidth > wrapperRect.width) {
            // Constrain horizontal panning
            this.offsetX = Math.min(Math.max(this.offsetX, minOffsetX), 0);
        } else {
            // Center smaller images horizontally
            this.offsetX = (wrapperRect.width - zoomedWidth) / 2;
        }
        
        if (zoomedHeight > wrapperRect.height) {
            // Constrain vertical panning
            this.offsetY = Math.min(Math.max(this.offsetY, minOffsetY), 0);
        } else {
            // Center smaller images vertically
            this.offsetY = (wrapperRect.height - zoomedHeight) / 2;
        }
    }
    
    updateContainerDimensions() {
        if (!this.imageElement.complete) return;

        // Calculate aspect ratio from natural dimensions
        const aspectRatio = (this.imageElement.naturalHeight / this.imageElement.naturalWidth) * 100;
        
        // Set container padding to maintain aspect ratio
        this.imageContainer.style.paddingBottom = `${aspectRatio}%`;
        
        // Force immediate layout update
        void this.imageContainer.offsetHeight;
    }
    
    applyTransform() {
        // Round zoom level to prevent floating point errors
        if (Math.abs(this.zoomLevel - 1.0) < 0.001) {
            // Reset transform at 100% zoom
            this.imageContainer.style.transform = 'none';
            this.zoomLevel = 1.0;
            this.offsetX = 0;
            this.offsetY = 0;
        } else {
            this.imageContainer.style.transform = 
                `matrix(${this.zoomLevel}, 0, 0, ${this.zoomLevel}, ${this.offsetX}, ${this.offsetY})`;
        }
        
        // Update any zone polygons to match current scale
        this.refreshZonePositions();

        // Update minimap when transform changes
        this.updateMinimapViewport();
    }
    
    updateZoomControls() {
        // Update zoom level display
        this.zoomLevelDisplay.textContent = `${Math.round(this.zoomLevel * 100)}%`;
        
        // Enable/disable buttons based on zoom limits
        this.zoomInBtn.disabled = this.zoomLevel >= this.maxZoom;
        this.zoomOutBtn.disabled = this.zoomLevel <= this.minZoom;
    }
    
    handleResize() {
        // Update dimensions
        this.updateContainerDimensions();
        
        // Also update wrapper dimensions when resizing
        this.updateWrapperDimensions();
        
        // When window resizes, make sure image stays within constraints
        this.constrainOffset();
        this.applyTransform();
        
        // Refresh zone positions
        this.refreshZonePositions();

        // Update minimap on resize
        this.updateMinimapViewport();
    }
    
    // New method to get cursor position relative to the image
    getCursorPositionRelativeToImage(clientX, clientY) {
        if (!clientX || !clientY) return null;

        // Get bounding rectangle of the image element itself for most accurate positioning
        const imageRect = this.imageElement.getBoundingClientRect();

        // Calculate cursor position relative to the image rect
        const rawX = clientX - imageRect.left;
        const rawY = clientY - imageRect.top;

        // Convert to position relative to image at current zoom level
        return {
            x: (rawX - this.offsetX) / this.zoomLevel,
            y: (rawY - this.offsetY) / this.zoomLevel
        };
    }

    // Replace the existing getUnzoomedCoordinates method with this improved version
    getUnzoomedCoordinates(clientX, clientY) {
        const rect = this.imageContainer.getBoundingClientRect();
        
        // Get relative position within the container
        const relativeX = (clientX - rect.left - this.offsetX) / this.zoomLevel;
        const relativeY = (clientY - rect.top - this.offsetY) / this.zoomLevel;
        
        // Convert to image coordinates
        return {
            x: relativeX,
            y: relativeY
        };
    }

    // New method to adjust offset to keep the cursor point fixed during zoom
    adjustOffsetToKeepPointFixed(point, oldZoomLevel) {

        // Get the current image and wrapper dimensions
        const imageRect = this.imageElement.getBoundingClientRect();
        const wrapperRect = this.wrapper.getBoundingClientRect();

        // Calculate the offset from the viewport to the image container
        const wrapperOffsetX = wrapperRect.left;
        const wrapperOffsetY = wrapperRect.top;

        // Calculate cursor position relative to the wrapper
        const relativeX = clientX - wrapperOffsetX;
        const relativeY = clientY - wrapperOffsetY;

        // Account for current zoom and pan
        // This is the key fix - we need to account for how the transform affects coordinates
        const unzoomedX = relativeX / this.zoomLevel - this.offsetX / this.zoomLevel;
        const unzoomedY = relativeY / this.zoomLevel - this.offsetY / this.zoomLevel;

        // Constrain to image bounds
        return {
            x: Math.max(0, Math.min(unzoomedX, this.imageElement.offsetWidth)),
            y: Math.max(0, Math.min(unzoomedY, this.imageElement.offsetHeight))
        };
    }
    
    // New method to adjust offset to keep the cursor point fixed during zoom
    adjustOffsetToKeepPointFixed(point, oldZoomLevel) {
        const wrapperRect = this.wrapper.getBoundingClientRect();

        // Calculate how the point would move at the new zoom level
        const oldScaledX = point.x * oldZoomLevel;
        const oldScaledY = point.y * oldZoomLevel;
        const newScaledX = point.x * this.zoomLevel;
        const newScaledY = point.y * this.zoomLevel;

        // Calculate the difference to adjust the offset
        const deltaX = newScaledX - oldScaledX;
        const deltaY = newScaledY - oldScaledY;

        // Update the offset to compensate for the movement
        this.offsetX -= deltaX;
        this.offsetY -= deltaY;

        // Ensure offset stays within limits
        this.constrainOffset();
    }

    // Replace the old getCursorPosition method
    getCursorPosition(clientX, clientY) {
        return this.getCursorPositionRelativeToImage(clientX, clientY);
    }

    // Replace the old adjustOffsetForFixedPoint method
    adjustOffsetForFixedPoint(beforeZoomPos) {
        const oldZoomLevel = this.zoomLevel - this.zoomStep; // Approximate previous zoom level
        this.adjustOffsetToKeepPointFixed(beforeZoomPos, oldZoomLevel);
    }

    // Method to get current zoom information for other components
    getZoomInfo() {
        return {
            level: this.zoomLevel,
            offsetX: this.offsetX,
            offsetY: this.offsetY
        };
    }
    
    // Method to adjust coordinate conversions based on zoom
    adjustCoordinate(x, y) {
        return {
            x: (x - this.offsetX) / this.zoomLevel,
            y: (y - this.offsetY) / this.zoomLevel
        };
    }

    // Method to refresh all zone positions after zoom changes
    refreshZonePositions() {
        // Get all parking zones
        const zones = document.querySelectorAll('.zone-polygon');

        // Update their dimensions to match current image size
        zones.forEach(zone => {
            zone.style.width = `${this.imageElement.offsetWidth}px`;
            zone.style.height = `${this.imageElement.offsetHeight}px`;
        });
        
        // Update zone labels based on their percentage positions
        document.querySelectorAll('.zone-label').forEach(label => {
            if (label.dataset.centerXPercent && label.dataset.centerYPercent) {
                const xPercent = parseFloat(label.dataset.centerXPercent);
                const yPercent = parseFloat(label.dataset.centerYPercent);
                
                // Calculate pixel position based on current image dimensions
                const x = this.imageElement.offsetWidth * xPercent / 100;
                const y = this.imageElement.offsetHeight * yPercent / 100;
                
                label.style.left = `${x}px`;
                label.style.top = `${y}px`;
            }
        });

        // Update vertex point positions for proper scaling when zoomed
        document.querySelectorAll('.vertex-point, .drawing-point').forEach(point => {
            if (this.zoomLevel !== 1) {
                point.style.transform = `scale(${1/this.zoomLevel})`;
            } else {
                point.style.transform = 'none';
            }
        });
    }

    // New methods for minimap management

    /**
     * Update minimap visibility based on zoom level and toggle state
     */
    updateMinimapVisibility() {
        if (!this.minimapContainer) return;

        // Show minimap if zoom level > 1.0 AND toggle is checked
        if (this.zoomLevel > 1.0 && this.minimapToggle && this.minimapToggle.checked) {
            this.minimapContainer.classList.remove('minimap-hidden');
        } else {
            this.minimapContainer.classList.add('minimap-hidden');
        }
    }

    /**
     * Update the minimap viewport rectangle to reflect the current visible area
     */
    updateMinimapViewport() {
        if (!this.minimapViewport || !this.minimapContainer) return;

        // Only update if minimap is visible
        if (this.minimapContainer.classList.contains('minimap-hidden')) return;

        // Get actual image dimensions
        const wrapperRect = this.wrapper.getBoundingClientRect();
        const minimapRect = this.minimapContainer.getBoundingClientRect();
        const imageRect = this.imageElement.getBoundingClientRect();

        // Calculate the actual aspect ratio of the original image
        const imageAspectRatio = this.imageElement.naturalWidth / this.imageElement.naturalHeight;
        
        // Calculate the scaling factor between minimap and actual image
        const minimapImageWidth = minimapRect.width;
        const minimapImageHeight = minimapRect.width / imageAspectRatio;

        // Calculate the visible portion relative to the actual image dimensions
        const visibleWidth = wrapperRect.width / this.zoomLevel;
        const visibleHeight = wrapperRect.height / this.zoomLevel;

        // Calculate viewport position and size relative to the image size
        const viewportX = (-this.offsetX / this.zoomLevel) * (minimapImageWidth / imageRect.width);
        const viewportY = (-this.offsetY / this.zoomLevel) * (minimapImageHeight / imageRect.height);
        const viewportWidth = visibleWidth * (minimapImageWidth / imageRect.width);
        const viewportHeight = visibleHeight * (minimapImageHeight / imageRect.height);

        // Apply to the viewport element
        this.minimapViewport.style.left = `${viewportX}px`;
        this.minimapViewport.style.top = `${viewportY}px`;
        this.minimapViewport.style.width = `${viewportWidth}px`;
        this.minimapViewport.style.height = `${viewportHeight}px`;
    }
}

// Export for use in other modules
window.ZoomManager = ZoomManager;

