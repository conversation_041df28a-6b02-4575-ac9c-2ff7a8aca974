import os
import time
from datetime import datetime, timedelta
import requests
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(levelname)s - %(message)s')

def parse_timestamp_from_filename(filename: str) -> datetime:
    """Extract timestamp from filename format: YYYYMMDDHHmmssSSS_UTC+XX.jpg"""
    timestamp_str = filename.split('_')[0]
    return datetime.strptime(timestamp_str, '%Y%m%d%H%M%S%f')

def filter_images_by_delay(image_files: list, min_delay_seconds: float) -> list:
    """
    Filter images to ensure minimum time delay between consecutive frames.
    
    Args:
        image_files: List of image files sorted by timestamp
        min_delay_seconds: Minimum delay between frames in seconds
        
    Returns:
        List of filtered image files meeting the delay requirement
    """
    if not image_files:
        return []
        
    filtered_images = [image_files[0]]  # Always include first image
    min_delay = timedelta(seconds=min_delay_seconds)
    
    for current_image in image_files[1:]:
        last_included = filtered_images[-1]
        current_time = parse_timestamp_from_filename(current_image.name)
        last_time = parse_timestamp_from_filename(last_included.name)
        
        if current_time - last_time >= min_delay:
            filtered_images.append(current_image)
    
    return filtered_images

def upload_images(base_dir: str, lot_id: str, min_delay: float, api_url: str, max_delay: float = None, send_as_body: bool = False):
    """
    Upload images from directory with specified minimum delay between uploads.
    
    Args:
        base_dir: Directory containing images
        lot_id: ID of the parking lot
        min_delay: Minimum delay between uploads in seconds
        api_url: Base URL of the API
        max_delay: Maximum sleep time between uploads in seconds. If actual delay is larger,
                  sleep will be capped at this value.
        send_as_body: If True, sends image as raw bytes in request body. If False, sends as multipart/form-data.
    """
    base_path = Path(base_dir)
    
    if not base_path.exists():
        raise ValueError(f"Directory {base_dir} does not exist")
    
    # Get all jpg files and sort them by timestamp in filename
    image_files = sorted(
        [f for f in base_path.glob('*.jpg')],
        key=lambda x: parse_timestamp_from_filename(x.name)
    )
    
    if not image_files:
        logging.error("No jpg files found in directory")
        return
        
    # Filter images to ensure minimum delay between frames
    filtered_images = filter_images_by_delay(image_files, min_delay)
    logging.info(f"Selected {len(filtered_images)} images from {len(image_files)} total files")
    
    endpoint = f"{api_url}/api/lots/{lot_id}/image"
    
    for i, image_path in enumerate(filtered_images):
        try:
            with open(image_path, 'rb') as img:
                if send_as_body:
                    # Send as raw bytes in body
                    headers = {'Content-Type': 'image/jpeg'}
                    response = requests.post(endpoint, data=img.read(), headers=headers)
                else:
                    # Send as multipart/form-data
                    files = {'file': (image_path.name, img, 'image/jpeg')}
                    response = requests.post(endpoint, files=files)
                
                if response.status_code == 200:
                    logging.info(f"Successfully uploaded {image_path.name}")
                else:
                    logging.error(f"Failed to upload {image_path.name}: {response.json()}")
                    
            # Calculate sleep time based on timestamps if not the last image
            if i < len(filtered_images) - 1:
                current_time = parse_timestamp_from_filename(image_path.name)
                next_time = parse_timestamp_from_filename(filtered_images[i + 1].name)
                sleep_time = (next_time - current_time).total_seconds()
                
                # Cap sleep time at max_delay if specified
                if max_delay is not None:
                    sleep_time = min(sleep_time, max_delay)
                    if sleep_time < (next_time - current_time).total_seconds():
                        logging.info(f"Capping sleep time to {max_delay}s (actual delay: {(next_time - current_time).total_seconds():.1f}s)")
                
                logging.info(f"Sleeping for {sleep_time:.1f}s before next upload")
                time.sleep(sleep_time)
            
        except Exception as e:
            logging.error(f"Error uploading {image_path.name}: {str(e)}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Upload parking lot images with delay')
    parser.add_argument('--dir', required=True, help='Directory with images')
    parser.add_argument('--lot-id', required=True, help='Parking lot ID')
    parser.add_argument('--delay', type=float, default=60,
                        help='Minimum delay between uploads in seconds')
    parser.add_argument('--max-delay', type=float,
                        help='Maximum sleep time between uploads. Caps sleep at this value.')
    parser.add_argument('--api-url', default='http://localhost:5000',
                        help='Base API URL')
    parser.add_argument('--send-as-body', action='store_true',
                        help='Send image as raw bytes in request body instead of multipart/form-data')
    
    args = parser.parse_args()
    
    upload_images(args.dir, args.lot_id, args.delay, args.api_url, 
                 max_delay=args.max_delay, send_as_body=args.send_as_body)