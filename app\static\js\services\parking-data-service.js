/**
 * ParkingDataService - Centralized service for fetching parking lot data
 * 
 * Provides a unified API for accessing:
 * - Parking lot information
 * - Viewpoint data and images
 * - Parking slot data and status
 * - Real-time updates via WebSocket
 */
class ParkingDataService {
    constructor() {
        this.cache = new Map();
        this.subscribers = new Map();
        this.websocket = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
    }

    /**
     * Get parking lot information
     */
    async getParkingLot(lotId) {
        const cacheKey = `lot_${lotId}`;

        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        try {
            const response = await fetch(`/api/lots/${lotId}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch parking lot: ${response.statusText}`);
            }

            const data = await response.json();
            this.cache.set(cacheKey, data);
            return data;
        } catch (error) {
            console.error('Error fetching parking lot:', error);
            throw error;
        }
    }

    /**
     * Get viewpoint information including image URL
     */
    async getViewpoint(lotId, viewpointId) {
        const cacheKey = `viewpoint_${viewpointId}`;

        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        try {
            // First get all viewpoints for the lot, then find the specific one
            const response = await fetch(`/api/lots/${lotId}/viewpoints/`);
            if (!response.ok) {
                throw new Error(`Failed to fetch viewpoints: ${response.statusText}`);
            }

            const data = await response.json();
            const viewpoint = data.viewpoints.find(vp => vp.id === viewpointId);

            if (!viewpoint) {
                throw new Error(`Viewpoint ${viewpointId} not found`);
            }

            this.cache.set(cacheKey, viewpoint);
            return viewpoint;
        } catch (error) {
            console.error('Error fetching viewpoint:', error);
            throw error;
        }
    }

    /**
     * Get parking slots for a specific viewpoint
     */
    async getParkingSlots(lotId, viewpointId = null) {
        const cacheKey = `slots_${lotId}_${viewpointId}`;

        try {
            const response = await fetch(`/api/lots/${lotId}/slots`);
            if (!response.ok) {
                throw new Error(`Failed to fetch parking slots: ${response.statusText}`);
            }

            const allSlots = await response.json();

            // Filter slots for the specific viewpoint if provided
            const filteredSlots = viewpointId ?
                allSlots.filter(slot => slot.viewpoint_id === viewpointId) :
                allSlots;

            const data = { slots: filteredSlots };

            // Cache the slots but don't cache for too long since status can change
            this.cache.set(cacheKey, data);
            setTimeout(() => this.cache.delete(cacheKey), 30000); // 30 second cache

            return data;
        } catch (error) {
            console.error('Error fetching parking slots:', error);
            throw error;
        }
    }

    /**
     * Get complete parking visualization data (lot + viewpoint + slots)
     */
    async getParkingVisualizationData(lotId, viewpointId) {
        try {
            const [lot, viewpoint, slotsResponse] = await Promise.all([
                this.getParkingLot(lotId),
                this.getViewpoint(lotId, viewpointId),
                this.getParkingSlots(lotId, viewpointId)
            ]);

            return {
                lot,
                viewpoint,
                slots: slotsResponse.slots || [],
                imageUrl: this.getViewpointImageUrl(viewpoint)
            };
        } catch (error) {
            console.error('Error fetching parking visualization data:', error);
            throw error;
        }
    }

    /**
     * Get the image URL for a viewpoint
     */
    getViewpointImageUrl(viewpoint) {
        if (viewpoint.stream_url && viewpoint.is_stream_active) {
            // For live streams, we might want to use a snapshot endpoint
            // For now, fall back to the image_path which should contain the latest frame
            return viewpoint.image_path ? `/static/${viewpoint.image_path}` : null;
        } else if (viewpoint.image_path) {
            return `/static/${viewpoint.image_path}`;
        } else {
            console.warn('No image available for viewpoint:', viewpoint.id);
            return null;
        }
    }

    /**
     * Subscribe to real-time updates for a parking lot
     */
    subscribeToUpdates(lotId, callback) {
        if (!this.subscribers.has(lotId)) {
            this.subscribers.set(lotId, new Set());
        }
        
        this.subscribers.get(lotId).add(callback);
        
        // Initialize WebSocket connection if not already connected
        this.initializeWebSocket();
        
        // Return unsubscribe function
        return () => {
            const callbacks = this.subscribers.get(lotId);
            if (callbacks) {
                callbacks.delete(callback);
                if (callbacks.size === 0) {
                    this.subscribers.delete(lotId);
                }
            }
        };
    }

    /**
     * Initialize WebSocket connection for real-time updates
     */
    initializeWebSocket() {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            return;
        }

        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/parking-updates`;
            
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                console.log('ParkingDataService: WebSocket connected');
                this.reconnectAttempts = 0;
            };
            
            this.websocket.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleWebSocketMessage(data);
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                }
            };
            
            this.websocket.onclose = () => {
                console.log('ParkingDataService: WebSocket disconnected');
                this.scheduleReconnect();
            };
            
            this.websocket.onerror = (error) => {
                console.error('ParkingDataService: WebSocket error:', error);
            };
            
        } catch (error) {
            console.error('Error initializing WebSocket:', error);
            this.scheduleReconnect();
        }
    }

    /**
     * Handle incoming WebSocket messages
     */
    handleWebSocketMessage(data) {
        const { type, lot_id, slot_id, is_occupied, viewpoint_id } = data;
        
        if (type === 'slot_status_update' && lot_id) {
            // Invalidate cache for affected slots
            this.cache.delete(`slots_${viewpoint_id}`);
            
            // Notify subscribers
            const callbacks = this.subscribers.get(lot_id);
            if (callbacks) {
                callbacks.forEach(callback => {
                    try {
                        callback({
                            type: 'slot_update',
                            slotId: slot_id,
                            isOccupied: is_occupied,
                            viewpointId: viewpoint_id
                        });
                    } catch (error) {
                        console.error('Error in subscriber callback:', error);
                    }
                });
            }
        }
    }

    /**
     * Schedule WebSocket reconnection
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max WebSocket reconnection attempts reached');
            return;
        }
        
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        
        setTimeout(() => {
            console.log(`Attempting WebSocket reconnection (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            this.initializeWebSocket();
        }, delay);
    }

    /**
     * Clear cache for specific keys or all cache
     */
    clearCache(pattern = null) {
        if (pattern) {
            for (const key of this.cache.keys()) {
                if (key.includes(pattern)) {
                    this.cache.delete(key);
                }
            }
        } else {
            this.cache.clear();
        }
    }

    /**
     * Cleanup resources
     */
    destroy() {
        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
        }
        this.subscribers.clear();
        this.cache.clear();
    }
}

// Create singleton instance
const parkingDataService = new ParkingDataService();

// Export for use in other modules
window.ParkingDataService = parkingDataService;
