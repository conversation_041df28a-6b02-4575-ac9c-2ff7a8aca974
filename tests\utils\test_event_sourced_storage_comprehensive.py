"""
Comprehensive tests for the EventSourcedStorage class.
"""
import pytest
import uuid
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock, ANY

from app.src.utils.event_sourced_storage import EventSourcedStorage
from app.src.utils.event_store_interface import EventStore
from app.src.models.events import (
    BaseEvent, ParkingLotCreated, Parking<PERSON>otDeleted, Parking<PERSON>otRenamed,
    ParkingLotImageUpdated, ParkingSlotAdded, ParkingSlotRemoved,
    ParkingSlotZoneUpdated, ParkingSlotThresholdUpdated,
    ParkingSlotOccupancyChanged, DetectionConfigCreated,
    DetectionConfigUpdated, DetectionConfigDeleted
)
from app.src.models.orm import ParkingEventORM
from app.src.models.parking import ParkingLot, ParkingSlot
from app.src.models.geometry import Polygon, Point
from app.src.models.detection import DetectionEngineConfig
from app.src.utils.mapper import Mapper

class TestEventSourcedStorageComprehensive:
    """Comprehensive test suite for EventSourcedStorage class."""

    @pytest.fixture
    def mock_event_store(self):
        """Create a mock event store for testing."""
        mock_store = Mock(spec=EventStore)
        mock_store.append_event.return_value = True
        mock_store.get_raw_events_for_aggregate.return_value = []
        mock_store.get_all_aggregate_ids.return_value = []
        return mock_store

    @pytest.fixture
    def storage(self, mock_event_store):
        """Create an EventSourcedStorage instance with a mock event store."""
        return EventSourcedStorage(mock_event_store)

    def test_initialization(self, mock_event_store):
        """Test EventSourcedStorage initialization."""
        # Act
        storage = EventSourcedStorage(mock_event_store)
        
        # Assert
        assert storage._event_store == mock_event_store

    def test_create_and_persist_event(self, storage, mock_event_store):
        """Test creating and persisting an event."""
        # Arrange
        lot_id = str(uuid.uuid4())
        payload = {"name": "Test Lot", "image_path": None}
        
        # Act
        result = storage._create_and_persist_event(ParkingLotCreated, lot_id, payload)
        
        # Assert
        assert result is True
        mock_event_store.append_event.assert_called_once()
        event_orm = mock_event_store.append_event.call_args[0][0]
        assert isinstance(event_orm, ParkingEventORM)
        assert event_orm.lot_id == lot_id
        assert event_orm.event_type == "ParkingLotCreated"
        assert event_orm.payload == payload

    def test_create_and_persist_event_failure(self, storage, mock_event_store):
        """Test handling of event persistence failure."""
        # Arrange
        mock_event_store.append_event.return_value = False
        lot_id = str(uuid.uuid4())
        payload = {"name": "Test Lot", "image_path": None}
        
        # Act
        result = storage._create_and_persist_event(ParkingLotCreated, lot_id, payload)
        
        # Assert
        assert result is False

    def test_create_and_persist_event_exception(self, storage, mock_event_store):
        """Test handling of exceptions during event creation/persistence."""
        # Arrange
        mock_event_store.append_event.side_effect = Exception("Test exception")
        lot_id = str(uuid.uuid4())
        payload = {"name": "Test Lot", "image_path": None}
        
        # Act
        result = storage._create_and_persist_event(ParkingLotCreated, lot_id, payload)
        
        # Assert
        assert result is False

    def test_get_events_for_lot(self, storage, mock_event_store):
        """Test retrieving events for a lot."""
        # Arrange
        lot_id = str(uuid.uuid4())
        event_orm1 = ParkingEventORM(
            lot_id=lot_id,
            event_type="ParkingLotCreated",
            payload={"name": "Test Lot", "image_path": None},
            timestamp=datetime.now()
        )
        event_orm2 = ParkingEventORM(
            lot_id=lot_id,
            event_type="ParkingLotRenamed",
            payload={"new_name": "New Name"},
            timestamp=datetime.now()
        )
        mock_event_store.get_raw_events_for_aggregate.return_value = [event_orm1, event_orm2]
        
        with patch('app.src.utils.mapper.Mapper.orm_to_dataclass_event') as mock_mapper:
            # Set up mock returns for the mapper
            mock_event1 = ParkingLotCreated(
                lot_id=lot_id,
                payload={"name": "Test Lot", "image_path": None}
            )
            mock_event2 = ParkingLotRenamed(
                lot_id=lot_id,
                payload={"new_name": "New Name"}
            )
            mock_mapper.side_effect = [mock_event1, mock_event2]
            
            # Act
            events = storage._get_events_for_lot(lot_id)
            
            # Assert
            assert len(events) == 2
            assert events[0] == mock_event1
            assert events[1] == mock_event2
            mock_event_store.get_raw_events_for_aggregate.assert_called_once_with(lot_id)

    def test_get_events_for_lot_mapping_error(self, storage, mock_event_store):
        """Test handling of mapping errors when retrieving events."""
        # Arrange
        lot_id = str(uuid.uuid4())
        event_orm = ParkingEventORM(
            lot_id=lot_id,
            event_type="ParkingLotCreated",
            payload={"name": "Test Lot", "image_path": None},
            timestamp=datetime.now()
        )
        mock_event_store.get_raw_events_for_aggregate.return_value = [event_orm]
        
        with patch('app.src.utils.mapper.Mapper.orm_to_dataclass_event') as mock_mapper:
            # Set up mock to raise an exception
            mock_mapper.side_effect = Exception("Test mapping exception")
            
            # Act
            events = storage._get_events_for_lot(lot_id)
            
            # Assert
            assert len(events) == 0

    def test_get_parking_lot_no_events(self, storage, mock_event_store):
        """Test getting a parking lot when no events exist."""
        # Arrange
        lot_id = str(uuid.uuid4())
        mock_event_store.get_raw_events_for_aggregate.return_value = []
        
        # Act
        lot = storage.get_parking_lot(lot_id)
        
        # Assert
        assert lot is None
        mock_event_store.get_raw_events_for_aggregate.assert_called_once_with(lot_id)

    def test_get_parking_lot_with_creation_event_only(self, storage):
        """Test reconstructing a parking lot with only a creation event."""
        # Arrange
        lot_id = str(uuid.uuid4())
        name = "Test Lot"
        
        with patch.object(storage, '_get_events_for_lot') as mock_get_events:
            # Create a single creation event
            creation_event = ParkingLotCreated(
                lot_id=lot_id,
                payload={"name": name, "image_path": None}
            )
            mock_get_events.return_value = [creation_event]
            
            # Act
            lot = storage.get_parking_lot(lot_id)
            
            # Assert
            assert lot is not None
            assert lot.id == lot_id
            assert lot.name == name
            assert lot.image_path is None
            assert len(lot.slots) == 0
            assert lot.detection_config is None

    def test_get_parking_lot_with_invalid_first_event(self, storage):
        """Test handling when the first event is not a creation event."""
        # Arrange
        lot_id = str(uuid.uuid4())
        
        with patch.object(storage, '_get_events_for_lot') as mock_get_events:
            # First event is not a creation event
            rename_event = ParkingLotRenamed(
                lot_id=lot_id,
                payload={"new_name": "New Name"}
            )
            mock_get_events.return_value = [rename_event]
            
            # Act
            lot = storage.get_parking_lot(lot_id)
            
            # Assert
            assert lot is None

    def test_get_parking_lot_with_multiple_events(self, storage):
        """Test reconstructing a parking lot with multiple events."""
        # Arrange
        lot_id = str(uuid.uuid4())
        name = "Original Name"
        new_name = "New Name"
        image_path = "/path/to/image.jpg"
        
        with patch.object(storage, '_get_events_for_lot') as mock_get_events:
            # Create a series of events
            events = [
                ParkingLotCreated(
                    lot_id=lot_id,
                    payload={"name": name, "image_path": None}
                ),
                ParkingLotRenamed(
                    lot_id=lot_id,
                    payload={"new_name": new_name}
                ),
                ParkingLotImageUpdated(
                    lot_id=lot_id,
                    payload={"new_image_path": image_path}
                )
            ]
            mock_get_events.return_value = events
            
            # Act
            lot = storage.get_parking_lot(lot_id)
            
            # Assert
            assert lot is not None
            assert lot.id == lot_id
            assert lot.name == new_name  # Updated by rename event
            assert lot.image_path == image_path  # Updated by image update event

    def test_get_parking_lot_with_delete_event(self, storage):
        """Test reconstructing a parking lot that was deleted."""
        # Arrange
        lot_id = str(uuid.uuid4())
        
        with patch.object(storage, '_get_events_for_lot') as mock_get_events:
            # Create events including a delete event
            events = [
                ParkingLotCreated(
                    lot_id=lot_id,
                    payload={"name": "Test Lot", "image_path": None}
                ),
                ParkingLotDeleted(
                    lot_id=lot_id,
                    payload={}
                )
            ]
            mock_get_events.return_value = events
            
            # Act
            lot = storage.get_parking_lot(lot_id)
            
            # Assert
            assert lot is None  # Lot is considered deleted

    def test_get_parking_lot_with_apply_error(self, storage):
        """Test handling errors when applying events."""
        # Arrange
        lot_id = str(uuid.uuid4())
        
        with patch.object(storage, '_get_events_for_lot') as mock_get_events:
            # Create events
            creation_event = ParkingLotCreated(
                lot_id=lot_id,
                payload={"name": "Test Lot", "image_path": None}
            )
            rename_event = ParkingLotRenamed(
                lot_id=lot_id,
                payload={"new_name": "New Name"}
            )
            mock_get_events.return_value = [creation_event, rename_event]
            
            # Mock the apply method to raise an exception
            with patch.object(ParkingLot, 'apply', side_effect=Exception("Test apply exception")):
                # Act
                lot = storage.get_parking_lot(lot_id)
                
                # Assert
                assert lot is not None  # Should still return lot even if event application fails
                assert lot.name == "Test Lot"  # Name not updated due to apply error

    def test_get_all_parking_lots(self, storage, mock_event_store):
        """Test retrieving all parking lots."""
        # Arrange
        lot_id1 = str(uuid.uuid4())
        lot_id2 = str(uuid.uuid4())
        lot1 = ParkingLot(id=lot_id1, name="Lot 1")
        lot2 = ParkingLot(id=lot_id2, name="Lot 2")
        
        mock_event_store.get_all_aggregate_ids.return_value = [lot_id1, lot_id2]
        
        with patch.object(storage, 'get_parking_lot', side_effect=[lot1, lot2]) as mock_get_lot:
            # Act
            lots = storage.get_all_parking_lots()
            
            # Assert
            assert len(lots) == 2
            assert lots[0] == lot1
            assert lots[1] == lot2
            mock_event_store.get_all_aggregate_ids.assert_called_once()
            assert mock_get_lot.call_count == 2

    def test_get_all_parking_lots_with_deleted(self, storage, mock_event_store):
        """Test retrieving all parking lots including some that were deleted."""
        # Arrange
        lot_id1 = str(uuid.uuid4())
        lot_id2 = str(uuid.uuid4())
        lot_id3 = str(uuid.uuid4())
        lot1 = ParkingLot(id=lot_id1, name="Lot 1")
        
        mock_event_store.get_all_aggregate_ids.return_value = [lot_id1, lot_id2, lot_id3]
        
        # Return one lot and None for the other two (representing deleted lots)
        with patch.object(storage, 'get_parking_lot', side_effect=[lot1, None, None]) as mock_get_lot:
            # Act
            lots = storage.get_all_parking_lots()
            
            # Assert
            assert len(lots) == 1
            assert lots[0] == lot1
            assert mock_get_lot.call_count == 3

    # Command Method Tests

    def test_create_parking_lot(self, storage):
        """Test creating a parking lot."""
        # Arrange
        name = "Test Lot"
        image_path = "/path/to/image.jpg"
        
        with patch.object(storage, '_create_and_persist_event', return_value=True) as mock_create_event:
            # Act
            lot_id = storage.create_parking_lot(name, image_path)
            
            # Assert
            assert lot_id is not None
            mock_create_event.assert_called_once()
            # Check that right parameters were passed using kwargs
            args = mock_create_event.call_args.kwargs
            assert args['lot_id'] is not None
            assert args['payload'] == {"name": name, "image_path": image_path}
            assert mock_create_event.call_args.args[0] == ParkingLotCreated

    def test_create_parking_lot_failure(self, storage):
        """Test handling failure when creating a parking lot."""
        # Arrange
        with patch.object(storage, '_create_and_persist_event', return_value=False) as mock_create_event:
            # Act
            lot_id = storage.create_parking_lot("Test Lot")
            
            # Assert
            assert lot_id is None
            mock_create_event.assert_called_once()

    def test_rename_parking_lot(self, storage):
        """Test renaming a parking lot."""
        # Arrange
        lot_id = str(uuid.uuid4())
        new_name = "New Lot Name"
        
        with patch.object(storage, '_create_and_persist_event', return_value=True) as mock_create_event:
            # Act
            result = storage.rename_parking_lot(lot_id, new_name)
            
            # Assert
            assert result is True
            mock_create_event.assert_called_once()
            assert mock_create_event.call_args.args[0] == ParkingLotRenamed
            assert mock_create_event.call_args.kwargs['lot_id'] == lot_id
            assert mock_create_event.call_args.kwargs['payload'] == {'new_name': new_name}

    def test_update_parking_lot_image(self, storage):
        """Test updating a parking lot image."""
        # Arrange
        lot_id = str(uuid.uuid4())
        new_image_path = "/path/to/new_image.jpg"
        
        with patch.object(storage, '_create_and_persist_event', return_value=True) as mock_create_event:
            # Act
            result = storage.update_parking_lot_image(lot_id, new_image_path)
            
            # Assert
            assert result is True
            mock_create_event.assert_called_once()
            assert mock_create_event.call_args.args[0] == ParkingLotImageUpdated
            assert mock_create_event.call_args.kwargs['lot_id'] == lot_id
            assert mock_create_event.call_args.kwargs['payload'] == {'new_image_path': new_image_path}

    def test_delete_parking_lot_nonexistent(self, storage):
        """Test deleting a non-existent parking lot."""
        # Arrange
        lot_id = str(uuid.uuid4())
        
        with patch.object(storage, 'get_parking_lot', return_value=None):
            # Act
            result = storage.delete_parking_lot(lot_id)
            
            # Assert
            assert result is False

    def test_delete_parking_lot(self, storage):
        """Test deleting an existing parking lot."""
        # Arrange
        lot_id = str(uuid.uuid4())
        lot = ParkingLot(id=lot_id, name="Test Lot")
        
        with patch.object(storage, 'get_parking_lot', return_value=lot), \
             patch.object(storage, '_create_and_persist_event', return_value=True) as mock_create_event:
            # Act
            result = storage.delete_parking_lot(lot_id)
            
            # Assert
            assert result is True
            mock_create_event.assert_called_once()
            assert mock_create_event.call_args.args[0] == ParkingLotDeleted
            assert mock_create_event.call_args.kwargs['lot_id'] == lot_id
            assert mock_create_event.call_args.kwargs['payload'] == {}

    def test_add_parking_slot_nonexistent_lot(self, storage):
        """Test adding a parking slot to a non-existent lot."""
        # Arrange
        lot_id = str(uuid.uuid4())
        zone_points = [{"x": 10, "y": 10}, {"x": 20, "y": 20}]
        with patch.object(storage, 'get_parking_lot', return_value=None):
            # Act
            slot_id = storage.add_parking_slot(lot_id, "Test Slot", zone_points)
            
            # Assert
            assert slot_id is None

    def test_add_parking_slot(self, storage):
        """Test adding a parking slot to an existing lot."""
        # Arrange
        lot_id = str(uuid.uuid4())
        lot = ParkingLot(id=lot_id, name="Test Lot")
        zone_points = [{"x": 10, "y": 10}, {"x": 20, "y": 20}]
        overlap_threshold = 0.5
        with patch.object(storage, 'get_parking_lot', return_value=lot), \
             patch.object(storage, '_create_and_persist_event', return_value=True) as mock_create_event:
            # Act
            slot_id = storage.add_parking_slot(lot_id, "Test Slot", zone_points, overlap_threshold)
            
            # Assert
            assert slot_id is not None
            mock_create_event.assert_called_once()
            assert mock_create_event.call_args.args[0] == ParkingSlotAdded
            assert mock_create_event.call_args.kwargs['lot_id'] == lot_id
            payload = mock_create_event.call_args.kwargs['payload']
            assert payload["slot_id"] == slot_id
            assert payload["zone_points"] == zone_points
            assert payload["overlap_threshold"] == overlap_threshold

    def test_add_parking_slot_failure(self, storage):
        """Test handling failure when adding a parking slot."""
        # Arrange
        lot_id = str(uuid.uuid4())
        lot = ParkingLot(id=lot_id, name="Test Lot")
        with patch.object(storage, 'get_parking_lot', return_value=lot), \
             patch.object(storage, '_create_and_persist_event', return_value=False):
            # Act
            slot_id = storage.add_parking_slot(lot_id, "Test Slot")
            
            # Assert
            assert slot_id is None

    def test_update_slot_zone_invalid_lot(self, storage):
        """Test updating slot zone with invalid lot."""
        # Arrange
        lot_id = str(uuid.uuid4())
        slot_id = str(uuid.uuid4())
        zone_points = [{"x": 10, "y": 10}, {"x": 20, "y": 20}]
        
        with patch.object(storage, 'get_parking_lot', return_value=None):
            # Act
            result = storage.update_slot_zone(lot_id, slot_id, zone_points)
            
            # Assert
            assert result is False

    def test_update_slot_zone_invalid_slot(self, storage):
        """Test updating zone for non-existent slot."""
        # Arrange
        lot_id = str(uuid.uuid4())
        slot_id = str(uuid.uuid4())
        zone_points = [{"x": 10, "y": 10}, {"x": 20, "y": 20}]
        lot = ParkingLot(id=lot_id, name="Test Lot", slots=[])  # No slots
        
        with patch.object(storage, 'get_parking_lot', return_value=lot):
            # Act
            result = storage.update_slot_zone(lot_id, slot_id, zone_points)
            
            # Assert
            assert result is False
    def test_update_slot_zone(self, storage):
        """Test updating slot zone for a valid slot."""
        # Arrange
        lot_id = str(uuid.uuid4())
        slot_id = str(uuid.uuid4())
        zone_points = [{"x": 10, "y": 10}, {"x": 20, "y": 20}]
        
        # Create a lot with a slot
        slot = ParkingSlot(id=slot_id, slot_name="Test Slot")
        lot = ParkingLot(id=lot_id, name="Test Lot", slots=[slot])
        
        with patch.object(storage, 'get_parking_lot', return_value=lot), \
             patch.object(storage, '_create_and_persist_event', return_value=True) as mock_create_event:
            # Act
            result = storage.update_slot_zone(lot_id, slot_id, zone_points)
            
            # Assert
            assert result is True
            mock_create_event.assert_called_once()
            assert mock_create_event.call_args.args[0] == ParkingSlotZoneUpdated
            assert mock_create_event.call_args.kwargs['lot_id'] == lot_id
            assert mock_create_event.call_args.kwargs['payload'] == {
                'slot_id': slot_id,
                'new_zone_points': zone_points
            }

    def test_update_slot_threshold_invalid_range(self, storage):
        """Test updating slot threshold with invalid value."""
        # Arrange
        lot_id = str(uuid.uuid4())
        slot_id = str(uuid.uuid4())
        
        # Act
        result = storage.update_slot_threshold(lot_id, slot_id, 1.5)  # Value > 1.0
        
        # Assert
        assert result is False

    def test_update_slot_threshold_nonexistent_lot_or_slot(self, storage):
        """Test updating threshold for non-existent lot or slot."""
        # Arrange
        lot_id = str(uuid.uuid4())
        slot_id = str(uuid.uuid4())
        
        with patch.object(storage, 'get_parking_lot', return_value=None):
            # Act
            result = storage.update_slot_threshold(lot_id, slot_id, 0.5)
            
            # Assert
            assert result is False

    def test_update_slot_threshold(self, storage):
        """Test updating slot threshold for a valid slot."""
        # Arrange
        lot_id = str(uuid.uuid4())
        slot_id = str(uuid.uuid4())
        new_threshold = 0.6
        
        # Create a lot with a slot
        slot = ParkingSlot(id=slot_id, slot_name="Test Slot")
        lot = ParkingLot(id=lot_id, name="Test Lot", slots=[slot])
        
        with patch.object(storage, 'get_parking_lot', return_value=lot), \
             patch.object(storage, '_create_and_persist_event', return_value=True) as mock_create_event:
            # Act
            result = storage.update_slot_threshold(lot_id, slot_id, new_threshold)
            
            # Assert
            assert result is True
            mock_create_event.assert_called_once()
            assert mock_create_event.call_args.args[0] == ParkingSlotThresholdUpdated
            assert mock_create_event.call_args.kwargs['lot_id'] == lot_id
            assert mock_create_event.call_args.kwargs['payload'] == {
                'slot_id': slot_id,
                'new_overlap_threshold': new_threshold
            }

    def test_update_slot_occupancy_nonexistent_lot_or_slot(self, storage):
        """Test updating occupancy for non-existent lot or slot."""
        # Arrange
        lot_id = str(uuid.uuid4())
        slot_id = str(uuid.uuid4())
        
        with patch.object(storage, 'get_parking_lot', return_value=None):
            # Act
            result = storage.update_slot_occupancy(lot_id, slot_id, True)
            
            # Assert
            assert result is False

    def test_update_slot_occupancy(self, storage):
        """Test updating slot occupancy for a valid slot."""
        # Arrange
        lot_id = str(uuid.uuid4())
        slot_id = str(uuid.uuid4())
        is_occupied = True
        detection_timestamp = datetime.now()
        
        # Create a lot with a slot
        slot = ParkingSlot(id=slot_id, slot_name="Test Slot")
        lot = ParkingLot(id=lot_id, name="Test Lot", slots=[slot])
        
        with patch.object(storage, 'get_parking_lot', return_value=lot), \
             patch.object(storage, '_create_and_persist_event', return_value=True) as mock_create_event:
            # Act
            result = storage.update_slot_occupancy(lot_id, slot_id, is_occupied, detection_timestamp)
            
            # Assert
            assert result is True
            mock_create_event.assert_called_once()
            assert mock_create_event.call_args.args[0] == ParkingSlotOccupancyChanged
            assert mock_create_event.call_args.kwargs['lot_id'] == lot_id
            payload = mock_create_event.call_args.kwargs['payload']
            assert payload["slot_id"] == slot_id
            assert payload["is_occupied"] == is_occupied
            assert payload["detection_timestamp"] == detection_timestamp.isoformat()

    def test_remove_parking_slot_nonexistent_lot_or_slot(self, storage):
        """Test removing a non-existent slot."""
        # Arrange
        lot_id = str(uuid.uuid4())
        slot_id = str(uuid.uuid4())
        
        with patch.object(storage, 'get_parking_lot', return_value=None):
            # Act
            result = storage.remove_parking_slot(lot_id, slot_id)
            
            # Assert
            assert result is False

    def test_remove_parking_slot(self, storage):
        """Test removing a valid parking slot."""
        # Arrange
        lot_id = str(uuid.uuid4())
        slot_id = str(uuid.uuid4())
        # Create a lot with a slot
        slot = ParkingSlot(id=slot_id, slot_name="Test Slot")
        lot = ParkingLot(id=lot_id, name="Test Lot", slots=[slot])
        
        with patch.object(storage, 'get_parking_lot', return_value=lot), \
             patch.object(storage, '_create_and_persist_event', return_value=True) as mock_create_event:
            # Act
            result = storage.remove_parking_slot(lot_id, slot_id)
            
            # Assert
            assert result is True
            mock_create_event.assert_called_once()
            assert mock_create_event.call_args.args[0] == ParkingSlotRemoved
            assert mock_create_event.call_args.kwargs['lot_id'] == lot_id
            assert mock_create_event.call_args.kwargs['payload'] == {'slot_id': slot_id}

    def test_create_detection_config_nonexistent_lot(self, storage):
        """Test creating detection config for non-existent lot."""
        # Arrange
        lot_id = str(uuid.uuid4())
        
        with patch.object(storage, 'get_parking_lot', return_value=None):
            # Act
            result = storage.create_detection_config(lot_id, "yolo")
            
            # Assert
            assert result is False

    def test_create_detection_config_new(self, storage):
        """Test creating a new detection config."""
        # Arrange
        lot_id = str(uuid.uuid4())
        engine_type = "yolo"
        is_enabled = True
        update_interval = 60
        parameters = {"confidence": 0.5}
        
        # Lot without existing config
        lot = ParkingLot(id=lot_id, name="Test Lot", detection_config=None)
        
        with patch.object(storage, 'get_parking_lot', return_value=lot), \
             patch.object(storage, '_create_and_persist_event', return_value=True) as mock_create_event:
            # Act
            result = storage.create_detection_config(
                lot_id, engine_type, is_enabled, update_interval, parameters
            )
            
            # Assert
            assert result is True
            mock_create_event.assert_called_once()
            assert mock_create_event.call_args.args[0] == DetectionConfigCreated
            assert mock_create_event.call_args.kwargs['lot_id'] == lot_id
            payload = mock_create_event.call_args.kwargs['payload']
            assert payload["engine_type"] == engine_type
            assert payload["is_enabled"] == is_enabled
            assert payload["update_interval"] == update_interval
            assert payload["parameters"] == parameters

    def test_create_detection_config_update(self, storage):
        """Test updating an existing detection config."""
        # Arrange
        lot_id = str(uuid.uuid4())
        engine_type = "yolo"
        is_enabled = True
        update_interval = 60
        
        # Lot with existing config
        config = DetectionEngineConfig(engine_type="old_engine", is_enabled=False, update_interval=30)
        lot = ParkingLot(id=lot_id, name="Test Lot", detection_config=config)
        
        with patch.object(storage, 'get_parking_lot', return_value=lot), \
             patch.object(storage, '_create_and_persist_event', return_value=True) as mock_create_event:
            # Act
            result = storage.create_detection_config(lot_id, engine_type, is_enabled, update_interval)
            
            # Assert
            assert result is True
            mock_create_event.assert_called_once()
            assert mock_create_event.call_args.args[0] == DetectionConfigUpdated
            assert mock_create_event.call_args.kwargs['lot_id'] == lot_id

    def test_delete_detection_config_nonexistent_lot(self, storage):
        """Test deleting detection config from non-existent lot."""
        # Arrange
        lot_id = str(uuid.uuid4())
        
        with patch.object(storage, 'get_parking_lot', return_value=None):
            # Act
            result = storage.delete_detection_config(lot_id)
            
            # Assert
            assert result is False

    def test_delete_detection_config_no_config(self, storage):
        """Test deleting non-existent detection config."""
        # Arrange
        lot_id = str(uuid.uuid4())
        
        # Lot without config
        lot = ParkingLot(id=lot_id, name="Test Lot", detection_config=None)
        
        with patch.object(storage, 'get_parking_lot', return_value=lot):
            # Act
            result = storage.delete_detection_config(lot_id)
            
            # Assert
            assert result is False

    def test_delete_detection_config(self, storage):
        """Test deleting an existing detection config."""
        # Arrange
        lot_id = str(uuid.uuid4())
        
        # Lot with config
        config = DetectionEngineConfig(engine_type="yolo", is_enabled=True, update_interval=30)
        lot = ParkingLot(id=lot_id, name="Test Lot", detection_config=config)
        
        with patch.object(storage, 'get_parking_lot', return_value=lot), \
             patch.object(storage, '_create_and_persist_event', return_value=True) as mock_create_event:
            # Act
            result = storage.delete_detection_config(lot_id)
            
            # Assert
            assert result is True
            mock_create_event.assert_called_once()
            assert mock_create_event.call_args.args[0] == DetectionConfigDeleted
            assert mock_create_event.call_args.kwargs['lot_id'] == lot_id
            assert mock_create_event.call_args.kwargs['payload'] == {}

    def test_update_parking_lot_deprecated(self, storage):
        """Test that deprecated update_parking_lot raises NotImplementedError."""
        # Arrange
        lot_id = str(uuid.uuid4())
        lot = ParkingLot(id=lot_id, name="Test Lot")
        
        # Act & Assert
        with pytest.raises(NotImplementedError):
            storage.update_parking_lot(lot_id, lot)