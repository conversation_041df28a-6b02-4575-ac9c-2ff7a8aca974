# type: ignore

import json

from behave import *
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
import uuid
import time
import random
import requests
from app.src.models.parking import ParkingLot, ParkingSlot
from app.src.models.geometry import Point, Polygon
from app.src.utils.storage import Storage
from app.src.services.occupancy_manager import OccupancyManager

use_step_matcher("re")


@given(r'the parking lot "(?P<lot_name>.*)" has (?P<slot_count>\d+) configured parking slots')
def step_impl(context, lot_name, slot_count):
    """Create a test parking lot with the specified number of slots"""
    # First create a parking lot through the upload process
    context.execute_steps('''
        Given the user is on the image upload page
        When the user selects a valid JPEG image file
        And the user clicks the upload button
        Then the system should display the image in the zone editor
    ''')

    # Extract lot_id from URL
    current_url = context.driver.current_url
    # Extract lot ID from between /lot/ and /edit
    import re
    match = re.search(r'/lot/([^/]+)/edit', current_url)
    assert match, "Could not extract lot ID from URL"
    context.lot_id = match.group(1)

    # Get the lot and add the specified number of slots
    lot = Storage.get_parking_lot(context.lot_id)
    assert lot is not None, f"Failed to create parking lot {lot_name}"

    # Clear existing slots if any
    lot.slots.clear()
    Storage.update_parking_lot(context.lot_id, lot)

    # Add slots directly via API calls
    slot_count = int(slot_count)
    get_x = lambda x: (x // 5) * 100 + 50
    get_y = lambda x: (x % 5) * 100 + 50
    slot_data = [{"id": f"S{i}", "points": [{"x": get_x(i), "y": get_y(i)},
                                            {"x": get_x(i) + 80, "y": get_y(i)},
                                            {"x": get_x(i) + 80, "y": get_y(i) + 80},
                                            {"x": get_x(i), "y": get_y(i) + 80}]}
                 for i in range(1, slot_count + 1)]


    # Make direct API call instead of using UI
    response = requests.post(
        f"{context.base_url}/api/lot/{context.lot_id}/slots",
        json=slot_data
    )
    assert response.status_code == 201, f"Failed to create slots for lot {context.lot_id}: {response.text}"

    # Refresh page to show the new slots
    context.driver.refresh()
    time.sleep(1)

    # Verify slots were created
    lot = Storage.get_parking_lot(context.lot_id, refresh=True)
    assert lot is not None, "Parking lot disappeared after adding slots"
    assert len(lot.slots) == slot_count, f"Expected {slot_count} slots, got {len(lot.slots)}"

    # Store lot info for later steps
    context.lot_name = lot_name
    context.slot_count = slot_count
    context.lot_id = lot.id

    print(f"Successfully created lot with ID: {context.lot_id} containing {len(lot.slots)} slots")


@step(r"(?P<occupied_count>\d+) slots are currently occupied")
def step_impl(context, occupied_count):
    """Set the specified number of slots to occupied status"""
    occupied_count = int(occupied_count)
    lot = Storage.get_parking_lot(context.lot_id)
    assert lot is not None, f"Lot {context.lot_id} not found"

    # Make sure we don't try to occupy more slots than available
    if occupied_count > len(lot.slots):
        raise ValueError(f"Cannot occupy {occupied_count} slots when only {len(lot.slots)} exist")

    # Reset all slots to unoccupied first
    for slot in lot.slots:
        slot.is_occupied = False

    # Randomly select slots to be occupied
    occupied_slots = random.sample(lot.slots, occupied_count)
    for slot in occupied_slots:
        slot.is_occupied = True

    # Store lot again to ensure persistence
    Storage.update_parking_lot(context.lot_id, lot)

    # Verify the changes took effect
    updated_lot = Storage.get_parking_lot(context.lot_id)
    actual_occupied = sum(1 for slot in updated_lot.slots if slot.is_occupied)
    assert actual_occupied == occupied_count, f"Expected {occupied_count} occupied slots, got {actual_occupied}"

    # Store occupied count for later verification
    context.occupied_count = occupied_count
    context.free_count = context.slot_count - occupied_count
    context.free_percentage = (context.free_count / context.slot_count) * 100


@when("the user views the dashboard")
def step_impl(context):
    """Navigate to the dashboard view for the test parking lot"""
    # Navigate to the dashboard for our test lot
    context.driver.get(f"{context.base_url}/dashboard/{context.lot_id}")

    # Wait for dashboard to load
    WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.CLASS_NAME, "card-body"))
    )


@given("the parking lot has both free and occupied slots")
def step_impl(context):
    """Ensure the test parking lot has a mix of free and occupied slots"""
    if not hasattr(context, 'lot_id'):
        # Create a default test lot if not already created
        context.execute_steps('''
            Given the parking lot "Downtown" has 10 configured parking slots
            And 5 slots are currently occupied
        ''')
    else:
        # Make sure we have both free and occupied slots
        lot = Storage.get_parking_lot(context.lot_id)

        # Count current occupied slots
        occupied_count = sum(1 for slot in lot.slots if slot.is_occupied)

        # If all slots are either free or occupied, reset to have a mix
        if occupied_count == 0 or occupied_count == len(lot.slots):
            # Set half of slots to occupied
            target_occupied = len(lot.slots) // 2
            for i, slot in enumerate(lot.slots):
                slot.is_occupied = i < target_occupied


@step('the user selects the "Show only free slots" filter')
def step_impl(context):
    """Find and click the filter to show only free slots"""
    element_id = "filter-free-only"
    filter_checkbox = WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.ID, element_id))
    )

    # Click the filter checkbox
    filter_checkbox.click()
    time.sleep(1)  # Wait for filter to take effect

    checkbox_value = filter_checkbox.get_attribute("checked")
    assert checkbox_value is not None, "Filter checkbox should be checked after clicking"


@then("the dashboard should only display slots that are currently free")
def step_impl(context):
    """Verify that only free slots are displayed in the slots table"""
    # Check that all visible slots have the "free" status
    slot_rows = WebDriverWait(context.driver, 10).until(
        EC.presence_of_all_elements_located((By.CSS_SELECTOR, "tr[data-slot-id]:not(.d-none)"))
    )

    # Verify we have the right number of visible slots
    assert len(slot_rows) == context.free_count, \
        f"Expected {context.free_count} free slots visible, but found {len(slot_rows)}"

    # Verify each visible slot has "Free" status
    for row in slot_rows:
        status_badge = row.find_element(By.CSS_SELECTOR, ".status-badge")
        assert "free" in status_badge.get_attribute("class"), \
            f"Found a visible slot that doesn't have 'free' status: {row.get_attribute('data-slot-id')}"
        assert status_badge.text == "Free", \
            f"Found a visible slot with incorrect status text: {status_badge.text}"


@then(r"the dashboard should show (?P<slot_count>\d+) slots occupied")
def step_impl(context, slot_count):
    element_id = "occupied-count"
    occupied_count = int(slot_count)

    # Check that the occupied count is displayed correctly

    occupied_count_element = WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.ID, element_id))
    )

    assert occupied_count_element.text == str(occupied_count), f"Expected {occupied_count} occupied slots, got {occupied_count_element.text}"


@step(r"the dashboard should show (?P<slot_count>\d+) slots available")
def step_impl(context, slot_count):
    element_id = "free-count"
    free_count = int(slot_count)

    # Check that the free count is displayed correctly

    free_count_element = WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.ID, element_id))
    )

    assert free_count_element.text == str(free_count), f"Expected {free_count} free slots, got {free_count_element.text}"


@then("each slot should display its current status")
def step_impl(context):
    """Verify each slot shows its status"""
    # Wait for slot elements to be visible
    slot_rows = WebDriverWait(context.driver, 10).until(
        EC.presence_of_all_elements_located((By.CSS_SELECTOR, "tr[data-slot-id]"))
    )

    # Verify we have the expected number of slots
    assert len(slot_rows) == context.slot_count, \
        f"Expected {context.slot_count} slot rows, but found {len(slot_rows)}"

    # Verify each slot has a status indicator
    for row in slot_rows:
        status_badge = row.find_element(By.CSS_SELECTOR, ".status-badge")
        assert status_badge.is_displayed(), \
            f"Status badge not displayed for slot {row.get_attribute('data-slot-id')}"
        assert status_badge.text in ["Free", "Occupied"], \
            f"Invalid status text: {status_badge.text}"


@then("occupied slots should be highlighted in red")
def step_impl(context):
    """Verify occupied slots have red highlighting"""
    # Find all occupied slots
    occupied_slots = WebDriverWait(context.driver, 10).until(
        EC.presence_of_all_elements_located((By.CSS_SELECTOR, "tr[data-slot-id] .status-badge.occupied"))
    )
    occupied_attr = "occupied"

    # Verify each occupied slot has appropriate styling
    for badge in occupied_slots:
        assert occupied_attr in badge.get_attribute("class"), f"Occupied slot is not highlighted in red: {badge.get_attribute('class')}"


@then("free slots should be highlighted in green")
def step_impl(context):
    """Verify free slots have green highlighting"""
    # Find all free slots
    free_slots = WebDriverWait(context.driver, 10).until(
        EC.presence_of_all_elements_located((By.CSS_SELECTOR, "tr[data-slot-id] .status-badge.free"))
    )

    free_attr = "free"

    # Verify each free slot has appropriate styling
    for badge in free_slots:
        assert free_attr in badge.get_attribute("class"), f"Free slot is not highlighted in green: {badge.get_attribute('class')}"


@given(r'the parking lot "(?P<lot_name>.*)" has a slot "(?P<slot_id>.*)" that is currently free')
def step_impl(context, lot_name, slot_id):
    """Ensure a specific slot in the parking lot is set as free"""
    # Use existing lot if already created in previous steps
    if not hasattr(context, 'lot_id'):
        context.execute_steps(f'''
            Given the parking lot "{lot_name}" has 10 configured parking slots
        ''')

    # Get the lot
    lot = Storage.get_parking_lot(context.lot_id)

    # Find the slot with the specified ID
    target_slot = next((slot for slot in lot.slots if slot.id == slot_id), None)

    if target_slot:
        # Ensure the slot is free
        target_slot.is_occupied = False
        Storage.update_parking_lot(context.lot_id, lot)
    else:
        # If the slot doesn't exist, we need to add it via the API
        slot_data = [{
            "id": slot_id,
            "points": [
                {"x": 100, "y": 100},
                {"x": 180, "y": 100},
                {"x": 180, "y": 180},
                {"x": 100, "y": 180}
            ]
        }]

        response = requests.post(
            f"{context.base_url}/api/lot/{context.lot_id}/slots",
            json=slot_data
        )
        assert response.status_code == 201, f"Failed to create slot {slot_id}: {response.text}"

    context.test_slot_id = slot_id


@when(r'the detection engine identifies a vehicle in slot "(?P<slot_id>.*)"')
def step_impl(context, slot_id):
    """Simulate detection engine finding a vehicle in a slot"""
    # Make API call to update the slot status
    response = requests.put(
        f"{context.base_url}/api/lot/{context.lot_id}/slots/{slot_id}/occupy",
        json={"occupied": True}
    )
    assert response.status_code in [200, 201], \
        f"Failed to update slot status: {response.status_code} - {response.text}"
    time.sleep(1)  # Wait for updates to process


@then(r'the system should update slot "(?P<slot_id>.*)" status to "(?P<status>.*)"')
def step_impl(context, slot_id, status):
    """Verify the slot status was updated correctly in the system"""
    # Navigate to lot dashboard
    context.driver.get(f"{context.base_url}/dashboard/{context.lot_id}")

    # Find the specific slot row
    slot_row = WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, f"tr[data-slot-id='{slot_id}']"))
    )

    # Check the status badge text
    status_badge = slot_row.find_element(By.CSS_SELECTOR, ".status-badge")
    expected_status = "Occupied" if status.lower() == "occupied" else "Free"
    assert status_badge.text == expected_status, \
        f"Expected status '{expected_status}', got '{status_badge.text}'"


@then(r'slot "(?P<slot_id>.*)" should change color to red on the dashboard')
def step_impl(context, slot_id):
    """Verify the slot changes color on the dashboard"""
    # Ensure we are on the correct dashboard
    current_url = context.driver.current_url
    assert f"/dashboard/{context.lot_id}" in current_url, "Not on the correct dashboard"
    # Find the specific slot row
    slot_row = WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, f"tr[data-slot-id='{slot_id}']"))
    )

    # Check the status badge color
    status_badge = slot_row.find_element(By.CSS_SELECTOR, ".status-badge")
    assert "occupied" in status_badge.get_attribute("class"), \
        "Slot status does not indicate occupied"


@given("the user has administrator access")
def step_impl(context):
    """Ensure the user is logged in with admin privileges"""
    # to be done, assume user is already logged in as admin
    pass


@when("the user configures a detection engine implementation")
def step_impl(context):
    """Configure a detection engine in the admin settings"""
    # Navigate to engine configuration page
    context.driver.get(f"{context.base_url}/dashboard/{context.lot_id}/configure")

    # wait for the page to load
    WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.CLASS_NAME, "card-body"))
    )

    # Select engine type `Mock` from dropdown
    engine_type = context.driver.find_element(By.CSS_SELECTOR, "#engine_type")
    already_selected_option = engine_type.find_element(By.CSS_SELECTOR, "option[selected]")
    if already_selected_option.text != "Mock":
        engine_type.find_element(By.CSS_SELECTOR, "option[value='Mock']").click()
    # Set interval to 30 seconds
    update_interval = context.driver.find_element(By.CSS_SELECTOR, "#update_interval")
    update_interval.clear()
    update_interval.send_keys("30")

    # Save configuration
    save_button = context.driver.find_element(By.CSS_SELECTOR, "button[type='submit']")

    # Scroll to the save button
    actions = ActionChains(context.driver)
    actions.move_to_element(save_button).perform()

    save_button.click()

    # Wait for confirmation message
    alert = WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, ".alert"))
    )
    alert_text = alert.text

    assert "Detection engine configured successfully" in alert_text, f"Failed to configure engine: {alert_text}"


@when("the user enables automatic detection")
def step_impl(context):
    """Enable automatic detection in the system"""
    # Go to the detection settings page
    context.driver.get(f"{context.base_url}/dashboard/{context.lot_id}/configure")
    # Find the toggle switch for automatic detection
    element_id = "is_enabled"
    auto_detect_toggle = WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.ID, element_id))
    )

    # Click to enable if not already enabled
    if not auto_detect_toggle.is_selected():
        auto_detect_toggle.click()

    # Save the settings
    save_button = context.driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
    # Scroll to the save button
    ActionChains(context.driver).move_to_element(save_button).perform()

    save_button.click()

    # Wait for confirmation
    alert = WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, ".alert"))
    )
    alert_text = alert.text

    assert "Detection engine configured successfully" in alert_text, f"Failed to enable automatic detection: {alert_text}"


@then("the system should use the detection engine to update slot statuses")
def step_impl(context):
    """Verify the system is using the detection engine for status updates"""
    # Navigate to the dashboard
    context.driver.get(f"{context.base_url}/dashboard/{context.lot_id}")

    # Verify detection engine status is shown as enabled
    engine_status = WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, "#engine-status"))
    )

    # Check that the status shows "Enabled"
    assert "Enabled" in engine_status.text, "Detection engine is not shown as enabled"

    # Verify detection engine type is shown
    engine_type = WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, "#engine-type"))
    )

    assert "mock" in engine_type.text, "Detection engine type is not correctly displayed"


@step(r'the user is logged in as "(?P<role>.*)"')
def step_impl(context, role):
    pass


@step(r'the user clicks the "(?P<element_text>.*)" button')
def step_impl(context, element_text):
    locator = (By.XPATH, f"//button[text()='{element_text}']")

    button = WebDriverWait(context.driver, 10).until(
        EC.element_to_be_clickable(locator)
    )

    assert button.is_displayed(), f"Button '{element_text}' is not visible"

    button.click()


@then("the system should replace the existing parking lot image")
def step_impl(context):
    """Verify the system updates the parking lot image"""
    image_id = "parking-image"
    old_image_url = context.uploaded_image_url
    # Wait for the image to be updated
    img = WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.ID, image_id))
    )

    # get the new image url
    new_image_url = img.get_attribute("src")

    assert new_image_url != old_image_url, "Image URL did not change after update"


@step("the system should preserve the parking lot slots")
def step_impl(context):
    lot_id = context.lot_id
    # Get the lot from storage
    lot = Storage.get_parking_lot(lot_id)
    assert lot is not None, f"Parking lot {lot_id} not found"

    # Verify that the lot still has the same number of slots
    assert len(lot.slots) == context.slot_count, \
        f"Expected {context.slot_count} slots, found {len(lot.slots)}"