"""
FastAPI router for zone editing routes
Migrated from routes/zones.py
"""

from fastapi import APIRouter, Request, HTTPException, Depends
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates

# Import business logic (unchanged from Flask)
from src.utils.storage import StorageService
from src.utils.logger import setup_logger
from src.dependencies import get_templates, get_base_template_context, get_socketio

# Initialize logger
LOGGER = setup_logger(__name__)

# Create router with prefix to match Flask blueprint
router = APIRouter(prefix="/lot", tags=["zones"])

@router.get("/{lot_id}/edit", response_class=HTMLResponse)
async def edit_zones(
    request: Request,
    lot_id: str,
    templates: Jinja2Templates = Depends(get_templates),
    socketio = Depends(get_socketio)
):
    """Page for editing parking slot zones on an image."""
    try:
        lot = StorageService.get_parking_lot(lot_id)
        if not lot:
            LOGGER.warning(f"Parking lot with ID {lot_id} not found for zone editing")
            # Emit notification via SocketIO instead of flash
            await socketio.emit('notification', {
                'type': 'error',
                'message': 'Parking lot not found'
            }, namespace='/dashboard')
            return RedirectResponse(url="/upload", status_code=302)

        # Get the default viewpoint
        default_viewpoint = None
        if lot.default_viewpoint_id:
            default_viewpoint = lot.get_viewpoint(lot.default_viewpoint_id)

        context = get_base_template_context(request)
        context.update({
            "lot": lot,
            "lot_id": lot_id,
            "default_viewpoint": default_viewpoint
        })

        return templates.TemplateResponse('zone_editor.html', context)
    except Exception as e:
        LOGGER.error(f"Error loading zone editor for lot {lot_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
