Feature: Parking Slot Detection Zone Configuration
  As a parking manager
  I want to draw detection zones on the parking lot image
  So that the system knows where to detect vehicles

  Scenario: Create new parking slot zone
    Given the parking lot image is displayed in the zone editor
    When the user selects "Add Parking Slot" option
    And the user draws a polygon on the image
    And the user assigns ID "A1" to the parking slot
    And the user saves the parking slot
    Then the system should save the polygon coordinates for slot "A1"
    And the system should display the zone with label "A1" on the image

  Scenario: Edit existing parking slot zone
    Given the parking lot has a parking slot with ID "A1"
    When the user selects slot "A1" in the zone editor
    And the user modifies the polygon shape
    And the user saves the changes
    Then the system should update the polygon coordinates for slot "A1"
    And the system should display the updated zone on the image

  Scenario: Delete parking slot zone
    Given the parking lot has a parking slot with ID "A1"
    When the user selects slot "A1" in the zone editor
    And the user chooses to delete the slot
    And the user confirms the deletion
    Then the system should remove slot "A1" from the parking lot
    And the zone should no longer appear on the image