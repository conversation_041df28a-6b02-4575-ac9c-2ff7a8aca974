from src.models.detection import DetectionEngine, DetectionResult
from src.utils.storage import StorageService
import random
from typing import Dict, List, Optional


class MockDetectionEngine(DetectionEngine):
    """
    A mock detection engine for testing and demonstration.
    Randomly assigns occupancy statuses to parking slots.
    In test mode, alternates between occupied and free states.
    """

    def __init__(self):
        self.config = {
            "random_seed": 42,
            "probability_threshold": 0.8,  # Threshold for considering a slot occupied
            "min_confidence": 0.7,  # Minimum confidence score
            "max_confidence": 0.98,  # Maximum confidence score
        }
        # Store previous results to alternate states in tests
        self._previous_results = {}

    def detect(self, parking_lot_id: str, image_path: Optional[str] = None) -> List[DetectionResult]:
        """Generate mock detection results for all slots in parking lot."""
        try:
            parking_lot = StorageService.get_parking_lot(parking_lot_id)
            if not parking_lot:
                return []

            # Get image path if not provided
            if not image_path:
                image_path = self._get_image_for_lot(parking_lot_id)
                if not image_path:
                    return []

            random.seed(self.config["random_seed"])

            results = []
            for slot in parking_lot.slots:
                # Generate a random confidence score
                confidence = random.uniform(self.config["min_confidence"],
                                            self.config["max_confidence"])

                # Check if we have a previous result for this slot
                if slot.id in self._previous_results:
                    # Alternate the state from the previous result
                    is_occupied = not self._previous_results[slot.id]
                else:
                    # First time, determine occupancy based on random value and threshold
                    is_occupied = random.random() >= self.config["probability_threshold"]

                # Store the current state for next time
                self._previous_results[slot.id] = is_occupied

                results.append(DetectionResult(
                    slot_id=slot.id,
                    is_occupied=is_occupied,
                    confidence=confidence
                ))

            return results
        except Exception as e:
            # Return empty list for any exceptions
            return []

    def get_configuration(self) -> Dict:
        """Get current configuration."""
        return self.config

    def configure(self, config: Dict) -> bool:
        """Update configuration parameters."""
        for key, value in config.items():
            if key in self.config:
                self.config[key] = value
        return True

    def is_available(self) -> bool:
        """Mock engine is always available."""
        return True

    def get_classes(self) -> List[str]:
        """Return a list of classes the mock engine can detect."""
        return ["car", "motorcycle", "bicycle", "bus", "truck"]

    def reset_state(self):
        """Reset the engine's state for testing."""
        # Reset the random seed to ensure consistent test results
        random.seed(self.config["random_seed"])
        # Clear previous results to start fresh
        self._previous_results = {}

    def _get_image_for_lot(self, lot_id: str) -> str:
        """Get the image path for a parking lot.

        This method exists to be mocked in tests.
        """
        parking_lot = StorageService.get_parking_lot(lot_id)
        if not parking_lot:
            return ""
        return parking_lot.image_path
