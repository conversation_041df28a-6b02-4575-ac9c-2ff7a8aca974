"""
Integration tests for the dashboard grouped slot statistics functionality
"""
import pytest
import json
from unittest.mock import Mock, patch
import uuid
from fastapi.testclient import TestClient

from app.src.models.parking import ParkingLot, ParkingSlot


class TestDashboardGroupedStatsIntegration:
    """Integration tests for dashboard grouped slot statistics"""

    @pytest.fixture
    def app(self):
        """Create a test FastAPI app"""
        from app.main import create_app
        return create_app()

    @pytest.fixture
    def client(self, app):
        """Create a test client"""
        return TestClient(app)

    @pytest.fixture
    def sample_lot_with_duplicates(self):
        """Create a sample parking lot with duplicate slot names across viewpoints"""
        lot = ParkingLot(id=str(uuid.uuid4()), name="Test Lot")
        
        # Simulate the real-world scenario: same slot names in different viewpoints
        slots = [
            # Slot "643" appears in two viewpoints
            ParkingSlot(id="slot1", slot_name="643", is_occupied=False, viewpoint_id="default"),
            ParkingSlot(id="slot2", slot_name="643", is_occupied=False, viewpoint_id="alt"),
            # Slot "639" appears only in default viewpoint
            ParkingSlot(id="slot3", slot_name="639", is_occupied=False, viewpoint_id="default"),
        ]
        
        for slot in slots:
            lot.slots.append(slot)
        
        return lot

    @patch('app.src.utils.storage.StorageService.get_parking_lot')
    def test_dashboard_page_uses_grouped_stats(self, mock_get_lot, client, sample_lot_with_duplicates):
        """Test that the dashboard page uses grouped stats for initial display"""
        # Arrange
        lot_id = sample_lot_with_duplicates.id
        mock_get_lot.return_value = sample_lot_with_duplicates

        # Act
        response = client.get(f'/dashboard/{lot_id}')

        # Assert
        assert response.status_code == 200
        html_content = response.text

        # Should show 2 available spots (2 unique slot groups)
        assert 'id="free-count">2<' in html_content
        # Should show 0 occupied spots
        assert 'id="occupied-count">0<' in html_content
        # Should show 0% occupancy
        assert 'style="width: 0.0%;"' in html_content
        assert '>0.0% Occupied<' in html_content

    @patch('app.src.utils.storage.StorageService.get_parking_lot')
    def test_dashboard_grouped_stats_with_occupied_slots(self, mock_get_lot, client):
        """Test dashboard grouped stats when some slot groups are occupied"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Partially Occupied Lot")

        slots = [
            # Group "A1" - occupied (any instance occupied makes group occupied)
            ParkingSlot(id="slot1", slot_name="A1", is_occupied=True, viewpoint_id="vp1"),
            ParkingSlot(id="slot2", slot_name="A1", is_occupied=False, viewpoint_id="vp2"),
            # Group "A2" - free
            ParkingSlot(id="slot3", slot_name="A2", is_occupied=False, viewpoint_id="vp1"),
            # Group "A3" - occupied
            ParkingSlot(id="slot4", slot_name="A3", is_occupied=True, viewpoint_id="vp1"),
        ]

        for slot in slots:
            lot.slots.append(slot)

        mock_get_lot.return_value = lot

        # Act
        response = client.get(f'/dashboard/{lot.id}')

        # Assert
        assert response.status_code == 200
        html_content = response.text

        # Should show 1 available spot (1 free group: A2)
        assert 'id="free-count">1<' in html_content
        # Should show 2 occupied spots (2 occupied groups: A1, A3)
        assert 'id="occupied-count">2<' in html_content
        # Should show 66.7% occupancy (2/3 groups occupied)
        assert 'style="width: 66.7%;"' in html_content
        assert '>66.7% Occupied<' in html_content

    @patch('app.src.utils.storage.StorageService.get_parking_lot')
    def test_dashboard_api_consistency(self, mock_get_lot, client, sample_lot_with_duplicates):
        """Test that dashboard and API return consistent grouped stats"""
        # Arrange
        lot_id = sample_lot_with_duplicates.id
        mock_get_lot.return_value = sample_lot_with_duplicates

        # Act - Get dashboard page
        dashboard_response = client.get(f'/dashboard/{lot_id}')
        dashboard_html = dashboard_response.text

        # Act - Get API stats
        api_response = client.get(f'/api/lots/{lot_id}/grouped-stats')
        api_data = api_response.json()

        # Assert - Both should show the same stats
        assert dashboard_response.status_code == 200
        assert api_response.status_code == 200

        # Extract stats from dashboard HTML
        assert f'id="free-count">{api_data["available_groups"]}<' in dashboard_html
        assert f'id="occupied-count">{api_data["occupied_groups"]}<' in dashboard_html
        assert f'style="width: {api_data["occupancy_rate"]}%;"' in dashboard_html

    @patch('app.src.utils.storage.StorageService.get_parking_lot')
    def test_dashboard_viewport_independence(self, mock_get_lot, client):
        """Test that dashboard stats are independent of viewport configuration"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Multi-Viewport Lot")
        
        # Create a complex scenario with multiple viewpoints
        slots = [
            # Slot "PARKING_A" in 3 different viewpoints
            ParkingSlot(id="slot1", slot_name="PARKING_A", is_occupied=True, viewpoint_id="front"),
            ParkingSlot(id="slot2", slot_name="PARKING_A", is_occupied=False, viewpoint_id="back"),
            ParkingSlot(id="slot3", slot_name="PARKING_A", is_occupied=False, viewpoint_id="side"),
            # Slot "PARKING_B" in 2 viewpoints
            ParkingSlot(id="slot4", slot_name="PARKING_B", is_occupied=False, viewpoint_id="front"),
            ParkingSlot(id="slot5", slot_name="PARKING_B", is_occupied=False, viewpoint_id="back"),
            # Slot "PARKING_C" in 1 viewpoint only
            ParkingSlot(id="slot6", slot_name="PARKING_C", is_occupied=True, viewpoint_id="front"),
        ]
        
        for slot in slots:
            lot.slots.append(slot)
        
        mock_get_lot.return_value = lot
        
        # Act
        response = client.get(f'/dashboard/{lot.id}')
        
        # Assert
        assert response.status_code == 200
        html_content = response.text
        
        # Should show 3 total groups: PARKING_A, PARKING_B, PARKING_C
        # 2 occupied groups: PARKING_A (any instance occupied), PARKING_C (occupied)
        # 1 free group: PARKING_B (all instances free)
        assert 'id="free-count">1<' in html_content
        assert 'id="occupied-count">2<' in html_content
        # Occupancy rate: 2/3 = 66.7%
        assert 'style="width: 66.7%;"' in html_content

    @patch('app.src.utils.storage.StorageService.get_parking_lot')
    def test_dashboard_empty_lot_handling(self, mock_get_lot, client):
        """Test dashboard behavior with an empty parking lot"""
        # Arrange
        empty_lot = ParkingLot(id=str(uuid.uuid4()), name="Empty Lot")
        mock_get_lot.return_value = empty_lot
        
        # Act
        response = client.get(f'/dashboard/{empty_lot.id}')
        
        # Assert
        assert response.status_code == 200
        html_content = response.text
        
        # Should show 0 for all stats
        assert 'id="free-count">0<' in html_content
        assert 'id="occupied-count">0<' in html_content
        assert 'style="width: 0.0%;"' in html_content
        assert '>0.0% Occupied<' in html_content

    @patch('app.src.utils.storage.StorageService.get_parking_lot')
    def test_dashboard_single_instance_groups(self, mock_get_lot, client):
        """Test dashboard with slot groups that have only single instances"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Single Instance Lot")
        
        slots = [
            ParkingSlot(id="slot1", slot_name="A1", is_occupied=True, viewpoint_id="vp1"),
            ParkingSlot(id="slot2", slot_name="A2", is_occupied=False, viewpoint_id="vp1"),
            ParkingSlot(id="slot3", slot_name="A3", is_occupied=True, viewpoint_id="vp1"),
            ParkingSlot(id="slot4", slot_name="A4", is_occupied=False, viewpoint_id="vp1"),
        ]
        
        for slot in slots:
            lot.slots.append(slot)
        
        mock_get_lot.return_value = lot
        
        # Act
        response = client.get(f'/dashboard/{lot.id}')
        
        # Assert
        assert response.status_code == 200
        html_content = response.text
        
        # Should show 4 total groups, 2 free, 2 occupied
        assert 'id="free-count">2<' in html_content
        assert 'id="occupied-count">2<' in html_content
        # Occupancy rate: 2/4 = 50%
        assert 'style="width: 50.0%;"' in html_content
        assert '>50.0% Occupied<' in html_content

    def test_dashboard_javascript_api_integration(self, client, app):
        """Test that the dashboard JavaScript can successfully call the grouped stats API"""
        # This test verifies that the API endpoint is properly registered and accessible
        # from the frontend JavaScript

        # Test that the route exists and is accessible by making a direct API call
        # This replaces the Flask url_for test since FastAPI doesn't have url_for
        test_lot_id = 'test-lot-id'

        # The API should be accessible even if the lot doesn't exist (returns 404)
        response = client.get(f'/api/lots/{test_lot_id}/grouped-stats')

        # Should get a response (either 200 with data or 404 for missing lot)
        # The important thing is that the route is registered and accessible
        assert response.status_code in [200, 404]

    @patch('app.src.utils.storage.StorageService.get_parking_lot')
    def test_dashboard_error_handling(self, mock_get_lot, client):
        """Test dashboard behavior when parking lot is not found"""
        # Arrange
        lot_id = str(uuid.uuid4())
        mock_get_lot.return_value = None
        
        # Act
        response = client.get(f'/dashboard/{lot_id}', follow_redirects=False)

        # Assert
        # Should return 404 or redirect to error page
        assert response.status_code in [404, 302]  # 302 for redirect to error page
