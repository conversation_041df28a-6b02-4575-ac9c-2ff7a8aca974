﻿/* Viewpoint Selector Component Styles */

viewpoint-selector {
    display: block;
    width: 100%;
}

.viewpoint-selector {
    position: relative;
    width: 100%;
}

.selector-button {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.selector-button:hover {
    border-color: #007bff;
}

.selector-button:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.selector-text {
    flex: 1;
    text-align: left;
}

.selector-arrow {
    margin-left: 8px;
}

.selector-button.open .selector-arrow {
    transform: rotate(180deg);
}

.dropdown {
    position: absolute;
    top: calc(100% - 1px);
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: 1px solid #007bff;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    display: none;
}

.dropdown.open {
    display: block;
}

.viewpoint-option {
    padding: 8px 12px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
    border-left: 3px solid transparent;
}

.viewpoint-option:hover {
    background-color: #f8f9fa;
}

.viewpoint-option.selected {
    background-color: #e3f2fd;
    border-left: 3px solid #007bff;
}

.viewpoint-option:last-child {
    border-bottom: none;
}

.viewpoint-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.viewpoint-icon {
    margin-right: 8px;
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.viewpoint-icon.default {
    color: #ffc107;
}

.viewpoint-name {
    font-weight: 500;
    margin-bottom: 2px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.viewpoint-badges {
    display: flex;
    align-items: center;
    gap: 4px;
}

.type-badge {
    font-size: 9px;
    font-weight: bold;
    padding: 1px 4px;
    border-radius: 3px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.type-badge.photo {
    background: #6c757d;
    color: white;
}

.type-badge.stream {
    background: #007bff;
    color: white;
}

.stream-status {
    font-size: 12px;
    margin-left: 2px;
}

.stream-status.status-active {
    color: #28a745; /* Green */
}

.stream-status.status-connecting {
    color: #ffc107; /* Yellow */
}

.stream-status.status-error {
    color: #dc3545; /* Red */
}

.stream-status.status-inactive {
    color: #6c757d; /* Gray */
}

.viewpoint-meta {
    font-size: 12px;
    color: #666;
}

.stream-info {
    font-size: 10px;
    color: #007bff;
    font-weight: 500;
    margin-top: 2px;
}

.default-badge {
    background: #ffc107;
    color: #000;
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 10px;
    font-weight: bold;
    margin-left: 4px;
}

.viewpoint-actions {
    display: flex;
    gap: 4px;
    opacity: 1;
}

.action-btn {
    background: none;
    border: none;
    padding: 4px 6px;
    cursor: pointer;
    border-radius: 3px;
    font-size: 12px;
    background-color: rgba(255, 255, 255, 0.8);
}

.action-btn:hover {
    background-color: rgba(255, 255, 255, 1);
}

.edit-btn {
    color: #007bff;
}

.delete-btn {
    color: #dc3545;
}

.add-viewpoint {
    padding: 8px 12px;
    cursor: pointer;
    color: #007bff;
    font-weight: 500;
    border-top: 1px solid #f0f0f0;
    text-align: center;
}

.add-viewpoint:hover {
    background-color: #f8f9fa;
}

.error {
    padding: 8px 12px;
    color: #dc3545;
    text-align: center;
    font-style: italic;
}

.loading {
    padding: 8px 12px;
    color: #666;
    text-align: center;
    font-style: italic;
}

/* Modal styles for viewpoint management */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: #fefefe;
    padding: 20px;
    border: 1px solid #888;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
    transition: color 0.2s ease;
}

.close:hover {
    color: #000;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-actions {
    text-align: right;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    margin-right: 10px;
}

.btn-secondary:hover {
    background-color: #545b62;
}
