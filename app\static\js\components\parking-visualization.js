/**
 * ParkingVisualization Web Component
 *
 * A reusable component for displaying parking lot images with:
 * - Parking slot overlays
 * - Zone overlays
 * - Zoom and pan functionality with minimap
 * - Real-time status updates via Socket.IO
 * - Occupancy statistics display
 * - Animation support for status changes
 *
 * Usage: <parking-visualization lot-id="123" viewpoint-id="456" show-controls="true"></parking-visualization>
 */

class ParkingVisualization extends HTMLElement {
    constructor() {
        super();

        // Initialize static cache if not already done
        if (!ParkingVisualization.slotsCache) {
            ParkingVisualization.slotsCache = new Map(); // lotId -> { slots: [], timestamp: Date }
        }
        this.attachShadow({ mode: 'open' });

        // Component state
        this.lotId = null;
        this.viewpointId = null;
        this.imageUrl = '';
        this.slots = [];
        this.zones = [];
        this.showSlots = true;
        this.showZones = true;
        this.zoomEnabled = true;
        this.showControls = true;
        this.showMinimap = true;
        this.showStats = false;
        this.loading = false;
        this.error = null;

        // Cache for all slots (shared across all instances for the same lot)
        this.allSlotsCache = null;
        this.allSlotsCacheTimestamp = null;

        // Internal references
        this.zoomManager = null;
        this.imageElement = null;
        this.imageContainer = null;
        this.minimapContainer = null;
        this.minimapImage = null;
        this.minimapViewport = null;
        this.unsubscribeFromUpdates = null;
        this.socketConnection = null;
        this.previousSlotStates = new Map();

        // Bind methods
        this.handleSocketSlotUpdate = this.handleSocketSlotUpdate.bind(this);
        this.handleSocketImageUpdate = this.handleSocketImageUpdate.bind(this);

        this.render();
        this.setupEventListeners();
    }

    connectedCallback() {
        console.log('ParkingVisualization: Connected to DOM');

        // Initialize attributes from HTML attributes
        this.lotId = this.getAttribute('lot-id');
        this.viewpointId = this.getAttribute('viewpoint-id');
        this.showSlots = this.getAttribute('show-slots') !== 'false';
        this.showZones = this.getAttribute('show-zones') !== 'false';
        this.zoomEnabled = this.getAttribute('zoom-enabled') !== 'false';
        this.showControls = this.getAttribute('show-controls') !== 'false';
        this.showMinimap = this.getAttribute('show-minimap') !== 'false';
        this.showStats = this.getAttribute('show-stats') === 'true';

        console.log('ParkingVisualization: Initial attributes - lotId:', this.lotId, 'viewpointId:', this.viewpointId);

        // Setup Socket.IO connection if available
        this.setupSocketConnection();

        // Load data if we have the required attributes
        if (this.lotId) {
            this.loadData();
        }
    }

    static get observedAttributes() {
        return ['lot-id', 'viewpoint-id', 'show-slots', 'show-zones', 'zoom-enabled', 'show-controls', 'show-minimap', 'show-stats'];
    }
    
    attributeChangedCallback(name, oldValue, newValue) {
        console.log(`ParkingVisualization: Attribute ${name} changed from "${oldValue}" to "${newValue}"`);

        switch (name) {
            case 'lot-id':
                this.lotId = newValue;
                console.log('ParkingVisualization: lotId updated to:', this.lotId);
                this.setupSocketConnection();
                this.loadData();
                break;
            case 'viewpoint-id':
                this.viewpointId = newValue;
                console.log('ParkingVisualization: viewpointId updated to:', this.viewpointId);
                // Clear existing slots before loading new ones
                this.clearSlotVisuals();
                // Use cached data for viewpoint changes instead of refetching
                this.loadDataForViewpoint();
                break;
            case 'show-slots':
                this.showSlots = newValue !== 'false';
                this.renderSlots();
                break;
            case 'show-zones':
                this.showZones = newValue !== 'false';
                this.renderZones();
                break;
            case 'zoom-enabled':
                this.zoomEnabled = newValue !== 'false';
                this.updateZoomManager();
                this.updateControlsVisibility();
                break;
            case 'show-controls':
                this.showControls = newValue !== 'false';
                this.updateControlsVisibility();
                break;
            case 'show-minimap':
                this.showMinimap = newValue !== 'false';
                this.updateMinimapVisibility();
                break;
            case 'show-stats':
                this.showStats = newValue === 'true';
                this.updateStatsVisibility();
                break;
        }
    }
    
    render() {
        this.shadowRoot.innerHTML = `
            <style>
                :host {
                    display: block;
                    position: relative;
                    width: 100%;
                    height: 100%;
                }

                .parking-visualization {
                    position: relative;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                }

                .zoom-controls {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.5rem 1rem;
                    background-color: rgba(255, 255, 255, 0.9);
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    z-index: 100;
                    border-bottom: 1px solid #eee;
                }

                .zoom-controls.hidden {
                    display: none;
                }

                .zoom-controls button {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.25rem;
                    padding: 0.25rem 0.5rem;
                    border: 1px solid #ccc;
                    background: white;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 0.875rem;
                }

                .zoom-controls button:hover {
                    background: #f8f9fa;
                }

                .zoom-level {
                    min-width: 50px;
                    text-align: center;
                    font-weight: bold;
                    font-size: 0.875rem;
                    color: #6c757d;
                }

                .zoom-wrapper {
                    position: relative;
                    flex: 1;
                    width: 100%;
                    overflow: hidden;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    background: #f8f9fa;
                }

                .image-container {
                    position: relative;
                    width: 100%;
                    height: 0;
                    padding-bottom: 56.25%; /* Default aspect ratio, updated by JS */
                    overflow: hidden;
                    cursor: grab;
                }

                .parking-slots-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    pointer-events: none;
                    z-index: 2;
                }

                /* Individual parking slot styles */
                .parking-slot {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    pointer-events: none;
                    z-index: 10;
                    transform-origin: 0 0;
                }

                .parking-slot.free {
                    background-color: rgba(40, 167, 69, 0.3);
                    border: 2px solid rgba(40, 167, 69, 0.8);
                }

                .parking-slot.occupied {
                    background-color: rgba(220, 53, 69, 0.3);
                    border: 2px solid rgba(220, 53, 69, 0.8);
                }

                .slot-label {
                    position: absolute;
                    background: rgba(0, 0, 0, 0.7);
                    color: white;
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-size: 12px;
                    font-weight: bold;
                    white-space: nowrap;
                    transform: translate(-50%, -50%);
                    pointer-events: none;
                    z-index: 11;
                }

                .image-container.dragging {
                    cursor: grabbing;
                }

                .parking-image {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                    display: block;
                    transform-origin: 0 0;
                    z-index: 1;
                }

                .minimap-container {
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    width: 200px;
                    height: auto;
                    background: rgba(255, 255, 255, 0.9);
                    border: 1px solid #ccc;
                    border-radius: 4px;
                    overflow: hidden;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                    z-index: 1000;
                    transition: opacity 0.3s ease;
                }

                .minimap-container.hidden {
                    display: none;
                }

                .minimap-container.zoom-hidden {
                    opacity: 0;
                    pointer-events: none;
                }

                .minimap-container img {
                    width: 100%;
                    height: auto;
                    display: block;
                }

                .minimap-viewport {
                    position: absolute;
                    border: 2px solid #007bff;
                    background: rgba(0, 123, 255, 0.1);
                    pointer-events: none;
                    box-sizing: border-box;
                }

                .parking-slot {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(40, 167, 69, 0.3);
                    border: 2px solid rgba(40, 167, 69, 0.8);
                    pointer-events: none;
                    z-index: 10;
                    transform-origin: 0 0;
                }

                .parking-slot.occupied {
                    background-color: rgba(220, 53, 69, 0.3);
                    border-color: rgba(220, 53, 69, 0.8);
                }

                .parking-slot .label {
                    position: absolute;
                    background: rgba(0, 0, 0, 0.7);
                    color: white;
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-size: 12px;
                    font-weight: bold;
                    white-space: nowrap;
                    transform: translate(-50%, -50%);
                    pointer-events: none;
                    z-index: 11;
                }

                .slot-label {
                    position: absolute;
                    background: rgba(0, 0, 0, 0.7);
                    color: white;
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-size: 12px;
                    font-weight: bold;
                    white-space: nowrap;
                    transform: translate(-50%, -50%);
                    pointer-events: none;
                    z-index: 11;
                }

                @keyframes slot-status-change {
                    0% { transform: scale(1); opacity: 0.7; }
                    50% { transform: scale(1.3); opacity: 0.9; }
                    100% { transform: scale(1); opacity: 0.7; }
                }

                .parking-slot.status-changed {
                    animation: slot-status-change 1000ms cubic-bezier(0.4, 0, 0.2, 1);
                    transform-origin: center;
                    z-index: 15;
                    pointer-events: none;
                    box-shadow: 0 0 25px currentColor;
                }

                .stats-container {
                    padding: 1rem;
                    background: white;
                    border-top: 1px solid #eee;
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 1rem;
                }

                .stats-container.hidden {
                    display: none;
                }

                .stat-box {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem;
                    border-radius: 8px;
                    font-size: 0.875rem;
                }

                .stat-box.free {
                    background-color: rgba(25, 135, 84, 0.1);
                    color: #198754;
                    border: 1px solid rgba(25, 135, 84, 0.2);
                }

                .stat-box.occupied {
                    background-color: rgba(220, 53, 69, 0.1);
                    color: #dc3545;
                    border: 1px solid rgba(220, 53, 69, 0.2);
                }

                .stat-number {
                    font-size: 1.5rem;
                    font-weight: bold;
                }

                .zone-overlay {
                    position: absolute;
                    border: 2px solid #007bff;
                    background-color: rgba(0, 123, 255, 0.1);
                    pointer-events: none;
                    z-index: 5;
                }

                .zone-overlay .zone-label {
                    position: absolute;
                    top: -25px;
                    left: 0;
                    background: #007bff;
                    color: white;
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-size: 11px;
                    font-weight: bold;
                    white-space: nowrap;
                }

                .error-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: rgba(248, 249, 250, 0.9);
                    z-index: 100;
                }

                .error-message {
                    text-align: center;
                    color: #dc3545;
                    padding: 20px;
                }

                .hidden {
                    display: none !important;
                }
            </style>

            <div class="parking-visualization">
                <!-- Zoom Controls -->
                <div class="zoom-controls" id="zoom-controls">
                    <button id="zoom-in-btn" title="Zoom In">
                        <i class="bi bi-zoom-in"></i> Zoom In
                    </button>
                    <button id="zoom-out-btn" title="Zoom Out">
                        <i class="bi bi-zoom-out"></i> Zoom Out
                    </button>
                    <button id="zoom-reset-btn" title="Reset Zoom">
                        <i class="bi bi-aspect-ratio"></i> Reset
                    </button>
                    <span class="zoom-level" id="zoom-level">100%</span>

                    <div style="margin-left: 1rem;">
                        <label>
                            <input type="checkbox" id="minimap-toggle" checked>
                            Show Minimap
                        </label>
                    </div>
                </div>

                <!-- Main Visualization Area -->
                <div class="zoom-wrapper">
                    <!-- Minimap -->
                    <div class="minimap-container" id="minimap-container">
                        <img id="minimap-image" alt="Minimap">
                        <div class="minimap-viewport" id="minimap-viewport"></div>
                    </div>

                    <!-- Image Container -->
                    <div class="image-container" id="image-container">
                        <img class="parking-image" id="parking-image" alt="Parking lot view">

                        <!-- Parking Slots Overlay - positioned absolutely over the image -->
                        <div class="parking-slots-overlay" id="parking-slots-overlay"></div>

                        <div class="error-overlay hidden" id="error-overlay">
                            <div class="error-message" id="error-message">
                                Failed to load parking data
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Stats Container -->
                <div class="stats-container hidden" id="stats-container">
                    <div class="stat-box free">
                        <div class="stat-number" id="free-count">0</div>
                        <div>Available</div>
                    </div>
                    <div class="stat-box occupied">
                        <div class="stat-number" id="occupied-count">0</div>
                        <div>Occupied</div>
                    </div>
                </div>
            </div>
        `;

        // Get element references
        this.imageContainer = this.shadowRoot.getElementById('image-container');
        this.imageElement = this.shadowRoot.getElementById('parking-image');
        this.slotsOverlay = this.shadowRoot.getElementById('parking-slots-overlay');
        this.errorOverlay = this.shadowRoot.getElementById('error-overlay');
        this.errorMessage = this.shadowRoot.getElementById('error-message');
        this.zoomControls = this.shadowRoot.getElementById('zoom-controls');
        this.minimapContainer = this.shadowRoot.getElementById('minimap-container');
        this.minimapImage = this.shadowRoot.getElementById('minimap-image');
        this.minimapViewport = this.shadowRoot.getElementById('minimap-viewport');
        this.minimapToggle = this.shadowRoot.getElementById('minimap-toggle');
        this.statsContainer = this.shadowRoot.getElementById('stats-container');
        this.freeCountElement = this.shadowRoot.getElementById('free-count');
        this.occupiedCountElement = this.shadowRoot.getElementById('occupied-count');
        this.zoomLevelElement = this.shadowRoot.getElementById('zoom-level');

        // Setup zoom control event listeners
        this.setupZoomControls();
    }
    
    setupEventListeners() {
        // Image load event
        this.imageElement.addEventListener('load', () => {
            this.onImageLoad();
        });

        // Image error event
        this.imageElement.addEventListener('error', () => {
            console.error('ParkingVisualization: Failed to load parking lot image');
        });

        // Resize observer for responsive behavior
        if (window.ResizeObserver) {
            const resizeObserver = new ResizeObserver(() => {
                this.onResize();
            });
            resizeObserver.observe(this);
        }
    }

    setupZoomControls() {
        const zoomInBtn = this.shadowRoot.getElementById('zoom-in-btn');
        const zoomOutBtn = this.shadowRoot.getElementById('zoom-out-btn');
        const zoomResetBtn = this.shadowRoot.getElementById('zoom-reset-btn');

        if (zoomInBtn) {
            zoomInBtn.addEventListener('click', (e) => {
                console.log('ParkingVisualization: Zoom in button clicked', this.zoomManager);
                e.preventDefault();
                if (this.zoomManager) {
                    this.zoomManager.zoomIn();
                } else {
                    console.log('ParkingVisualization: No zoom manager available');
                }
            });
        }

        if (zoomOutBtn) {
            zoomOutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                if (this.zoomManager) {
                    this.zoomManager.zoomOut();
                }
            });
        }

        if (zoomResetBtn) {
            zoomResetBtn.addEventListener('click', (e) => {
                e.preventDefault();
                if (this.zoomManager) {
                    this.zoomManager.resetZoom();
                }
            });
        }

        if (this.minimapToggle) {
            this.minimapToggle.addEventListener('change', (e) => {
                this.showMinimap = e.target.checked;
                this.updateMinimapVisibility();
            });
        }
    }

    setupSocketConnection() {
        if (!this.lotId || !window.socketConnection) {
            return;
        }

        this.socketConnection = window.socketConnection;

        // Remove existing listeners to prevent duplicates
        if (this.socketConnection) {
            this.socketConnection.off('slot_status_updated', this.handleSocketSlotUpdate);
            this.socketConnection.off('image_updated', this.handleSocketImageUpdate);
        }

        // Add new listeners
        this.socketConnection.on('slot_status_updated', this.handleSocketSlotUpdate);
        this.socketConnection.on('image_updated', this.handleSocketImageUpdate);

        console.log('ParkingVisualization: Socket.IO listeners setup for lot:', this.lotId);
    }

    handleSocketSlotUpdate(data) {
        if (data.lot_id === this.lotId) {
            console.log('ParkingVisualization: Received slot update:', data);

            // Update the cached slots data
            this.updateSlotInCache(data.slot_id, data.is_occupied);

            // Update the component's display
            this.updateSlotStatus(data.slot_id, data.is_occupied);

            // Dispatch event for external listeners
            this.dispatchEvent(new CustomEvent('slot-updated', {
                detail: {
                    slotId: data.slot_id,
                    isOccupied: data.is_occupied,
                    lotId: data.lot_id
                }
            }));
        }
    }

    handleSocketImageUpdate(data) {
        if (data.lot_id === this.lotId) {
            console.log('ParkingVisualization: Received image update:', data);

            // Check if this update is for our current viewpoint
            if (data.viewpoint_id && data.viewpoint_id !== this.viewpointId) {
                console.log('ParkingVisualization: Image update for different viewpoint, ignoring');
                return;
            }

            // Update image with cache busting (smooth update like old implementation)
            let imageUrl = data.new_image_path || data.image_path;
            if (imageUrl) {
                if (!imageUrl.startsWith('/static/')) {
                    imageUrl = imageUrl.startsWith('/') ? `/static${imageUrl}` : `/static/${imageUrl}`;
                }
                const cacheBustedUrl = `${imageUrl}?t=${new Date().getTime()}`;

                // Update main image smoothly
                this.imageElement.onload = () => {
                    console.log('ParkingVisualization: Image updated successfully');
                    this.handleResize(false); // Set correct aspect ratio, but don't re-render slots
                    // No need to re-render slots/zones - they haven't changed, only the image has
                    // Slots and zones will be re-rendered only when their data actually changes
                    this.imageElement.onload = null; // Clean up
                };
                this.imageElement.onerror = () => {
                    console.error('ParkingVisualization: Error loading updated image');
                    this.imageElement.onerror = null; // Clean up
                };
                // Reset loaded flag to allow onImageLoad to process this change
                this.imageElement.dataset.loaded = 'false';
                this.imageElement.src = cacheBustedUrl;

                // Update minimap image
                if (this.minimapImage) {
                    this.minimapImage.onload = () => {
                        console.log('ParkingVisualization: Minimap updated successfully');
                        this.minimapImage.onload = null;
                    };
                    this.minimapImage.onerror = () => {
                        console.error('ParkingVisualization: Error loading minimap image');
                        this.minimapImage.onerror = null;
                    };
                    this.minimapImage.src = cacheBustedUrl;
                }

                // Reset zoom after image update
                if (this.zoomManager) {
                    this.zoomManager.resetZoom();
                }
            }
        }
    }

    updateControlsVisibility() {
        if (this.zoomControls) {
            if (this.showControls && this.zoomEnabled) {
                this.zoomControls.classList.remove('hidden');
            } else {
                this.zoomControls.classList.add('hidden');
            }
        }
    }

    updateMinimapVisibility() {
        if (this.minimapContainer) {
            if (this.showMinimap) {
                this.minimapContainer.classList.remove('hidden');
                // Initially hide minimap since we start at 100% zoom
                this.minimapContainer.classList.add('zoom-hidden');
            } else {
                this.minimapContainer.classList.add('hidden');
            }
        }
    }

    updateStatsVisibility() {
        if (this.statsContainer) {
            if (this.showStats) {
                this.statsContainer.classList.remove('hidden');
            } else {
                this.statsContainer.classList.add('hidden');
            }
        }
    }

    updateStats() {
        if (!this.showStats || !this.slots.length) return;

        const freeSlots = this.slots.filter(slot => !slot.is_occupied).length;
        const occupiedSlots = this.slots.filter(slot => slot.is_occupied).length;

        if (this.freeCountElement) {
            this.freeCountElement.textContent = freeSlots;
        }
        if (this.occupiedCountElement) {
            this.occupiedCountElement.textContent = occupiedSlots;
        }

        // Dispatch stats update event
        this.dispatchEvent(new CustomEvent('stats-updated', {
            detail: {
                free: freeSlots,
                occupied: occupiedSlots,
                total: this.slots.length,
                occupancyRate: this.slots.length > 0 ? (occupiedSlots / this.slots.length) * 100 : 0
            }
        }));
    }

    /**
     * Get all slots for the lot, using cache if available and fresh
     */
    async getAllSlots() {
        const cacheKey = this.lotId;
        const now = Date.now();
        const cacheMaxAge = 30000; // 30 seconds cache

        // Check if we have fresh cached data
        const cached = ParkingVisualization.slotsCache.get(cacheKey);
        if (cached && (now - cached.timestamp) < cacheMaxAge) {
            console.log('ParkingVisualization: Using cached slots data');
            return cached.slots;
        }

        // Fetch fresh data
        console.log('ParkingVisualization: Fetching fresh slots data from API');
        const slotsResponse = await fetch(`/api/lots/${this.lotId}/slots`);
        if (!slotsResponse.ok) {
            throw new Error(`Failed to load slots: ${slotsResponse.status}`);
        }
        const allSlots = await slotsResponse.json();

        // Cache the data
        ParkingVisualization.slotsCache.set(cacheKey, {
            slots: allSlots,
            timestamp: now
        });

        console.log(`ParkingVisualization: Cached ${allSlots.length} slots for lot ${this.lotId}`);
        return allSlots;
    }

    /**
     * Update a slot's status in the cache (called from Socket.IO updates)
     */
    updateSlotInCache(slotId, isOccupied) {
        const cacheKey = this.lotId;
        const cached = ParkingVisualization.slotsCache.get(cacheKey);

        if (cached && cached.slots) {
            const slot = cached.slots.find(s => s.id === slotId);
            if (slot) {
                slot.is_occupied = isOccupied;
                console.log(`ParkingVisualization: Updated slot ${slotId} in cache, occupied: ${isOccupied}`);

                // Also update the current component's slots if it contains this slot
                const currentSlot = this.slots.find(s => s.id === slotId);
                if (currentSlot) {
                    currentSlot.is_occupied = isOccupied;
                }
            }
        }
    }

    /**
     * Load data for viewpoint change using cached data if available
     */
    async loadDataForViewpoint() {
        console.log('ParkingVisualization: loadDataForViewpoint called with lotId:', this.lotId, 'viewpointId:', this.viewpointId);

        if (!this.lotId) {
            console.warn('ParkingVisualization: No lotId provided, cannot load data');
            return;
        }

        try {
            // Get all slots from cache or fetch if not cached/expired
            const allSlots = await this.getAllSlots();

            // Filter slots for current viewpoint if specified
            this.slots = this.viewpointId ?
                allSlots.filter(slot => slot.viewpoint_id === this.viewpointId) : allSlots;

            console.log(`ParkingVisualization: Filtered ${this.slots.length} slots for viewpoint ${this.viewpointId} (out of ${allSlots.length} total) - USING CACHE`);

            // Store previous states for change detection
            this.slots.forEach(slot => {
                this.previousSlotStates.set(slot.id, slot.is_occupied);
            });

            // Load zones data (this is usually small and doesn't need heavy caching)
            const zonesResponse = await fetch(`/api/lots/${this.lotId}/zones`);
            if (zonesResponse.ok) {
                this.zones = await zonesResponse.json();
                console.log(`ParkingVisualization: Loaded ${this.zones.length} zones`);
            } else {
                console.warn('ParkingVisualization: Failed to load zones, continuing without them');
                this.zones = [];
            }

            // Render the visualization
            this.render();

            // Wait for image to load before rendering slots and zones
            if (this.imageElement && this.imageElement.complete && this.imageElement.naturalWidth > 0) {
                // Image is already loaded, render immediately
                this.renderSlots();
                this.renderZones();
            } else if (this.imageElement) {
                // Image is not loaded yet, wait for it
                this.imageElement.addEventListener('load', () => {
                    this.renderSlots();
                    this.renderZones();
                }, { once: true });
            }

            // Dispatch data loaded event
            this.dispatchEvent(new CustomEvent('data-loaded', {
                detail: {
                    lotId: this.lotId,
                    viewpointId: this.viewpointId,
                    slotsCount: this.slots.length,
                    zonesCount: this.zones.length
                }
            }));

        } catch (error) {
            console.error('ParkingVisualization: Error loading data for viewpoint:', error);
            this.error = error.message;
            this.render();
        }
    }

    /**
     * Load parking data using API calls
     */
    async loadData() {
        console.log('ParkingVisualization: loadData called with lotId:', this.lotId, 'viewpointId:', this.viewpointId);

        if (!this.lotId) {
            console.warn('ParkingVisualization: No lotId provided, cannot load data');
            return;
        }

        try {
            // Get all slots from cache or fetch if not cached/expired
            const allSlots = await this.getAllSlots();

            // Filter slots for current viewpoint if specified
            this.slots = this.viewpointId ?
                allSlots.filter(slot => slot.viewpoint_id === this.viewpointId) : allSlots;

            console.log(`ParkingVisualization: Filtered ${this.slots.length} slots for viewpoint ${this.viewpointId} (out of ${allSlots.length} total) - INITIAL LOAD`);
            console.log('ParkingVisualization: Slot viewpoint breakdown:', {
                withCurrentViewpoint: allSlots.filter(s => s.viewpoint_id === this.viewpointId).length,
                withoutViewpoint: allSlots.filter(s => !s.viewpoint_id || s.viewpoint_id === null).length,
                withOtherViewpoints: allSlots.filter(s => s.viewpoint_id && s.viewpoint_id !== this.viewpointId).length,
                total: allSlots.length
            });

            // Store previous states for change detection
            this.slots.forEach(slot => {
                this.previousSlotStates.set(slot.id, slot.is_occupied);
            });

            // Load viewpoint/lot data for image
            let imageUrl = '';
            if (this.viewpointId) {
                const viewpointResponse = await fetch(`/api/lots/${this.lotId}/viewpoints`);
                if (viewpointResponse.ok) {
                    const viewpointsData = await viewpointResponse.json();
                    const viewpoint = viewpointsData.viewpoints?.find(v => v.id === this.viewpointId);
                    if (viewpoint && viewpoint.image_path) {
                        imageUrl = viewpoint.image_path.startsWith('/static/') ?
                            viewpoint.image_path : `/static/${viewpoint.image_path}`;
                    }
                }
            }

            // Fallback to lot image if no viewpoint image
            if (!imageUrl) {
                const lotResponse = await fetch(`/api/lots/${this.lotId}`);
                if (lotResponse.ok) {
                    const lotData = await lotResponse.json();
                    if (lotData.image_path) {
                        imageUrl = lotData.image_path.startsWith('/static/') ?
                            lotData.image_path : `/static/${lotData.image_path}`;
                    }
                }
            }

            if (imageUrl) {
                console.log('ParkingVisualization: Setting image URL:', imageUrl);
                this.imageUrl = imageUrl;

                // Reset loaded flag to allow onImageLoad to process this change
                this.imageElement.dataset.loaded = 'false';

                // Set image source without loading overlay to prevent flicker
                this.imageElement.src = imageUrl;
                if (this.minimapImage) {
                    this.minimapImage.src = imageUrl;
                }
            } else {
                console.error('ParkingVisualization: No image URL available');
                return;
            }

            console.log(`ParkingVisualization: Loaded ${this.slots.length} slots for lot ${this.lotId}`);

            // Dispatch data loaded event
            this.dispatchEvent(new CustomEvent('data-loaded', {
                detail: {
                    lotId: this.lotId,
                    viewpointId: this.viewpointId,
                    slots: this.slots,
                    imageUrl: this.imageUrl
                }
            }));

        } catch (error) {
            console.error('ParkingVisualization: Error loading parking data:', error);
        }
    }

    /**
     * Subscribe to real-time updates
     */
    subscribeToUpdates() {
        // Unsubscribe from previous updates
        if (this.unsubscribeFromUpdates) {
            this.unsubscribeFromUpdates();
        }

        if (window.ParkingDataService && this.lotId) {
            this.unsubscribeFromUpdates = window.ParkingDataService.subscribeToUpdates(
                this.lotId,
                (update) => this.handleRealtimeUpdate(update)
            );
        }
    }

    /**
     * Handle real-time updates
     */
    handleRealtimeUpdate(update) {
        if (update.type === 'slot_update' && update.viewpointId === this.viewpointId) {
            this.updateSlotStatus(update.slotId, update.isOccupied);

            // Dispatch update event
            this.dispatchEvent(new CustomEvent('slot-updated', {
                detail: {
                    slotId: update.slotId,
                    isOccupied: update.isOccupied
                }
            }));
        }
    }

    // Note: Loading/error overlays removed to prevent flicker - errors are logged to console

    /**
     * Invalidate the slots cache for this lot
     */
    static invalidateCache(lotId) {
        ParkingVisualization.slotsCache.delete(lotId);
        console.log(`ParkingVisualization: Invalidated cache for lot ${lotId}`);
    }

    /**
     * Refresh the component data (called from dashboard after detection)
     */
    refresh() {
        console.log('ParkingVisualization: Refreshing component data');
        // Invalidate cache to force fresh data fetch
        ParkingVisualization.invalidateCache(this.lotId);
        this.loadData();
    }
    
    onImageLoad() {
        // Only process if this is the initial load or a legitimate image change
        if (!this.imageElement.dataset.loaded) {
            this.imageElement.dataset.loaded = 'true';

            this.handleResize(); // Set correct aspect ratio
            this.renderSlots();
            this.renderZones();
            this.updateZoomManager();
            this.updateStats();

            this.dispatchEvent(new CustomEvent('image-loaded', {
                detail: {
                    naturalWidth: this.imageElement.naturalWidth,
                    naturalHeight: this.imageElement.naturalHeight,
                    displayWidth: this.imageElement.offsetWidth,
                    displayHeight: this.imageElement.offsetHeight
                }
            }));
        }
    }

    handleResize(shouldRerenderSlots = true) {
        if (!this.imageElement || !this.imageContainer) return;

        // Wait for image natural dimensions to be available
        if (!this.imageElement.naturalWidth || !this.imageElement.naturalHeight) {
            this.imageElement.addEventListener('load', () => this.handleResize(shouldRerenderSlots), { once: true });
            return;
        }

        // Calculate and cache the original aspect ratio
        if (!this.imageContainer.dataset.aspectRatio) {
            const aspectRatio = (this.imageElement.naturalHeight / this.imageElement.naturalWidth * 100).toFixed(4);
            this.imageContainer.dataset.aspectRatio = aspectRatio;
        }

        // Use cached aspect ratio for consistent resizing
        this.imageContainer.style.paddingBottom = `${this.imageContainer.dataset.aspectRatio}%`;

        // Re-render slots after resize completes (only if this is a real resize, not just an image update)
        if (shouldRerenderSlots && this.slots.length > 0 && this.imageElement.complete) {
            setTimeout(() => {
                this.renderSlots();
                if (this.zoomManager) {
                    this.zoomManager.refreshZonePositions();
                }
            }, 50);
        }
    }

    /**
     * Cleanup when component is removed from DOM
     */
    disconnectedCallback() {
        // Clean up Socket.IO listeners
        if (this.socketConnection) {
            this.socketConnection.off('slot_status_updated', this.handleSocketSlotUpdate);
            this.socketConnection.off('image_updated', this.handleSocketImageUpdate);
            this.socketConnection = null;
        }

        if (this.unsubscribeFromUpdates) {
            this.unsubscribeFromUpdates();
            this.unsubscribeFromUpdates = null;
        }

        if (this.zoomManager) {
            this.zoomManager.destroy();
            this.zoomManager = null;
        }
    }
    
    onResize() {
        // Use the same resize logic as handleResize
        this.handleResize();
    }
    

    
    updateZoomManager() {
        if (this.zoomEnabled && this.imageElement) {
            if (!this.zoomManager) {
                console.log('ParkingVisualization: Creating zoom manager');
                // Create a custom zoom manager that works with our Shadow DOM elements
                this.zoomManager = this.createCustomZoomManager();
            }
        } else if (!this.zoomEnabled && this.zoomManager) {
            console.log('ParkingVisualization: Destroying zoom manager');
            if (this.zoomManager.destroy) {
                this.zoomManager.destroy();
            }
            this.zoomManager = null;
        }
    }

    createCustomZoomManager() {
        console.log('ParkingVisualization: Creating custom zoom manager');
        // Create a simplified zoom manager that works with our component
        const zoomManager = {
            zoomLevel: 1.0,
            maxZoom: 5.0,
            minZoom: 0.5,
            isDragging: false,
            lastX: 0,
            lastY: 0,
            offsetX: 0,
            offsetY: 0,

            zoomIn: (centerX, centerY) => {
                const newZoom = Math.min(zoomManager.zoomLevel * 1.2, zoomManager.maxZoom);
                zoomManager.setZoom(newZoom, centerX, centerY);
            },

            zoomOut: (centerX, centerY) => {
                const newZoom = Math.max(zoomManager.zoomLevel / 1.2, zoomManager.minZoom);
                zoomManager.setZoom(newZoom, centerX, centerY);
            },

            resetZoom: () => {
                zoomManager.zoomLevel = 1.0;
                zoomManager.offsetX = 0;
                zoomManager.offsetY = 0;
                zoomManager.applyTransform();
                zoomManager.updateControls();
            },

            setZoom: (newZoom, centerX, centerY) => {
                const oldZoom = zoomManager.zoomLevel;
                zoomManager.zoomLevel = newZoom;

                // Adjust offset to zoom towards center point if provided
                if (centerX !== undefined && centerY !== undefined) {
                    const rect = this.imageContainer.getBoundingClientRect();
                    const relativeX = centerX - rect.left;
                    const relativeY = centerY - rect.top;

                    zoomManager.offsetX = relativeX - (relativeX - zoomManager.offsetX) * (newZoom / oldZoom);
                    zoomManager.offsetY = relativeY - (relativeY - zoomManager.offsetY) * (newZoom / oldZoom);
                }

                zoomManager.applyTransform();
                zoomManager.updateControls();
            },

            applyTransform: () => {
                this.imageElement.style.transform =
                    `scale(${zoomManager.zoomLevel}) translate(${zoomManager.offsetX / zoomManager.zoomLevel}px, ${zoomManager.offsetY / zoomManager.zoomLevel}px)`;
            },

            updateControls: () => {
                if (this.zoomLevelElement) {
                    this.zoomLevelElement.textContent = `${Math.round(zoomManager.zoomLevel * 100)}%`;
                }
                this.updateMinimapZoomVisibility(zoomManager.zoomLevel);
            },

            refreshZonePositions: () => {
                // Placeholder for compatibility
            },

            destroy: () => {
                // Clean up event listeners if needed
            }
        };

        // Add wheel event for zooming
        this.imageContainer.addEventListener('wheel', (e) => {
            console.log('ParkingVisualization: Wheel event detected', e.deltaY);
            e.preventDefault();
            const rect = this.imageContainer.getBoundingClientRect();
            const centerX = e.clientX;
            const centerY = e.clientY;

            if (e.deltaY < 0) {
                console.log('ParkingVisualization: Zooming in');
                zoomManager.zoomIn(centerX, centerY);
            } else {
                console.log('ParkingVisualization: Zooming out');
                zoomManager.zoomOut(centerX, centerY);
            }
        });

        // Add mouse events for panning
        let startX, startY, startOffsetX, startOffsetY;

        this.imageContainer.addEventListener('mousedown', (e) => {
            if (zoomManager.zoomLevel > 1.0) {
                zoomManager.isDragging = true;
                startX = e.clientX;
                startY = e.clientY;
                startOffsetX = zoomManager.offsetX;
                startOffsetY = zoomManager.offsetY;
                this.imageContainer.style.cursor = 'grabbing';
                e.preventDefault();
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (zoomManager.isDragging) {
                zoomManager.offsetX = startOffsetX + (e.clientX - startX);
                zoomManager.offsetY = startOffsetY + (e.clientY - startY);
                zoomManager.applyTransform();
                e.preventDefault();
            }
        });

        document.addEventListener('mouseup', () => {
            if (zoomManager.isDragging) {
                zoomManager.isDragging = false;
                this.imageContainer.style.cursor = zoomManager.zoomLevel > 1.0 ? 'grab' : 'default';
            }
        });

        return zoomManager;
    }

    updateMinimapZoomVisibility(zoomLevel) {
        if (!this.minimapContainer) return;

        // Hide minimap when zoom is at 100% (1.0) or less
        if (zoomLevel <= 1.0) {
            this.minimapContainer.classList.add('zoom-hidden');
        } else {
            this.minimapContainer.classList.remove('zoom-hidden');
        }
    }
    
    // Coordinate conversion helpers

    /**
     * Convert API pixel coordinates (based on natural image size) to display percentages
     * Properly handles object-fit: contain letterboxing
     */
    convertApiPixelsToDisplayPercentage(apiX, apiY) {
        if (!this.imageElement) return { x: 0, y: 0 };

        // Get natural (original) image dimensions - this is what API coordinates are based on
        const naturalWidth = this.imageElement.naturalWidth;
        const naturalHeight = this.imageElement.naturalHeight;

        // Get container dimensions (what the image element occupies)
        const containerWidth = this.imageElement.offsetWidth;
        const containerHeight = this.imageElement.offsetHeight;

        if (naturalWidth === 0 || naturalHeight === 0 || containerWidth === 0 || containerHeight === 0) {
            console.warn('Image dimensions not available for coordinate conversion');
            return { x: 0, y: 0 };
        }

        // Calculate aspect ratios
        const naturalAspectRatio = naturalWidth / naturalHeight;
        const containerAspectRatio = containerWidth / containerHeight;

        let actualImageWidth, actualImageHeight, offsetX, offsetY;

        if (naturalAspectRatio > containerAspectRatio) {
            // Image is wider than container - letterboxing on top/bottom
            actualImageWidth = containerWidth;
            actualImageHeight = containerWidth / naturalAspectRatio;
            offsetX = 0;
            offsetY = (containerHeight - actualImageHeight) / 2;
        } else {
            // Image is taller than container - letterboxing on left/right
            actualImageWidth = containerHeight * naturalAspectRatio;
            actualImageHeight = containerHeight;
            offsetX = (containerWidth - actualImageWidth) / 2;
            offsetY = 0;
        }

        // Scale from natural coordinates to actual displayed image coordinates
        const scaleX = actualImageWidth / naturalWidth;
        const scaleY = actualImageHeight / naturalHeight;

        // Convert to actual position within the container
        const actualX = (apiX * scaleX) + offsetX;
        const actualY = (apiY * scaleY) + offsetY;

        // Convert to percentages relative to the container
        const percentageX = (actualX / containerWidth) * 100;
        const percentageY = (actualY / containerHeight) * 100;

        return { x: percentageX, y: percentageY };
    }

    /**
     * Convert display pixel coordinates to display percentages (legacy function)
     * This is for coordinates that are already in display pixel space
     */
    convertToPercentage(x, y) {
        if (!this.imageElement) return { x: 0, y: 0 };

        const width = this.imageElement.offsetWidth;
        const height = this.imageElement.offsetHeight;

        if (width === 0 || height === 0) {
            console.warn('Image dimensions not available for coordinate conversion');
            return { x: 0, y: 0 };
        }

        return {
            x: (x / width) * 100,
            y: (y / height) * 100
        };
    }

    convertToPixels(x, y) {
        if (!this.imageElement) return { x: 0, y: 0 };

        const width = this.imageElement.offsetWidth;
        const height = this.imageElement.offsetHeight;

        if (width === 0 || height === 0) {
            console.warn('Image dimensions not available for coordinate conversion');
            return { x: 0, y: 0 };
        }

        return {
            x: (x * width) / 100,
            y: (y * height) / 100
        };
    }
    
    // Public API methods

    /**
     * Refresh data from the server
     */
    async refresh() {
        await this.loadData();
    }

    /**
     * Update a specific slot's status (used by real-time updates)
     */
    updateSlotStatus(slotId, isOccupied) {
        const slot = this.slots.find(s => s.id === slotId);
        if (slot) {
            const previousState = this.previousSlotStates.get(slotId);
            const hasChanged = previousState !== isOccupied;

            slot.is_occupied = isOccupied;
            this.previousSlotStates.set(slotId, isOccupied);

            this.updateSlotVisual(slot, hasChanged);
            this.updateStats();
        }
    }

    // Visual update function with animation support
    updateSlotVisual(slot, hasChanged) {
        console.log('ParkingVisualization: UpdateVisual called for slot:', slot.id, 'hasChanged:', hasChanged);

        if (!slot || typeof slot.id === 'undefined') {
            console.error('ParkingVisualization: Invalid slot object or missing slot id:', slot);
            return;
        }

        const slotComponent = this.slotsOverlay.querySelector(`parking-slot[slot-id="${slot.id}"]`);

        if (!slotComponent) {
            console.warn(`ParkingVisualization: Slot component not found for slot id: ${slot.id}`);
            // Re-render all slots if individual slot not found
            this.renderSlots();
            return;
        }

        // Update the component's occupancy state with animation
        slotComponent.updateOccupancy(slot.is_occupied, hasChanged);

        console.log(`ParkingVisualization: Updated visual for slot ${slot.id}, occupied: ${slot.is_occupied}`);
    }

    /**
     * Get current slot data
     */
    getSlots() {
        return [...this.slots];
    }

    /**
     * Get current component state
     */
    getState() {
        return {
            lotId: this.lotId,
            viewpointId: this.viewpointId,
            loading: this.loading,
            error: this.error,
            slotsCount: this.slots.length,
            occupiedCount: this.slots.filter(s => s.is_occupied).length
        };
    }

    /**
     * Clear all overlays
     */
    clearOverlays() {
        this.shadowRoot.querySelectorAll('.parking-slot, .zone-overlay').forEach(el => el.remove());
    }
    
    renderSlots() {
        if (!this.showSlots || !this.imageElement || !this.imageElement.complete) {
            return;
        }

        console.log('ParkingVisualization: Rendering', this.slots.length, 'parking slots');

        // Clear existing slot elements
        this.clearSlotVisuals();

        if (!this.imageContainer || !this.imageElement) {
            console.error('Image container or parking image not found');
            return;
        }

        // Check natural dimensions are available (needed for coordinate conversion)
        if (!this.imageElement.naturalWidth || !this.imageElement.naturalHeight) {
            console.error("Image natural dimensions not available");
            return;
        }

        const imgWidth = this.imageElement.offsetWidth;
        const imgHeight = this.imageElement.offsetHeight;

        if (imgWidth <= 0 || imgHeight <= 0) {
            console.error("Invalid image dimensions:", imgWidth, imgHeight);
            return;
        }

        this.slots.forEach(slot => {
            if (slot.detection_zone && slot.detection_zone.points) {
                // API returns pixel coordinates based on natural image size, convert to display percentages
                const apiPixelPoints = slot.detection_zone.points;
                const percentagePoints = apiPixelPoints.map(point =>
                    this.convertApiPixelsToDisplayPercentage(point.x, point.y)
                );

                console.log(`ParkingVisualization: Slot ${slot.id} - API points:`, apiPixelPoints, 'Percentage points:', percentagePoints);

                // Draw the slot to UI
                this.drawSlotToUI(slot.id, apiPixelPoints, percentagePoints, slot.slot_name, slot.is_occupied);
            } else {
                console.warn('ParkingVisualization: Slot', slot.id, 'has no detection zone or points');
            }
        });

        // Update stats after rendering
        this.updateStats();

        // Notify zoom manager about position changes
        if (this.zoomManager) {
            this.zoomManager.refreshZonePositions();
        }
    }

    // Clear slot visuals
    clearSlotVisuals() {
        if (this.slotsOverlay) {
            this.slotsOverlay.innerHTML = '';
        }
    }

    // Draw individual slot to UI using parking-slot component
    drawSlotToUI(slotId, pixelPoints, percentagePoints, slotName, isOccupied) {
        // Remove any existing slot component with the same ID
        const existingSlot = this.slotsOverlay.querySelector(`parking-slot[slot-id="${slotId}"]`);
        if (existingSlot) {
            existingSlot.remove();
        }

        // Create parking-slot component
        const slotComponent = document.createElement('parking-slot');
        slotComponent.setAttribute('slot-id', slotId);
        slotComponent.setAttribute('slot-name', slotName || slotId);
        slotComponent.setAttribute('is-occupied', isOccupied.toString());
        slotComponent.setAttribute('points', JSON.stringify(percentagePoints));

        // Add to slots overlay
        this.slotsOverlay.appendChild(slotComponent);
    }
    

    
    renderZones() {
        if (!this.showZones || !this.imageElement || !this.imageElement.complete) {
            return;
        }
        
        // Clear existing zones
        this.shadowRoot.querySelectorAll('.zone-overlay').forEach(el => el.remove());
        
        this.zones.forEach(zone => {
            if (zone.detection_zone && zone.detection_zone.points) {
                this.createZoneOverlay(zone);
            }
        });
    }
    
    createZoneOverlay(zone) {
        // Implementation for zone overlays (similar to slots but different styling)
        // This can be expanded based on zone requirements
    }
}

// Register the custom element
customElements.define('parking-visualization', ParkingVisualization);

// Make the class available globally for cache management
window.ParkingVisualization = ParkingVisualization;
