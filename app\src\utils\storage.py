import json
import os
import sqlite3
import uuid
from typing import Dict, List, Optional, Union, cast, Any
import threading
from src.models.parking import ParkingLot
from src.models.events import BaseEvent
from src.utils.logger import setup_logger
from datetime import datetime

# Import the new event sourcing components
from src.utils.event_sourced_storage import EventSourcedStorage
from src.utils.sqlalchemy_event_store import SQLAlchemyEventStore
from src.utils.event_store_interface import EventStore

# Keep imports for backward compatibility during transition
from src.utils.azure_sql_storage import AzureSQLStorage
from src.utils.sqlalchemy_storage import SQLAlchemyStorage

LOGGER = setup_logger(__name__)

# Define type hints
StorageRepository = Union[AzureSQLStorage, SQLAlchemyStorage, EventSourcedStorage]

class Storage:
    """Storage utility for parking lots data."""

    _db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'parking.db')
    _cache: Dict[str, ParkingLot] = {}
    _lock = threading.Lock()
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                # Double-check locking
                if cls._instance is None:
                    cls._instance = super(Storage, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """
        Initializes the Storage instance with the appropriate repository.
        Uses the new EventSourcedStorage with SQLAlchemyEventStore backend.
        """
        if self._initialized:
            return
        with self._lock:
            if self._initialized:
                return

            # Initialize the appropriate event store
            connection_string = os.getenv('AZURE_SQL_CONNECTION_STRING')

            if connection_string:
                LOGGER.info("Azure SQL Connection String configured, using Azure SQL event store")
                # TODO: Implement AzureSQLEventStore if needed
                # event_store = AzureSQLEventStore.instance()
                # For now, fall back to SQLAlchemy
                event_store = SQLAlchemyEventStore.instance()
            else:
                LOGGER.info("Using SQLAlchemy event store with local SQLite database")
                event_store = SQLAlchemyEventStore.instance()

            # Initialize the event-sourced storage with the configured event store
            self._repo = EventSourcedStorage(event_store)

            LOGGER.info(f"Storage facade initialized with {self._repo.__class__.__name__}")
            self._initialized = True

    @classmethod
    def get_instance(cls):
        """Returns the singleton instance of the Storage facade."""
        return cls()

    # --- State Query Methods (Read Side) ---
    def get_parking_lot(self, lot_id: str) -> Optional[ParkingLot]:
        """
        Gets the current state of a parking lot by reconstructing from events.
        Uses in-memory cache to avoid repeated reconstruction.
        Returns None if the lot doesn't exist or has been deleted.
        """
        with self._lock:
            # Check cache first
            if lot_id in self._cache:
                LOGGER.debug(f"Returning cached parking lot: {lot_id}")
                return self._cache[lot_id]

            # Not in cache, reconstruct from events
            LOGGER.debug(f"Cache miss for lot {lot_id}, reconstructing from events")
            lot = self._repo.get_parking_lot(lot_id)

            # Cache the result if lot exists
            if lot:
                self._cache[lot_id] = lot
                LOGGER.debug(f"Cached parking lot: {lot_id}")

            return lot

    def get_all_parking_lots(self) -> List[ParkingLot]:
        """
        Gets the current state of all existing (not deleted) parking lots.
        Uses cache when possible to avoid repeated reconstruction.
        """
        # Get all lot IDs from repository
        lot_ids = self._repo._event_store.get_all_aggregate_ids()

        all_lots = []
        with self._lock:
            for lot_id in lot_ids:
                # Use cached version if available, otherwise reconstruct
                if lot_id in self._cache:
                    lot = self._cache[lot_id]
                else:
                    lot = self._repo.get_parking_lot(lot_id)
                    if lot:
                        self._cache[lot_id] = lot

                if lot:
                    all_lots.append(lot)

        return all_lots

    def _invalidate_cache(self, lot_id: str):
        """Invalidate cache entry for a specific lot."""
        with self._lock:
            if lot_id in self._cache:
                del self._cache[lot_id]
                LOGGER.debug(f"Invalidated cache for lot: {lot_id}")

    def _update_cache(self, lot_id: str):
        """Update cache entry for a specific lot by reconstructing from events."""
        with self._lock:
            lot = self._repo.get_parking_lot(lot_id)
            if lot:
                self._cache[lot_id] = lot
                LOGGER.debug(f"Updated cache for lot: {lot_id}")
            elif lot_id in self._cache:
                # Lot was deleted, remove from cache
                del self._cache[lot_id]
                LOGGER.debug(f"Removed deleted lot from cache: {lot_id}")

    def _update_slot_occupancy_in_cache(self, lot_id: str, slot_id: str, is_occupied: bool):
        """Efficiently update slot occupancy in cache without full reconstruction."""
        with self._lock:
            if lot_id in self._cache:
                lot = self._cache[lot_id]
                # Find and update the slot in the cached lot
                for slot in lot.slots:
                    if slot.id == slot_id:
                        slot.is_occupied = is_occupied
                        LOGGER.debug(f"Updated slot {slot_id} occupancy in cache to {is_occupied}")
                        return
                LOGGER.debug(f"Slot {slot_id} not found in cached lot {lot_id}")
            else:
                LOGGER.debug(f"Lot {lot_id} not in cache, cannot update slot occupancy")

    # --- Command Methods (Write Side - Generate Events) ---
    def create_parking_lot(self, name: str, image_path: Optional[str] = None, lot_id: Optional[str] = None) \
            -> Optional[str]:
        """
        Creates a new parking lot with a generated UUID.

        Args:
            name: The name of the parking lot
            image_path: Optional path to an image for the lot
            lot_id: Optional ID for the lot (if not provided, a new UUID will be generated)

        Returns:
            Optional[str]: The ID of the created lot, or None if creation failed
        """
        LOGGER.debug(f"Creating new parking lot '{name}' with image path: {image_path}")
        if not lot_id:
            lot_id = str(uuid.uuid4())
            LOGGER.debug(f"Generated new UUID for parking lot: {lot_id}")

        result = self._repo.create_parking_lot_with_id(lot_id, name, image_path)
        if result:
            LOGGER.info(f"Successfully created parking lot '{name}' with ID {lot_id}")
            # Update cache with new lot
            self._update_cache(lot_id)
        else:
            LOGGER.warning(f"Failed to create parking lot '{name}' with ID {lot_id}")
        return result

    def create_parking_lot_with_id(self, lot_id: str, name: str, image_path: Optional[str] = None) -> Optional[str]:
        """
        Creates a new parking lot with a specified ID.

        Args:
            lot_id: The ID for the new parking lot.
            name: The name of the parking lot.
            image_path: Optional path to an image for the lot.

        Returns:
            Optional[str]: The ID of the created lot (same as input lot_id), or None if creation failed.
        """
        LOGGER.debug(f"Creating new parking lot '{name}' with specified ID {lot_id} and image path: {image_path}")
        if hasattr(self._repo, 'create_parking_lot_with_id'):
            result = self._repo.create_parking_lot_with_id(lot_id, name, image_path)
            if result:
                LOGGER.info(f"Successfully created parking lot '{name}' with ID {lot_id}")
            else:
                LOGGER.warning(f"Failed to create parking lot '{name}' with ID {lot_id}")
            return result
        else:
            LOGGER.error(f"Repository {self._repo.__class__.__name__} does not support create_parking_lot_with_id.")
            return None

    def delete_parking_lot(self, lot_id: str) -> bool:
        """
        Deletes a parking lot by marking it as deleted.

        Args:
            lot_id: The ID of the lot to delete

        Returns:
            bool: True if deletion was successful, False otherwise
        """
        LOGGER.debug(f"Attempting to delete parking lot with ID {lot_id}")
        result = self._repo.delete_parking_lot(lot_id)
        if result:
            LOGGER.info(f"Successfully deleted parking lot with ID {lot_id}")
            # Remove from cache since lot is deleted
            self._invalidate_cache(lot_id)
        else:
            LOGGER.warning(f"Failed to delete parking lot with ID {lot_id}")
        return result

    def update_parking_lot_image(self, lot_id: str, new_image_path: Optional[str]) -> bool:
        """
        Updates a parking lot's image.

        Args:
            lot_id: The ID of the lot to update
            new_image_path: The new image path, or None to clear

        Returns:
            bool: True if update was successful, False otherwise
        """
        LOGGER.debug(f"Updating image for parking lot {lot_id} to {new_image_path}")
        result = self._repo.update_parking_lot_image(lot_id, new_image_path)
        if result:
            LOGGER.info(f"Successfully updated image for parking lot {lot_id}")
        else:
            LOGGER.warning(f"Failed to update image for parking lot {lot_id}")
        return result

    def rename_parking_lot(self, lot_id: str, new_name: str) -> bool:
        """
        Renames a parking lot.

        Args:
            lot_id: The ID of the lot to rename
            new_name: The new name

        Returns:
            bool: True if rename was successful, False otherwise
        """
        LOGGER.debug(f"Renaming parking lot {lot_id} to '{new_name}'")
        result = self._repo.rename_parking_lot(lot_id, new_name)
        if result:
            LOGGER.info(f"Successfully renamed parking lot {lot_id} to '{new_name}'")
        else:
            LOGGER.warning(f"Failed to rename parking lot {lot_id} to '{new_name}'")
        return result

    def add_parking_slot(self, lot_id: str, slot_name: str, zone_points: List[Dict[str, float]] = None,
                         overlap_threshold: float = 0.3, viewpoint_id: Optional[str] = None) -> Optional[str]:
        """
        Adds a new parking slot to a lot.

        Args:
            lot_id: The ID of the lot to add the slot to
            slot_name: The name of the new slot
            zone_points: Optional list of detection zone points
            overlap_threshold: The overlap threshold for the new slot
            viewpoint_id: Optional ID of the viewpoint this slot belongs to

        Returns:
            Optional[str]: The ID of the created slot, or None if creation failed
            :param slot_name:
        """
        LOGGER.debug(f"Adding new parking slot '{slot_name}' to lot {lot_id} with {len(zone_points) if zone_points else 0} points, viewpoint_id: {viewpoint_id}")
        result = self._repo.add_parking_slot(lot_id, slot_name, zone_points, overlap_threshold, viewpoint_id)
        if result:
            LOGGER.info(f"Successfully added parking slot '{slot_name}' (ID: {result}) to lot {lot_id}, viewpoint_id: {viewpoint_id}")
            # Update cache to reflect new slot
            self._update_cache(lot_id)
        else:
            LOGGER.warning(f"Failed to add parking slot '{slot_name}' to lot {lot_id}")
        return result

    def update_slot_zone(self, lot_id: str, slot_id: str, zone_points: List[Dict[str, float]]) -> bool:
        """
        Updates a parking slot's detection zone.

        Args:
            lot_id: The ID of the lot
            slot_id: The ID of the slot to update
            zone_points: The new detection zone points

        Returns:
            bool: True if update was successful, False otherwise
        """
        LOGGER.debug(f"Updating zone for slot {slot_id} in lot {lot_id} with {len(zone_points)} points")
        result = self._repo.update_slot_zone(lot_id, slot_id, zone_points)
        if result:
            LOGGER.info(f"Successfully updated zone for slot {slot_id} in lot {lot_id}")
            # Update cache to reflect zone changes
            self._update_cache(lot_id)
        else:
            LOGGER.warning(f"Failed to update zone for slot {slot_id} in lot {lot_id}")
        return result

    def update_slot_threshold(self, lot_id: str, slot_id: str, new_threshold: float) -> bool:
        """
        Updates a parking slot's overlap threshold.

        Args:
            lot_id: The ID of the lot
            slot_id: The ID of the slot to update
            new_threshold: The new overlap threshold

        Returns:
            bool: True if update was successful, False otherwise
        """
        LOGGER.debug(f"Updating overlap threshold for slot {slot_id} in lot {lot_id} to {new_threshold}")
        result = self._repo.update_slot_threshold(lot_id, slot_id, new_threshold)
        if result:
            LOGGER.info(f"Successfully updated overlap threshold for slot {slot_id} in lot {lot_id}")
        else:
            LOGGER.warning(f"Failed to update overlap threshold for slot {slot_id} in lot {lot_id}")
        return result

    def update_slot_occupancy(self, lot_id: str, slot_id: str, is_occupied: bool,
                              detection_timestamp: Optional[datetime] = None) -> bool:
        """
        Updates a parking slot's occupancy status.

        Args:
            lot_id: The ID of the lot
            slot_id: The ID of the slot to update
            is_occupied: Whether the slot is occupied
            detection_timestamp: Optional timestamp of when the occupancy was detected

        Returns:
            bool: True if update was successful, False otherwise
        """
        status = "occupied" if is_occupied else "free"
        timestamp_info = f" at {detection_timestamp}" if detection_timestamp else ""
        LOGGER.debug(f"Updating occupancy status for slot {slot_id} in lot {lot_id} to {status}{timestamp_info}")
        result = self._repo.update_slot_occupancy(lot_id, slot_id, is_occupied, detection_timestamp)
        if result:
            LOGGER.info(f"Successfully updated slot {slot_id} in lot {lot_id} to {status}")
            # Efficiently update only the slot occupancy in cache
            self._update_slot_occupancy_in_cache(lot_id, slot_id, is_occupied)
        else:
            LOGGER.warning(f"Failed to update occupancy status for slot {slot_id} in lot {lot_id}")
        return result

    def remove_parking_slot(self, lot_id: str, slot_id: str) -> bool:
        """
        Removes a parking slot.

        Args:
            lot_id: The ID of the lot
            slot_id: The ID of the slot to remove

        Returns:
            bool: True if removal was successful, False otherwise
        """
        LOGGER.debug(f"Removing parking slot {slot_id} from lot {lot_id}")
        result = self._repo.remove_parking_slot(lot_id, slot_id)
        if result:
            LOGGER.info(f"Successfully removed parking slot {slot_id} from lot {lot_id}")
        else:
            LOGGER.warning(f"Failed to remove parking slot {slot_id} from lot {lot_id}")
        return result

    def create_detection_config(self, lot_id: str, engine_type: str,
                               is_enabled: bool = True, update_interval: int = 30,
                               parameters: Dict[str, Any] = None) -> bool:
        """
        Creates or updates a detection configuration.

        Args:
            lot_id: The ID of the lot
            engine_type: The type of detection engine to use
            is_enabled: Whether detection is enabled
            update_interval: The update interval in seconds
            parameters: Additional parameters for the detection engine

        Returns:
            bool: True if creation/update was successful, False otherwise
        """
        LOGGER.debug(f"Creating detection config for lot {lot_id} with engine type {engine_type}")
        LOGGER.debug(f"Config details: enabled={is_enabled}, interval={update_interval}s, parameters={parameters}")
        result = self._repo.create_detection_config(lot_id, engine_type, is_enabled, update_interval, parameters)
        if result:
            LOGGER.info(f"Successfully created detection config for lot {lot_id} with engine type {engine_type}")
        else:
            LOGGER.warning(f"Failed to create detection config for lot {lot_id}")
        return result

    def delete_detection_config(self, lot_id: str) -> bool:
        """
        Deletes a detection configuration.

        Args:
            lot_id: The ID of the lot

        Returns:
            bool: True if deletion was successful, False otherwise
        """
        LOGGER.debug(f"Deleting detection config for lot {lot_id}")
        result = self._repo.delete_detection_config(lot_id)
        if result:
            LOGGER.info(f"Successfully deleted detection config for lot {lot_id}")
        else:
            LOGGER.warning(f"Failed to delete detection config for lot {lot_id}")
        return result

    # --- Deprecated Methods ---
    def update_parking_lot(self, lot_id: str, lot: ParkingLot) -> bool:
        """
        DEPRECATED: Direct state updates are incompatible with event sourcing.
        Use specific command methods (e.g., rename_parking_lot, add_slot, update_slot_zone)
        that generate corresponding events.
        """
        LOGGER.error("Attempted to call deprecated update_parking_lot. Use specific command methods instead.")
        raise NotImplementedError("Direct update_parking_lot is deprecated. Use specific command methods.")

    # --- Backward Compatibility Methods (Temporary) ---
    def get_events_for_lot(self, lot_id: str) -> List[BaseEvent]:
        """
        Retrieves all events for a specific lot, ordered by sequence.
        NOTE: This is mostly for backward compatibility and diagnostics.
        """
        # For EventSourcedStorage, we need to use private method
        if isinstance(self._repo, EventSourcedStorage):
            return self._repo._get_events_for_lot(lot_id)
        # For legacy repositories
        return self._repo.get_events_for_lot(lot_id)

    # Clear all events for a specific lot
    def clear_events(self, lot_id: str) -> bool:
        """
        Clears all events for a specific lot.
        NOTE: This is mostly for backward compatibility and diagnostics.
        """
        # For EventSourcedStorage, we need to use private method
        if isinstance(self._repo, EventSourcedStorage):
            return self._repo._clear_events(lot_id)
        # For legacy repositories
        return self._repo.clear_events(lot_id)

    # Legacy create_parking_lot with explicit lot_id (kept for backward compatibility)
    def _legacy_create_parking_lot(self, lot_id: str, name: str, image_path: Optional[str]) -> bool:
        """
        DEPRECATED: Legacy method that accepts explicit lot_id.
        Use create_parking_lot(name, image_path) instead which generates the ID.
        """
        LOGGER.warning("Using legacy create_parking_lot with explicit lot_id parameter")
        if hasattr(self._repo, "create_parking_lot"):
            # Check if the repo implementation accepts lot_id parameter
            import inspect
            sig = inspect.signature(self._repo.create_parking_lot)
            if "lot_id" in sig.parameters:
                return cast(bool, self._repo.create_parking_lot(lot_id, name, image_path))

        # Otherwise use the new version and ignore the provided lot_id
        LOGGER.warning(f"Ignoring provided lot_id={lot_id}, generating new ID instead")
        result = self._repo.create_parking_lot(name, image_path)
        return result is not None

    # --- Viewpoint Management Methods ---
    def add_viewpoint(self, lot_id: str, name: str, image_path: str,
                     camera_position: Optional[str] = None,
                     is_active: bool = True, sort_order: int = 0) -> Optional[str]:
        """
        Adds a new viewpoint to a parking lot.

        Args:
            lot_id: The ID of the lot
            name: The name of the viewpoint
            image_path: Path to the viewpoint image
            camera_position: Optional camera position description
            is_active: Whether the viewpoint is active
            sort_order: Sort order for the viewpoint

        Returns:
            str: The ID of the created viewpoint, or None if creation failed
        """
        LOGGER.debug(f"Adding new viewpoint '{name}' to lot {lot_id}")
        result = self._repo.add_viewpoint(lot_id, name, image_path, camera_position, is_active, sort_order)
        if result:
            LOGGER.info(f"Successfully added viewpoint '{name}' (ID: {result}) to lot {lot_id}")
            # Update cache to reflect new viewpoint
            self._update_cache(lot_id)
        else:
            LOGGER.warning(f"Failed to add viewpoint '{name}' to lot {lot_id}")
        return result

    def remove_viewpoint(self, lot_id: str, viewpoint_id: str) -> bool:
        """
        Removes a viewpoint from a parking lot.

        Args:
            lot_id: The ID of the lot
            viewpoint_id: The ID of the viewpoint to remove

        Returns:
            bool: True if removal was successful, False otherwise
        """
        LOGGER.debug(f"Removing viewpoint {viewpoint_id} from lot {lot_id}")
        result = self._repo.remove_viewpoint(lot_id, viewpoint_id)
        if result:
            LOGGER.info(f"Successfully removed viewpoint {viewpoint_id} from lot {lot_id}")
            # Update cache to reflect viewpoint removal
            self._update_cache(lot_id)
        else:
            LOGGER.warning(f"Failed to remove viewpoint {viewpoint_id} from lot {lot_id}")
        return result

    def update_viewpoint(self, lot_id: str, viewpoint_id: str, name: Optional[str] = None,
                        image_path: Optional[str] = None, camera_position: Optional[str] = None,
                        is_active: Optional[bool] = None, sort_order: Optional[int] = None) -> bool:
        """
        Updates a viewpoint's properties.

        Args:
            lot_id: The ID of the lot
            viewpoint_id: The ID of the viewpoint to update
            name: Optional new name
            image_path: Optional new image path
            camera_position: Optional new camera position
            is_active: Optional new active status
            sort_order: Optional new sort order

        Returns:
            bool: True if update was successful, False otherwise
        """
        # Build updates dictionary with only non-None values
        updates = {}
        if name is not None:
            updates['name'] = name
        if image_path is not None:
            updates['image_path'] = image_path
        if camera_position is not None:
            updates['camera_position'] = camera_position
        if is_active is not None:
            updates['is_active'] = is_active
        if sort_order is not None:
            updates['sort_order'] = sort_order

        return self._repo.update_viewpoint(lot_id, viewpoint_id, **updates)

    def set_default_viewpoint(self, lot_id: str, viewpoint_id: str) -> bool:
        """
        Sets the default viewpoint for a parking lot.

        Args:
            lot_id: The ID of the lot
            viewpoint_id: The ID of the viewpoint to set as default

        Returns:
            bool: True if setting was successful, False otherwise
        """
        return self._repo.set_default_viewpoint(lot_id, viewpoint_id)

    def add_viewpoint_zone(self, lot_id: str, viewpoint_id: str, slot_id: str, 
                          zone_points: List[Dict[str, float]]) -> bool:
        """
        Adds a zone to a viewpoint.

        Args:
            lot_id: The ID of the lot
            viewpoint_id: The ID of the viewpoint
            slot_id: The ID of the parking slot
            zone_points: List of points defining the zone

        Returns:
            bool: True if addition was successful, False otherwise
        """
        return self._repo.add_viewpoint_zone(lot_id, viewpoint_id, slot_id, zone_points)

    def remove_viewpoint_zone(self, lot_id: str, viewpoint_id: str, slot_id: str) -> bool:
        """
        Removes a zone from a viewpoint.

        Args:
            lot_id: The ID of the lot
            viewpoint_id: The ID of the viewpoint
            slot_id: The ID of the parking slot

        Returns:
            bool: True if removal was successful, False otherwise
        """
        LOGGER.debug(f"Removing zone for slot {slot_id} from viewpoint {viewpoint_id} in lot {lot_id}")
        result = self._repo.remove_viewpoint_zone(lot_id, viewpoint_id, slot_id)
        if result:
            LOGGER.info(f"Successfully removed zone for slot {slot_id} from viewpoint {viewpoint_id} in lot {lot_id}")
            # Update cache to reflect zone removal
            self._update_cache(lot_id)
        else:
            LOGGER.warning(f"Failed to remove zone for slot {slot_id} from viewpoint {viewpoint_id} in lot {lot_id}")
        return result

    # --- Stream Management Methods ---
    def configure_viewpoint_stream(self, lot_id: str, viewpoint_id: str,
                                  stream_url: str, capture_fps: float = 1.0) -> bool:
        """
        Configure stream settings for a viewpoint.

        Args:
            lot_id: The ID of the lot
            viewpoint_id: The ID of the viewpoint
            stream_url: The stream URL
            capture_fps: Frame capture rate

        Returns:
            bool: True if configuration was successful, False otherwise
        """
        LOGGER.debug(f"Configuring stream for viewpoint {viewpoint_id} in lot {lot_id}")
        result = self._repo.configure_viewpoint_stream(lot_id, viewpoint_id, stream_url, capture_fps)
        if result:
            LOGGER.info(f"Successfully configured stream for viewpoint {viewpoint_id} in lot {lot_id}")
            # Update cache to reflect stream configuration
            self._update_cache(lot_id)
        else:
            LOGGER.warning(f"Failed to configure stream for viewpoint {viewpoint_id} in lot {lot_id}")
        return result

    def activate_viewpoint_stream(self, lot_id: str, viewpoint_id: str) -> bool:
        """
        Activate stream capture for a viewpoint.

        Args:
            lot_id: The ID of the lot
            viewpoint_id: The ID of the viewpoint

        Returns:
            bool: True if activation was successful, False otherwise
        """
        LOGGER.debug(f"Activating stream for viewpoint {viewpoint_id} in lot {lot_id}")
        result = self._repo.activate_viewpoint_stream(lot_id, viewpoint_id)
        if result:
            LOGGER.info(f"Successfully activated stream for viewpoint {viewpoint_id} in lot {lot_id}")
            # Update cache to reflect stream activation
            self._update_cache(lot_id)
        else:
            LOGGER.warning(f"Failed to activate stream for viewpoint {viewpoint_id} in lot {lot_id}")
        return result

    def deactivate_viewpoint_stream(self, lot_id: str, viewpoint_id: str) -> bool:
        """
        Deactivate stream capture for a viewpoint.

        Args:
            lot_id: The ID of the lot
            viewpoint_id: The ID of the viewpoint

        Returns:
            bool: True if deactivation was successful, False otherwise
        """
        return self._repo.deactivate_viewpoint_stream(lot_id, viewpoint_id)



# Make the singleton instance easily accessible
StorageService = Storage.get_instance()
