/**
 * <PERSON>les drawing polygons on the image
 */
class PolygonDrawing {
    constructor(imageContainer, parkingImage) {
        this.imageContainer = imageContainer;
        this.parkingImage = parkingImage;
        this.isDrawing = false;
        this.currentPoints = [];
        this.elements = [];
        this.imageRect = null;
    }

    /**
     * Start drawing mode
     */
    startDrawing() {
        this.isDrawing = true;
        this.currentPoints = [];
        this.elements = [];
        this.imageRect = this.parkingImage.getBoundingClientRect();
        this.imageContainer.classList.add('drawing-active');
    }

    /**
     * Cancel drawing and clean up
     */
    cancelDrawing() {
        this.isDrawing = false;
        this.currentPoints = [];
        this.imageContainer.classList.remove('drawing-active');

        // Remove any drawing elements
        this.elements.forEach(el => el.remove());
        this.elements = [];
    }

    /**
     * Handle click on the image with zoom adjustment
     * @param {MouseEvent} e - Click event
     * @param {Object} zoomInfo - Zoom information containing offsetX, offsetY, and level
     */
    handleClickWithZoom(e, zoomInfo) {
        if (!this.isDrawing) return;

        // Always get the fresh image rectangle to ensure accurate dimensions
        this.imageRect = this.parkingImage.getBoundingClientRect();

        // Convert click coordinates to unzoomed image space
        const point = this.getUnzoomedCoordinates(e, zoomInfo);

        // Make sure point is within bounds
        const adjustedPoint = this.constrainToImageBounds(point);

        // Add point using common handler
        this.addPoint(adjustedPoint.x, adjustedPoint.y);
    }

    /**
     * Convert screen coordinates to unzoomed image coordinates
     * @param {MouseEvent} e - Mouse event with clientX/clientY
     * @param {Object} zoomInfo - Current zoom info (level, offsetX, offsetY)
     * @returns {Object} Coordinates in unzoomed image space
     */
    getUnzoomedCoordinates(e, zoomInfo) {
        // Instead of our own calculation, defer to the zoom manager if available
        if (window.zoomManagerInstance) {
            return window.zoomManagerInstance.getUnzoomedCoordinates(e.clientX, e.clientY);
        }

        // Fall back to original method if no zoom manager available
        const rect = this.imageRect;

        // Relative position within the visible area
        const relativeX = e.clientX - rect.left;
        const relativeY = e.clientY - rect.top;

        // Convert to unzoomed coordinates by accounting for offset and zoom level
        return {
            x: (relativeX - zoomInfo.offsetX) / zoomInfo.level,
            y: (relativeY - zoomInfo.offsetY) / zoomInfo.level
        };
    }

    /**
     * Constrain a point to be within image bounds
     * @param {Object} point - The point to constrain
     * @returns {Object} The constrained point
     */
    constrainToImageBounds(point) {
        return {
            x: Math.max(0, Math.min(point.x, this.parkingImage.offsetWidth)),
            y: Math.max(0, Math.min(point.y, this.parkingImage.offsetHeight))
        };
    }

    /**
     * Add a point to the drawing at the specified coordinates
     * @param {number} x - X coordinate in image space
     * @param {number} y - Y coordinate in image space
     */
    addPoint(x, y) {
        // Always refresh image dimensions to ensure accuracy
        this.imageRect = this.parkingImage.getBoundingClientRect();

        // Add point to the current drawing using percentage coordinates
        const percentX = (x / this.parkingImage.offsetWidth) * 100;
        const percentY = (y / this.parkingImage.offsetHeight) * 100;

        const point = {x, y};
        this.currentPoints.push(point);

        // Create visual point element using percentages for stable positioning
        const pointElement = document.createElement('div');
        pointElement.className = 'drawing-point';
        pointElement.style.left = `${percentX}%`;
        pointElement.style.top = `${percentY}%`;
        pointElement.dataset.percentX = percentX;
        pointElement.dataset.percentY = percentY;
        pointElement.dataset.vertexType = 'drawing-point';

        // Scale point size based on zoom level if ZoomManager is available
        if (window.zoomManagerInstance && window.zoomManagerInstance.zoomLevel > 1) {
            pointElement.style.transform = `translate(-50%, -50%) scale(${1/window.zoomManagerInstance.zoomLevel})`;
        } else {
            pointElement.style.transform = 'translate(-50%, -50%)';
        }

        this.imageContainer.appendChild(pointElement);
        this.elements.push(pointElement);

        // Update the temporary polygon
        this.updateTemporaryPolygon();
    }

    /**
     * Update the temporary polygon being drawn
     */
    updateTemporaryPolygon() {
        // Always update the image rectangle for latest dimensions
        this.imageRect = this.parkingImage.getBoundingClientRect();
        
        // Remove old polygon and lines
        this.clearTemporaryElements();

        // Need at least 2 points to draw lines
        if (this.currentPoints.length < 2) return;

        // Draw lines between points using percentages
        this.drawConnectingLines();

        // Create polygon if we have at least 3 points
        if (this.currentPoints.length >= 3) {
            this.createTemporaryPolygon();
        }

        // Scale points based on zoom level
        const zoomLevel = window.zoomManagerInstance ? window.zoomManagerInstance.zoomLevel : 1;
        if (zoomLevel > 1) {
            this.elements.forEach(el => {
                if (el.classList.contains('drawing-point')) {
                    el.style.transform = `translate(-50%, -50%) scale(${1/zoomLevel})`;
                }
            });
        }
    }

    /**
     * Clear temporary polygon and connecting lines
     */
    clearTemporaryElements() {
        // Find and remove the old polygon
        const oldPolygon = this.elements.find(el => el.classList.contains('zone-polygon'));
        if (oldPolygon) {
            oldPolygon.remove();
            this.elements = this.elements.filter(el => el !== oldPolygon);
        }

        // Remove old lines
        const lines = this.elements.filter(el => el.classList.contains('overlay-line'));
        lines.forEach(el => {
            el.remove();
            this.elements = this.elements.filter(e => e !== el);
        });
    }

    /**
     * Draw lines connecting the current points using percentage-based positioning
     */
    drawConnectingLines() {
        for (let i = 0; i < this.currentPoints.length; i++) {
            const p1 = this.currentPoints[i];
            // Connect to next point or back to first if last point
            const p2 = this.currentPoints[(i + 1) % this.currentPoints.length];

            // Calculate percentage coordinates for both points
            const p1PercentX = (p1.x / this.parkingImage.offsetWidth) * 100;
            const p1PercentY = (p1.y / this.parkingImage.offsetHeight) * 100;
            const p2PercentX = (p2.x / this.parkingImage.offsetWidth) * 100;
            const p2PercentY = (p2.y / this.parkingImage.offsetHeight) * 100;

            // Create line with percentage-based attributes
            const line = document.createElement('div');
            line.className = 'overlay-line';
            line.dataset.vertexType = 'overlay-line';

            // Calculate length as percentage of image width
            const imgWidth = this.parkingImage.offsetWidth;
            const deltaX = p2.x - p1.x;
            const deltaY = p2.y - p1.y;
            const length = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
            const lengthPercent = (length / imgWidth) * 100;

            // Calculate angle
            const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI;

            // Position line using percentages
            line.style.left = `${p1PercentX}%`;
            line.style.top = `${p1PercentY}%`;
            line.style.width = `${lengthPercent}%`;
            line.style.transform = `rotate(${angle}deg)`;
            line.style.transformOrigin = '0 0';

            this.imageContainer.appendChild(line);
            this.elements.push(line);
        }
    }

    /**
     * Create a temporary polygon from the current points using percentage-based clipping
     */
    createTemporaryPolygon() {
        const polygon = document.createElement('div');
        polygon.className = 'zone-polygon';
        polygon.dataset.vertexType = 'temp-polygon';

        // Set consistent positioning properties
        polygon.style.position = 'absolute';
        polygon.style.width = `${this.parkingImage.offsetWidth}px`;
        polygon.style.height = `${this.parkingImage.offsetHeight}px`;
        polygon.style.left = '0';
        polygon.style.top = '0';
        polygon.style.margin = '0';
        polygon.style.padding = '0';
        polygon.style.transformOrigin = '0 0';

        // Convert to percentage coordinates for clip-path
        const percentagePoints = this.currentPoints.map(p => ({
            x: (p.x / this.parkingImage.offsetWidth) * 100,
            y: (p.y / this.parkingImage.offsetHeight) * 100
        }));

        // Apply clip-path using percentage coordinates
        const clipPath = `polygon(${percentagePoints.map(p => `${p.x}% ${p.y}%`).join(', ')})`;
        polygon.style.clipPath = clipPath;
        polygon.style.webkitClipPath = clipPath;

        this.imageContainer.appendChild(polygon);
        this.elements.push(polygon);
    }

    /**
     * Finish drawing the polygon
     * @returns {Array} The current points of the polygon
     */
    finishDrawing() {
        this.isDrawing = false;
        this.imageContainer.classList.remove('drawing-active');
        return [...this.currentPoints]; // Return a copy of the points array
    }

    /**
     * Check if the polygon is valid
     * @returns {boolean} True if polygon has at least 3 points
     */
    isValidPolygon() {
        return this.currentPoints.length >= 3;
    }
}
