import cv2
import time
import os
from datetime import datetime
import argparse
import requests
import logging
import sys
import numpy as np

logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(levelname)s - %(message)s')

def setup_stream(rtsp_url: str, attempts: int = 3) -> cv2.VideoCapture:
    """
    Set up video capture with optimized H.264 settings
    """
    # Configure FFmpeg options for better H.264 handling
    os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'protocol_whitelist;file,rtp,udp,tcp,rtsp|rtsp_transport;tcp|max_delay;500000|fflags;nobuffer|flags;low_delay'
    
    for i in range(attempts):
        try:
            cap = cv2.VideoCapture(rtsp_url, cv2.CAP_FFMPEG)
            if not cap.isOpened():
                if i < attempts - 1:
                    logging.warning(f"Attempt {i+1}/{attempts} failed, retrying...")
                    time.sleep(2)
                    continue
                raise ValueError(f"Failed to open RTSP stream after {attempts} attempts")
            
            # Configure stream parameters
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Minimize buffer size
            cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('H', '2', '6', '4'))
            # Request keyframes more frequently
            cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('H', '2', '6', '4'))
            
            return cap
            
        except Exception as e:
            if i == attempts - 1:
                raise ValueError(f"Failed to initialize stream: {str(e)}")
            logging.error(f"Error during setup attempt {i+1}: {str(e)}")
            time.sleep(2)
    
    raise ValueError("Failed to set up stream")

def validate_frame(frame: np.ndarray) -> bool:
    """
    Validate frame quality and check for severe corruption
    """
    if frame is None:
        return False
        
    # Check if frame is empty or has invalid dimensions
    if frame.size == 0 or frame.shape[0] == 0 or frame.shape[1] == 0:
        return False
        
    # Check for completely black or white frames
    if np.mean(frame) < 1 or np.mean(frame) > 254:
        return False
        
    # Check for severe corruption (high contrast areas)
    try:
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        if np.var(laplacian) < 1:  # Too smooth, might be corrupted
            return False
    except Exception:
        return False
        
    return True

def capture_and_upload_stream(rtsp_url: str, lot_id: str, target_fps: float, api_url: str):
    """
    Capture frames from RTSP stream and upload them at specified FPS.
    Includes enhanced error handling and frame validation.
    """
    logging.info(f"Attempting to connect to RTSP stream: {rtsp_url}")
    
    cap = setup_stream(rtsp_url)
    
    # Get and log stream properties
    actual_fps = cap.get(cv2.CAP_PROP_FPS)
    frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    logging.info(f"Stream properties - FPS: {actual_fps}, Resolution: {frame_width}x{frame_height}")
    
    # Use target FPS if actual FPS is invalid or too high
    upload_fps = target_fps if actual_fps <= 0 or actual_fps > 30 else min(target_fps, actual_fps)
    frame_interval = 1.0 / upload_fps
    
    endpoint = f"{api_url}/api/lots/{lot_id}/image"
    last_upload_time = 0
    frames_received = 0
    frames_uploaded = 0
    consecutive_errors = 0
    max_consecutive_errors = 10
    last_frame_time = time.time()
    reconnect_timeout = 5.0
    
    try:
        while True:
            current_time = time.time()
            
            if current_time - last_upload_time < frame_interval:
                # Drop frame by reading and immediately discarding
                cap.grab()
                time.sleep(0.001)
                continue
            
            # Read frame with validation
            ret = cap.grab()
            if not ret:
                consecutive_errors += 1
                if consecutive_errors >= max_consecutive_errors:
                    logging.error(f"Too many consecutive errors ({consecutive_errors}), attempting reconnection")
                    cap.release()
                    try:
                        cap = setup_stream(rtsp_url)
                        consecutive_errors = 0
                    except Exception as e:
                        logging.error(f"Failed to reconnect: {str(e)}")
                        raise
                continue
            
            ret, frame = cap.retrieve()
            if not ret or not validate_frame(frame):
                logging.warning("Invalid or corrupted frame detected, skipping")
                consecutive_errors += 1
                continue
            
            consecutive_errors = 0  # Reset error counter on successful frame
            frames_received += 1
            last_frame_time = time.time()
            
            # Convert frame to JPEG in memory with optimized settings
            try:
                # Use optimal JPEG encoding parameters
                encode_params = [int(cv2.IMWRITE_JPEG_QUALITY), 95,
                               int(cv2.IMWRITE_JPEG_OPTIMIZE), 1,
                               int(cv2.IMWRITE_JPEG_PROGRESSIVE), 0]
                success, buffer = cv2.imencode('.jpg', frame, encode_params)
                if not success:
                    logging.error("Failed to encode frame as JPEG")
                    continue
            except Exception as e:
                logging.error(f"Error encoding frame: {str(e)}")
                continue
            
            # Upload the frame
            try:
                headers = {'Content-Type': 'image/jpeg'}
                response = requests.post(
                    endpoint,
                    data=buffer.tobytes(),
                    headers=headers,
                    timeout=5
                )
                
                if response.status_code == 200:
                    frames_uploaded += 1
                    if frames_uploaded % 10 == 0:
                        logging.info(f"Stats - Received: {frames_received}, Uploaded: {frames_uploaded}, "
                                   f"Drop rate: {((frames_received-frames_uploaded)/frames_received)*100:.1f}%")
                else:
                    logging.error(f"Failed to upload frame: Status {response.status_code}")
            
            except requests.exceptions.Timeout:
                logging.error("Upload timeout")
            except requests.exceptions.ConnectionError as e:
                logging.error(f"Connection error: {str(e)}")
                time.sleep(1)
            except Exception as e:
                logging.error(f"Error uploading frame: {str(e)}")
            
            last_upload_time = time.time()
    
    except KeyboardInterrupt:
        logging.info(f"Stopping stream capture. Final stats - Received: {frames_received}, "
                    f"Uploaded: {frames_uploaded}, Drop rate: {((frames_received-frames_uploaded)/frames_received)*100:.1f}%")
    except Exception as e:
        logging.error(f"Unexpected error: {str(e)}")
        raise
    finally:
        if cap is not None:
            cap.release()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Capture and upload frames from RTSP stream')
    parser.add_argument('--rtsp-url', required=True,
                        help='RTSP stream URL')
    parser.add_argument('--lot-id', required=True,
                        help='Parking lot ID')
    parser.add_argument('--fps', type=float, default=1.0,
                        help='Target frames per second to upload')
    parser.add_argument('--api-url', default='http://localhost:5000',
                        help='Base API URL')
    
    args = parser.parse_args()
    
    capture_and_upload_stream(args.rtsp_url, args.lot_id, args.fps, args.api_url)