"""
Pydantic schemas for FastAPI request/response models
Maintains exact compatibility with Flask API responses
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional, Union
from datetime import datetime

# ============================================================================
# Request Models
# ============================================================================

class CreateLotRequest(BaseModel):
    """Request model for creating a parking lot"""
    name: str = Field(..., description="Name of the parking lot")

class CreateSlotRequest(BaseModel):
    """Request model for creating a parking slot"""
    slot_name: str = Field(..., description="Name of the parking slot")
    coordinates: List[List[int]] = Field(..., description="Polygon coordinates for detection zone")
    viewpoint_id: Optional[str] = Field(None, description="Associated viewpoint ID")
    overlap_threshold: Optional[float] = Field(0.3, description="Overlap threshold for detection")

class UpdateSlotStatusRequest(BaseModel):
    """Request model for updating slot occupancy status"""
    is_occupied: bool = Field(..., description="Whether the slot is occupied")

class CreateLotWithStreamRequest(BaseModel):
    """Request model for creating a parking lot with video stream"""
    name: str = Field(..., description="Name of the parking lot")
    stream_url: str = Field(..., description="Video stream URL (RTSP, HTTP, etc.)")
    capture_fps: float = Field(1.0, description="Frame capture rate (frames per second)")
    viewpoint_name: str = Field("Main Camera", description="Name for the main viewpoint")

class UpdateSlotZoneRequest(BaseModel):
    """Request model for updating a parking slot's detection zone"""
    coordinates: List[List[int]] = Field(..., description="Polygon coordinates for detection zone")
    overlap_threshold: Optional[float] = Field(None, description="Overlap threshold for detection")

class DetectionConfigRequest(BaseModel):
    """Request model for detection engine configuration"""
    engine_type: str = Field(..., description="Type of detection engine")
    is_enabled: bool = Field(True, description="Whether detection is enabled")
    update_interval: int = Field(30, description="Update interval in seconds")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Engine-specific parameters")

class CreateViewpointRequest(BaseModel):
    """Request model for creating a viewpoint"""
    name: str = Field(..., description="Name of the viewpoint")
    camera_position: Optional[Dict[str, Any]] = Field(None, description="Camera position data")
    is_active: bool = Field(True, description="Whether the viewpoint is active")
    sort_order: int = Field(0, description="Sort order for display")

class UpdateViewpointRequest(BaseModel):
    """Request model for updating a viewpoint"""
    name: Optional[str] = Field(None, description="Name of the viewpoint")
    camera_position: Optional[Dict[str, Any]] = Field(None, description="Camera position data")
    is_active: Optional[bool] = Field(None, description="Whether the viewpoint is active")
    sort_order: Optional[int] = Field(None, description="Sort order for display")

# ============================================================================
# Response Models
# ============================================================================

class PointResponse(BaseModel):
    """Response model for geometric points"""
    x: float
    y: float

class PolygonResponse(BaseModel):
    """Response model for geometric polygons"""
    points: List[PointResponse]

class ParkingSlotResponse(BaseModel):
    """Response model for parking slots"""
    id: str
    slot_name: str
    detection_zone: Optional[PolygonResponse] = None
    is_occupied: bool
    overlap_threshold: float
    viewpoint_id: Optional[str] = None

class ViewpointZoneResponse(BaseModel):
    """Response model for viewpoint zones"""
    slot_id: str
    slot_name: str

class ViewpointResponse(BaseModel):
    """Response model for viewpoints"""
    id: str
    name: str
    image_path: str
    camera_position: Optional[Dict[str, Any]] = None
    is_active: bool
    sort_order: int
    zones: List[ViewpointZoneResponse] = []
    stream_url: Optional[str] = None
    capture_fps: float = 1.0
    is_stream_active: bool = False
    stream_status: str = "inactive"
    stream_error_message: Optional[str] = None
    last_frame_time: Optional[float] = None

class ViewpointListItemResponse(BaseModel):
    """Response model for viewpoint list items (with additional computed fields)"""
    id: str
    name: str
    image_path: str
    camera_position: Optional[Dict[str, Any]] = None
    is_active: bool
    sort_order: int
    zone_count: int
    is_default: bool
    stream_url: Optional[str] = None
    capture_fps: float = 1.0
    is_stream_active: bool = False
    stream_status: str = "inactive"
    stream_error_message: Optional[str] = None
    last_frame_time: Optional[float] = None

class DetectionConfigResponse(BaseModel):
    """Response model for detection configuration"""
    engine_type: str
    is_enabled: bool
    update_interval: int
    parameters: Dict[str, Any]

class ParkingEventResponse(BaseModel):
    """Response model for parking events"""
    id: str
    lot_id: str
    event_type: str
    description: str
    timestamp: datetime

class ParkingLotResponse(BaseModel):
    """Response model for parking lots"""
    id: str
    name: str
    image_path: Optional[str] = None
    slots: List[ParkingSlotResponse] = []
    detection_config: Optional[DetectionConfigResponse] = None
    events: List[ParkingEventResponse] = []
    viewpoints: List[ViewpointResponse] = []
    default_viewpoint_id: Optional[str] = None

class ParkingLotStatsResponse(BaseModel):
    """Response model for parking lot statistics"""
    success: bool
    updates: int
    occupancy_rate: float
    free_slots: int
    busy_slots: int

# ============================================================================
# API Response Wrappers
# ============================================================================

class ApiSuccessResponse(BaseModel):
    """Standard success response wrapper"""
    success: bool = True
    message: Optional[str] = None
    data: Optional[Any] = None

class ApiErrorResponse(BaseModel):
    """Standard error response wrapper"""
    error: str
    success: bool = False

class CreateLotResponse(BaseModel):
    """Response for lot creation"""
    id: str
    message: str

class ImageUploadResponse(BaseModel):
    """Response for image upload"""
    message: str
    image_path: str

class EventsResponse(BaseModel):
    """Response for paginated events"""
    events: List[ParkingEventResponse]
    total: int
    page: int
    per_page: int

class ViewpointsResponse(BaseModel):
    """Response for viewpoints list"""
    viewpoints: List[ViewpointListItemResponse]
    default_viewpoint_id: Optional[str] = None
    total_count: int

class DetectionClassesResponse(BaseModel):
    """Response for detection classes"""
    classes: List[str]

class ClearEventsResponse(BaseModel):
    """Response for clearing events"""
    success: bool

# ============================================================================
# Utility Functions for Model Conversion
# ============================================================================

def convert_parking_lot_to_response(lot) -> ParkingLotResponse:
    """Convert domain model to response model"""
    # Convert slots
    slots = []
    for slot in lot.slots:
        detection_zone = None
        if slot.detection_zone:
            points = [PointResponse(x=p.x, y=p.y) for p in slot.detection_zone.points]
            detection_zone = PolygonResponse(points=points)
        
        slots.append(ParkingSlotResponse(
            id=slot.id,
            slot_name=slot.slot_name,
            detection_zone=detection_zone,
            is_occupied=slot.is_occupied,
            overlap_threshold=slot.overlap_threshold,
            viewpoint_id=slot.viewpoint_id
        ))
    
    # Convert detection config
    detection_config = None
    if lot.detection_config:
        detection_config = DetectionConfigResponse(
            engine_type=lot.detection_config.engine_type,
            is_enabled=lot.detection_config.is_enabled,
            update_interval=lot.detection_config.update_interval,
            parameters=lot.detection_config.parameters
        )
    
    # Convert events
    events = []
    for event in lot.events:
        events.append(ParkingEventResponse(
            id=event.id,
            lot_id=event.lot_id,
            event_type=event.event_type,
            description=event.description,
            timestamp=event.timestamp
        ))
    
    # Convert viewpoints
    viewpoints = []
    for viewpoint in lot.viewpoints:
        zones = [ViewpointZoneResponse(slot_id=z.slot_id, slot_name=z.slot_name) 
                for z in viewpoint.zones]
        
        viewpoints.append(ViewpointResponse(
            id=viewpoint.id,
            name=viewpoint.name,
            image_path=viewpoint.image_path,
            camera_position=viewpoint.camera_position,
            is_active=viewpoint.is_active,
            sort_order=viewpoint.sort_order,
            zones=zones
        ))
    
    return ParkingLotResponse(
        id=lot.id,
        name=lot.name,
        image_path=lot.image_path,
        slots=slots,
        detection_config=detection_config,
        events=events,
        viewpoints=viewpoints,
        default_viewpoint_id=lot.default_viewpoint_id
    )

def convert_parking_slot_to_response(slot) -> ParkingSlotResponse:
    """Convert slot domain model to response model"""
    detection_zone = None
    if slot.detection_zone:
        points = [PointResponse(x=p.x, y=p.y) for p in slot.detection_zone.points]
        detection_zone = PolygonResponse(points=points)
    
    return ParkingSlotResponse(
        id=slot.id,
        slot_name=slot.slot_name,
        detection_zone=detection_zone,
        is_occupied=slot.is_occupied,
        overlap_threshold=slot.overlap_threshold,
        viewpoint_id=slot.viewpoint_id
    )
