{% extends "base_layout.html" %}

{% block title %}Upload Parking Lot Image{% endblock %}

{% block content %}
<h1>Upload Parking Lot Image</h1>

<!-- Flash messages are now handled by SocketIO notifications in base_layout.html -->

<form method="post" enctype="multipart/form-data" class="mt-4" id="upload-form">
    <div class="mb-3">
        <label for="name" class="form-label">Parking Lot Name</label>
        <input type="text" class="form-control" id="name" name="name" required>
    </div>

    <!-- Source Type Selection -->
    <div class="mb-3">
        <label class="form-label">Image Source</label>
        <div class="form-check">
            <input class="form-check-input" type="radio" name="source_type" id="source_image" value="image" checked>
            <label class="form-check-label" for="source_image">
                Upload Image File
            </label>
        </div>
        <div class="form-check">
            <input class="form-check-input" type="radio" name="source_type" id="source_stream" value="stream">
            <label class="form-check-label" for="source_stream">
                Video Stream URL
            </label>
        </div>
    </div>

    <!-- Image Upload Section -->
    <div class="mb-3" id="image-upload-section">
        <label for="file" class="form-label">Select Parking Lot Image</label>
        <input class="form-control" type="file" id="file" name="file" accept=".jpg,.jpeg,.png">
        <div class="form-text">Supported formats: JPG, PNG</div>
    </div>

    <!-- Stream URL Section -->
    <div class="mb-3" id="stream-url-section" style="display: none;">
        <label for="stream_url" class="form-label">Video Stream URL</label>
        <input type="url" class="form-control" id="stream_url" name="stream_url"
               placeholder="rtsp://camera.example.com/stream or http://camera.example.com/mjpeg">
        <div class="form-text">Supported protocols: RTSP, HTTP/MJPEG, HTTPS</div>

        <div class="row mt-3">
            <div class="col-md-6">
                <label for="capture_fps" class="form-label">Capture Rate (FPS)</label>
                <input type="number" class="form-control" id="capture_fps" name="capture_fps"
                       value="1.0" min="0.1" max="30" step="0.1">
                <div class="form-text">Frames per second to capture from stream</div>
            </div>
            <div class="col-md-6">
                <label for="viewpoint_name" class="form-label">Camera Name</label>
                <input type="text" class="form-control" id="viewpoint_name" name="viewpoint_name"
                       value="Main Camera">
                <div class="form-text">Name for this camera viewpoint</div>
            </div>
        </div>
    </div>

    <div class="mb-3">
        <button type="submit" class="btn btn-primary" id="upload-btn">Create Parking Lot</button>
        <a href="/upload" class="btn btn-secondary ms-2">Cancel</a>
    </div>

    <!-- Debug info -->
    <div id="debug-info" class="mt-3" style="display: none;">
        <div class="alert alert-info">
            <h6>Debug Information:</h6>
            <div id="debug-content"></div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('UPLOAD DEBUG: Upload page loaded');

    const form = document.getElementById('upload-form');
    const uploadBtn = document.getElementById('upload-btn');
    const nameInput = document.getElementById('name');
    const fileInput = document.getElementById('file');
    const debugInfo = document.getElementById('debug-info');
    const debugContent = document.getElementById('debug-content');

    // Source type elements
    const sourceImageRadio = document.getElementById('source_image');
    const sourceStreamRadio = document.getElementById('source_stream');
    const imageUploadSection = document.getElementById('image-upload-section');
    const streamUrlSection = document.getElementById('stream-url-section');
    const streamUrlInput = document.getElementById('stream_url');

    // Handle source type switching
    function toggleSourceType() {
        if (sourceStreamRadio.checked) {
            imageUploadSection.style.display = 'none';
            streamUrlSection.style.display = 'block';
            fileInput.removeAttribute('required');
            streamUrlInput.setAttribute('required', 'required');
            uploadBtn.textContent = 'Create Parking Lot with Stream';
        } else {
            imageUploadSection.style.display = 'block';
            streamUrlSection.style.display = 'none';
            fileInput.setAttribute('required', 'required');
            streamUrlInput.removeAttribute('required');
            uploadBtn.textContent = 'Create Parking Lot';
        }
        updateDebugInfo();
    }

    // Add event listeners for source type switching
    sourceImageRadio.addEventListener('change', toggleSourceType);
    sourceStreamRadio.addEventListener('change', toggleSourceType);

    // Show debug info
    debugInfo.style.display = 'block';

    function updateDebugInfo() {
        const fileName = fileInput.files.length > 0 ? fileInput.files[0].name : 'None';
        const fileSize = fileInput.files.length > 0 ? fileInput.files[0].size : 0;
        const nameValue = nameInput.value || 'Empty';
        const sourceType = sourceStreamRadio.checked ? 'Stream' : 'Image';
        const streamUrl = streamUrlInput.value || 'None';

        debugContent.innerHTML = `
            <strong>Name:</strong> ${nameValue}<br>
            <strong>Source Type:</strong> ${sourceType}<br>
            <strong>File:</strong> ${fileName}<br>
            <strong>File Size:</strong> ${fileSize} bytes<br>
            <strong>Stream URL:</strong> ${streamUrl}<br>
            <strong>Form Action:</strong> ${form.action || 'Default (current URL)'}<br>
            <strong>Form Method:</strong> ${form.method}<br>
            <strong>Form Enctype:</strong> ${form.enctype}
        `;
    }

    // Update debug info on changes
    nameInput.addEventListener('input', updateDebugInfo);
    fileInput.addEventListener('change', updateDebugInfo);
    streamUrlInput.addEventListener('input', updateDebugInfo);

    // Initial update
    toggleSourceType();
    updateDebugInfo();

    // Form submission handler
    form.addEventListener('submit', function(e) {
        console.log('UPLOAD DEBUG: Form submission started');
        console.log('UPLOAD DEBUG: Name:', nameInput.value);
        console.log('UPLOAD DEBUG: Source type:', sourceStreamRadio.checked ? 'Stream' : 'Image');

        if (!nameInput.value.trim()) {
            console.error('UPLOAD DEBUG: No name provided');
            e.preventDefault();
            alert('Please enter a parking lot name');
            return false;
        }

        if (sourceStreamRadio.checked) {
            // Stream validation
            if (!streamUrlInput.value.trim()) {
                console.error('UPLOAD DEBUG: No stream URL provided');
                e.preventDefault();
                alert('Please enter a stream URL');
                return false;
            }
            console.log('UPLOAD DEBUG: Stream URL:', streamUrlInput.value);
        } else {
            // File validation
            if (fileInput.files.length === 0) {
                console.error('UPLOAD DEBUG: No file selected');
                e.preventDefault();
                alert('Please select a file');
                return false;
            }
            console.log('UPLOAD DEBUG: File:', fileInput.files[0]);
        }

        // Disable button to prevent double submission
        uploadBtn.disabled = true;
        uploadBtn.textContent = sourceStreamRadio.checked ? 'Creating with Stream...' : 'Uploading...';

        console.log('UPLOAD DEBUG: Form validation passed, submitting...');

        // Let the form submit naturally
        return true;
    });

    // Listen for page unload to detect if we're navigating away
    window.addEventListener('beforeunload', function() {
        console.log('UPLOAD DEBUG: Page is unloading');
    });

    // Listen for errors
    window.addEventListener('error', function(e) {
        console.error('UPLOAD DEBUG: JavaScript error:', e.error);
    });
});
</script>
{% endblock %}