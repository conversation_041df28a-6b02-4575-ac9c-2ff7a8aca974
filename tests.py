#!/usr/bin/env python
"""
Test runner script for ParkingMan project

This script configures the Python path to ensure tests can find the project modules,
then runs pytest with coverage reporting.

Usage:
    python tests.py               # Run all tests
    python tests.py services      # Run only service tests
    python tests.py -xvs services # Pass pytest options
"""
import sys
import os
from pathlib import Path
import subprocess

# Get the project root directory
PROJECT_ROOT = Path(__file__).resolve().parent

def main():
    """Run the tests with proper path configuration"""
    # Add the app directory to Python path for new structure
    app_root = PROJECT_ROOT / "app"
    sys.path.insert(0, str(app_root))
    os.environ['PYTHONPATH'] = str(app_root)

    # Default pytest arguments
    pytest_args = [
        'pytest',
        '--cov=src.services,src.models,src.utils',  # Measure coverage for main modules
        '--cov-report=term',            # Report to terminal
        '--cov-report=html:coverage_html',  # Generate HTML report
    ]
    
    # Add any command line arguments
    if len(sys.argv) > 1:
        pytest_args.extend(sys.argv[1:])
    
    # Run pytest with the configured arguments
    subprocess.run(pytest_args)

if __name__ == '__main__':
    main()