# type: ignore

from behave import *
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver import ActionChains
import time
import json
import requests

use_step_matcher("re")


@given("the parking lot image is displayed in the zone editor")
def step_impl(context):
    # Upload a test image first to get to the zone editor
    context.execute_steps('''
        Given the user is on the image upload page
        When the user selects a valid JPEG image file
        And the user clicks the upload button
    ''')

    # Wait for the zone editor to load
    WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.ID, "image-container"))
    )

    # Store the lot_id from the URL for later API calls
    url_parts = context.driver.current_url.split('/')
    context.lot_id = url_parts[-2]  # Extract lot_id from the URL


@when('the user selects "Add Parking Slot" option')
def step_impl(context):
    add_button = WebDriverWait(context.driver, 10).until(
        EC.element_to_be_clickable((By.ID, "add-zone-btn"))
    )
    add_button.click()

    # Verify drawing mode is activated
    WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, "#image-container.drawing-active"))
    )


@step("the user draws a polygon on the image")
def step_impl(context):
    # Get the image container and image elements
    img_container = context.driver.find_element(By.ID, "image-container")
    img = context.driver.find_element(By.ID, "parking-image")

    # Wait for drawing mode to be fully active
    time.sleep(1)

    # Use JavaScript to trigger clicks directly at specific coordinates
    # This is more reliable than ActionChains for this specific interaction
    # Using updated coordinates with more central positioning to ensure room for editing
    points = [
        (200, 150),  # Top-left - moved more to center
        (400, 150),  # Top-right - moved more to center
        (400, 350),  # Bottom-right - moved more to center
        (200, 350)   # Bottom-left - moved more to center
    ]

    for x, y in points:
        # Use JavaScript to directly create click events at specific coordinates
        script = f"""
        var evt = new MouseEvent('click', {{
            bubbles: true,
            cancelable: true,
            view: window,
            clientX: {x + img.location['x']},
            clientY: {y + img.location['y']}
        }});
        document.getElementById('image-container').dispatchEvent(evt);
        """
        context.driver.execute_script(script)
        time.sleep(0.5)  # Give time for the click to register

    # Store the points for later verification
    context.polygon_points = points

    # Handle any alerts that might appear
    try:
        # Click finish drawing button
        finish_button = WebDriverWait(context.driver, 5).until(
            EC.element_to_be_clickable((By.ID, "finish-draw-btn"))
        )
        finish_button.click()

        # Wait for the form to appear
        WebDriverWait(context.driver, 5).until(
            EC.visibility_of_element_located((By.ID, "zone-form"))
        )
    except:
        # If there's an alert, accept it and try again with more points
        try:
            alert = context.driver.switch_to.alert
            alert.accept()

            # Try drawing one more point to ensure we have enough
            x, y = 200, 200  # Center point
            script = f"""
            var evt = new MouseEvent('click', {{
                bubbles: true,
                cancelable: true,
                view: window,
                clientX: {x + img.location['x']},
                clientY: {y + img.location['y']}
            }});
            document.getElementById('image-container').dispatchEvent(evt);
            """
            context.driver.execute_script(script)
            time.sleep(0.5)

            # Try clicking finish again
            finish_button = WebDriverWait(context.driver, 5).until(
                EC.element_to_be_clickable((By.ID, "finish-draw-btn"))
            )
            finish_button.click()

            WebDriverWait(context.driver, 5).until(
                EC.visibility_of_element_located((By.ID, "zone-form"))
            )
        except Exception as e:
            # If still failing, raise a more descriptive error
            raise Exception(f"Failed to draw polygon: {str(e)}")


@step('the user assigns ID "A1" to the parking slot')
def step_impl(context):
    slot_id_input = context.driver.find_element(By.ID, "slot-id")
    slot_id_input.clear()
    slot_id_input.send_keys("A1")
    context.slot_id = "A1"


@step("the user saves the parking slot")
def step_impl(context):
    save_button = context.driver.find_element(By.ID, "save-zone-btn")
    save_button.click()

    # Wait for the save operation to complete (add button should reappear)
    WebDriverWait(context.driver, 10).until(
        EC.visibility_of_element_located((By.ID, "add-zone-btn"))
    )


@then('the system should save the polygon coordinates for slot "A1"')
def step_impl(context):
    # Make API call to get the slots
    response = requests.get(f"{context.base_url}/api/lot/{context.lot_id}/slots")
    assert response.status_code == 200, "Failed to retrieve slots from API"

    slots = response.json()
    a1_slot = next((slot for slot in slots if slot["id"] == "A1"), None)

    assert a1_slot is not None, "Slot A1 not found in the saved data"
    assert a1_slot["detection_zone"] is not None, "No detection zone found for slot A1"
    assert len(
        a1_slot["detection_zone"]["points"]) == 4, f"Expected 4 points, got {len(a1_slot['detection_zone']['points'])}"


@step('the system should display the zone with label "A1" on the image')
def step_impl(context):
    # Check that the polygon is displayed
    polygon = WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, ".zone-polygon[data-zone-id='A1']"))
    )
    assert polygon.is_displayed(), "Polygon for A1 is not displayed"

    # Check that the label is displayed
    label = context.driver.find_element(By.CSS_SELECTOR, ".zone-label[data-zone-id='A1']")
    assert label.is_displayed(), "Label for A1 is not displayed"
    assert label.text == "A1", f"Expected label text 'A1', got '{label.text}'"


@given(r'the parking lot has a parking slot with ID "A1"')
def step_impl(context):
    # Check if we're continuing from previous scenario
    if not hasattr(context, 'lot_id') or not context.lot_id:
        # Create a parking slot with ID A1
        context.execute_steps('''
            Given the parking lot image is displayed in the zone editor
            When the user selects "Add Parking Slot" option
            And the user draws a polygon on the image
            And the user assigns ID "A1" to the parking slot
            And the user saves the parking slot
        ''')
    else:
        # Check if A1 exists
        response = requests.get(f"{context.base_url}/api/lot/{context.lot_id}/slots")
        slots = response.json()
        if not any(slot["id"] == "A1" for slot in slots):
            context.execute_steps('''
                When the user selects "Add Parking Slot" option
                And the user draws a polygon on the image
                And the user assigns ID "A1" to the parking slot
                And the user saves the parking slot
            ''')

    # Wait for the zone to be visible
    WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, ".zone-polygon[data-zone-id='A1']"))
    )


@when('the user selects slot "A1" in the zone editor')
def step_impl(context):
    # Find and click the edit button for slot A1
    edit_button = WebDriverWait(context.driver, 10).until(
        EC.element_to_be_clickable((By.CSS_SELECTOR, "button.edit-zone[data-slot-id='A1']"))
    )
    edit_button.click()

    # Wait for edit mode to be active
    WebDriverWait(context.driver, 10).until(
        EC.visibility_of_element_located((By.ID, "edit-zone-controls"))
    )

    # Store original zone coordinates before modification
    response = requests.get(f"{context.base_url}/api/lot/{context.lot_id}/slots")
    slots = response.json()
    a1_slot = next((slot for slot in slots if slot["id"] == "A1"), None)
    context.original_points = a1_slot["detection_zone"]["points"]


@step("the user modifies the polygon shape")
def step_impl(context):
    # Find a vertex point and drag it to modify the shape
    vertex = WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, ".vertex-point[data-vertex-type='edit-handle']"))
    )
    
    # Store the initial position for verification
    initial_location = vertex.location
    context.vertex_modified = {
        'initial': initial_location,
        'offset': {'x': 30, 'y': 30}  # Use smaller offset to prevent going out of bounds
    }

    actions = ActionChains(context.driver)
    # Drag the vertex 30px to the right and 30px down (reduced from 50px)
    actions.drag_and_drop_by_offset(vertex, 30, 30).perform()
    
    # Add a small wait to ensure the drag completed
    time.sleep(0.5)
    
    # Store the fact that we moved a point 30px right and down
    context.point_moved = True


@step("the user saves the changes")
def step_impl(context):
    update_button = WebDriverWait(context.driver, 10).until(
        EC.element_to_be_clickable((By.ID, "update-zone-btn"))
    )
    update_button.click()

    # Wait for edit mode to be deactivated
    WebDriverWait(context.driver, 10).until(
        EC.invisibility_of_element_located((By.ID, "edit-zone-controls"))
    )
    time.sleep(1)  # Additional wait for AJAX completion


@then('the system should update the polygon coordinates for slot "A1"')
def step_impl(context):
    # Get the updated coordinates
    response = requests.get(f"{context.base_url}/api/lot/{context.lot_id}/slots")
    slots = response.json()
    a1_slot = next((slot for slot in slots if slot["id"] == "A1"), None)
    updated_points = a1_slot["detection_zone"]["points"]

    # Print debug information
    print(f"Original points: {context.original_points}")
    print(f"Updated points: {updated_points}")
    
    if hasattr(context, 'vertex_modified'):
        print(f"Vertex modified: {context.vertex_modified}")
    
    # Verify that at least one point has changed
    def points_different(p1, p2):
        diff_x = abs(p1["x"] - p2["x"])
        diff_y = abs(p1["y"] - p2["y"])
        is_different = diff_x > 1 or diff_y > 1
        print(f"Comparing points - Original: ({p1['x']}, {p1['y']}), Updated: ({p2['x']}, {p2['y']})")
        print(f"Difference: X={diff_x}, Y={diff_y}, Is Different: {is_different}")
        return is_different

    any_point_changed = any(
        points_different(original, updated)
        for original, updated in zip(context.original_points, updated_points)
    )

    assert any_point_changed, "No points appear to have changed after edit"


@step("the system should display the updated zone on the image")
def step_impl(context):
    # Add a small delay to ensure the DOM has been updated
    time.sleep(1)

    # Verify the polygon is still displayed
    polygon = WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, ".zone-polygon[data-zone-id='A1']"))
    )

    assert polygon.is_displayed(), "Updated polygon is not displayed"

    # Verify the polygon has been updated

    # Get the updated points from the API
    response = requests.get(f"{context.base_url}/api/lot/{context.lot_id}/slots")
    slots = response.json()
    a1_slot = next((slot for slot in slots if slot["id"] == "A1"), None)

    assert a1_slot, "Slot A1 does not exist"


@step("the user chooses to delete the slot")
def step_impl(context):
    # Find and click the delete button for slot A1
    delete_button = WebDriverWait(context.driver, 10).until(
        EC.element_to_be_clickable((By.CSS_SELECTOR, "button.delete-zone[data-slot-id='A1']"))
    )
    delete_button.click()

    # Wait for the confirmation modal
    WebDriverWait(context.driver, 10).until(
        EC.visibility_of_element_located((By.ID, "confirmation-modal"))
    )


@step("the user confirms the deletion")
def step_impl(context):
    confirm_button = WebDriverWait(context.driver, 10).until(
        EC.element_to_be_clickable((By.ID, "confirm-delete-btn"))
    )
    confirm_button.click()

    # Wait for the modal to close
    WebDriverWait(context.driver, 10).until(
        EC.invisibility_of_element_located((By.ID, "confirmation-modal"))
    )
    time.sleep(1)  # Additional wait for AJAX completion


@then('the system should remove slot "A1" from the parking lot')
def step_impl(context):
    # Get the updated list of slots
    response = requests.get(f"{context.base_url}/api/lot/{context.lot_id}/slots")
    slots = response.json()

    # Verify A1 is not in the list
    assert not any(slot["id"] == "A1" for slot in slots), "Slot A1 was not removed"


@step("the zone should no longer appear on the image")
def step_impl(context):
    # Verify the polygon is not displayed
    try:
        WebDriverWait(context.driver, 10).until(
            EC.invisibility_of_element_located((By.CSS_SELECTOR, ".zone-polygon[data-zone-id='A1']"))
        )
    except TimeoutException:
        pytest.fail("Polygon for slot A1 still appears on the image after deletion.")

    # Verify the label is not displayed
    try:
        WebDriverWait(context.driver, 10).until(
            EC.invisibility_of_element_located((By.CSS_SELECTOR, ".zone-label[data-zone-id='A1']"))
        )
    except TimeoutException:
        pytest.fail("Label for slot A1 still appears on the image after deletion.")

