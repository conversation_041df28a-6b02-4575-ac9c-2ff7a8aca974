.engine-settings-card {
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.engine-settings-header {
    background-color: #e9ecef;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
}

.engine-settings-body {
    padding: 1.5rem;
}

.slider-value-display {
    font-weight: bold;
    color: #0d6efd;
}

.slider-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #6c757d;
}

.vehicle-classes-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.vehicle-class-item {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 0.5rem;
    transition: all 0.2s;
}

.vehicle-class-item:hover {
    background-color: #f1f3f5;
}

.form-actions {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
}

.config-section {
    margin-bottom: 2rem;
}

.switch-label {
    font-weight: 500;
}

.form-range::-webkit-slider-thumb {
    background: #0d6efd;
}

.form-range::-moz-range-thumb {
    background: #0d6efd;
}

.resolution-control-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.resolution-control-container .form-range {
    flex-grow: 1;
}

.resolution-control-container .btn {
    flex-shrink: 0;
}
