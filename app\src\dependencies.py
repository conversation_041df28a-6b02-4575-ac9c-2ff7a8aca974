"""
FastAPI dependency injection utilities
Provides common dependencies for routers
"""

from fastapi import Request, Depends
from fastapi.templating import <PERSON><PERSON><PERSON>Templates
from typing import Dict, Any

from src.config.settings import get_settings

# Import socketio with proper handling
try:
    import socketio
    SocketIOType = socketio.AsyncServer
except ImportError:
    SocketIOType = Any

def get_templates(request: Request) -> Jinja2Templates:
    """Get Jinja2 templates instance from app state (backward compatibility)"""
    return request.app.state.templates

def get_config(request: Request) -> Dict[str, Any]:
    """Get application configuration from settings (backward compatibility)"""
    return get_settings().get_config_dict()

def get_socketio(request: Request) -> SocketIOType:
    """Get SocketIO instance from settings"""
    return get_settings().socketio

def get_upload_folder(request: Request) -> str:
    """Get upload folder path from settings"""
    return get_settings().get_upload_folder_str()

def get_model_path(request: Request) -> str:
    """Get model path from settings"""
    return get_settings().get_model_path()

# New dependency functions that don't require Request
def get_settings_dependency() -> Dict[str, Any]:
    """Get settings as dependency without requiring Request"""
    return get_settings().get_config_dict()

def get_socketio_dependency() -> SocketIOType:
    """Get SocketIO instance as dependency without requiring Request"""
    return get_settings().socketio

# Common template context
def get_base_template_context(request: Request) -> Dict[str, Any]:
    """Get base template context with common variables"""
    return {
        "request": request,
        # Add any global template variables here
    }
