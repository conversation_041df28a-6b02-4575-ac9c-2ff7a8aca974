import logging
import sys
from pathlib import Path

# Ensure log directory exists
# Log directory should be one level up from the currents app working dir (as it is in 'app')
log_dir = Path(__file__).resolve().parent.parent.parent / 'logs'
log_dir.mkdir(parents=True, exist_ok=True)

# Configure root logger with INFO level to reduce verbosity
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        # logging.FileHandler(log_dir / 'app.log'),
        logging.StreamHandler(sys.stdout)
    ],
    force=True  # Override any existing configuration
)

def setup_logger(name) -> logging.Logger:
    """Setup logger with INFO level to reduce verbosity"""
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)
    socketio_server_logger = logging.getLogger('socketio.server')
    socketio_server_logger.setLevel(logging.WARNING)

    # Ensure the logger propagates to root logger
    logger.propagate = True

    # Only log initialization at debug level if explicitly enabled
    if logging.getLogger().isEnabledFor(logging.DEBUG):
        logger.debug(f"Logger '{name}' initialized with INFO level")

    return logger