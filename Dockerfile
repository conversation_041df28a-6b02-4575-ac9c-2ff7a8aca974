FROM ultralytics/ultralytics:8.3.82-cpu

WORKDIR /app

# Install system dependencies, Microsoft ODBC Driver, and related tools in one step
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    gnupg \
    apt-transport-https \
    lsb-release \
    # Download Microsoft GPG key using the recommended method
    && curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor -o /usr/share/keyrings/microsoft-prod.gpg \
    # Create the Microsoft repository source list file directly
    && echo "deb [arch=amd64 signed-by=/usr/share/keyrings/microsoft-prod.gpg] https://packages.microsoft.com/debian/11/prod bullseye main" > /etc/apt/sources.list.d/mssql-release.list \
    # Update package list again and install ODBC driver and related tools
    && apt-get update \
    && ACCEPT_EULA=Y apt-get install -y --no-install-recommends \
        msodbcsql18 \
        unixodbc \
        unixodbc-dev \
        libgl1-mesa-glx \
        libglib2.0-0 \
    # Clean up apt lists to reduce image size
    && rm -rf /var/lib/apt/lists/*

# Install python dependencies
COPY requirements.txt .
COPY app/ app/

# Ensure data directory exists for potential SQLite usage
RUN mkdir -p /app/data && chmod -R 777 /app/data && \
    mkdir -p /opt/models && chmod -R 777 /opt/models
COPY model.pt /opt/models/model.pt
# Set environment variables
ENV MODEL_PATH=yolo12x.pt

# Set working directory to app
WORKDIR /app/app

# Run the application using Uvicorn - production ASGI server for FastAPI
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]