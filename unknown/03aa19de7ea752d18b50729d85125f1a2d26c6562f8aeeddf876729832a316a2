import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any, Type

from src.models.events import (
    BaseEvent, ParkingLotCreated, ParkingLotDeleted, ParkingLotRenamed,
    ParkingLotImageUpdated, ParkingSlotAdded, Parking<PERSON>lotRemoved,
    Parking<PERSON><PERSON><PERSON>oneUpdated, Parking<PERSON>lotThresholdUpdated,
    ParkingSlotOccupancyChanged, DetectionConfigCreated,
    DetectionConfigUpdated, DetectionConfigDeleted,
    ViewpointAdded, ViewpointRemoved, ViewpointUpdated,
    ViewpointStreamConfigured, ViewpointStreamActivated, ViewpointStreamDeactivated,
    ViewpointStreamStatusChanged,
    ViewpointZoneAdded, ViewpointZoneUpdated, ViewpointZoneRemoved,
    DefaultViewpointSet
)
from src.models.orm import ParkingEventORM
from src.models.parking import ParkingLot, ParkingSlot
from src.models.geometry import Polygon, Point
from src.models.detection import DetectionEngineConfig
from src.utils.event_store_interface import EventStore
from src.utils.mapper import Mapper
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

class EventSourcedStorage:
    """
    High-level storage facade implementing event sourcing principles.
    Uses an EventStore for persistence and handles reconstruction of domain objects.
    """
    
    def __init__(self, event_store: EventStore):
        """
        Initialize the storage with an event store implementation.
        
        Args:
            event_store: An implementation of the EventStore interface.
        """
        self._event_store = event_store
        logger.info(f"EventSourcedStorage initialized with event store: {event_store.__class__.__name__}")
    
    # --- Domain Event Creation and Persistence ---
    
    def _create_and_persist_event(self, event_class: Type[BaseEvent], lot_id: str, payload: Dict[str, Any]) -> bool:
        """
        Create an event of the specified type and persist it through the event store.
        
        Args:
            event_class: The BaseEvent subclass to instantiate
            lot_id: The ID of the parking lot aggregate
            payload: Event-specific data to include in the payload
            
        Returns:
            bool: True if event was successfully persisted, False otherwise
        """
        try:
            # Create the domain event instance
            event = event_class(lot_id=lot_id, payload=payload)
            
            # Convert to ORM for persistence
            event_orm = ParkingEventORM(
                lot_id=event.lot_id,
                event_type=event.event_type,
                payload=event.payload,
                timestamp=event.timestamp,
                # sequence_id will be set by event_store
            )
            
            # Persist via event store
            success = self._event_store.append_event(event_orm)
            if success:
                logger.debug(f"Successfully persisted {event_class.__name__} for lot {lot_id}")
                # TODO: Emit real-time notification if needed
                # from services.event_service import emit_parking_event
                # emit_parking_event(lot_id, event)
            else:
                logger.error(f"Failed to persist {event_class.__name__} for lot {lot_id}")
            
            return success
        except Exception as e:
            logger.error(f"Error creating/persisting {event_class.__name__} for lot {lot_id}: {e}", exc_info=True)
            return False
    
    # --- State Reconstruction ---
    
    def _get_events_for_lot(self, lot_id: str) -> List[BaseEvent]:
        """
        Retrieve all events for a lot and convert them to domain event objects.
        
        Args:
            lot_id: The ID of the parking lot aggregate
            
        Returns:
            List[BaseEvent]: Ordered list of domain events
        """
        # Get raw ORM events from the event store
        event_orms = self._event_store.get_raw_events_for_aggregate(lot_id)
        
        # Convert ORMs to domain events
        domain_events = []
        for event_orm in event_orms:
            try:
                domain_event = Mapper.orm_to_dataclass_event(event_orm)
                domain_events.append(domain_event)
            except Exception as e:
                logger.error(f"Failed to convert event ORM to domain event (type: {event_orm.event_type}): {e}", exc_info=True)
        
        return domain_events
    
    def get_parking_lot(self, lot_id: str) -> Optional[ParkingLot]:
        """
        Reconstruct a parking lot's current state by replaying its events.
        
        Args:
            lot_id: The ID of the parking lot to reconstruct
            
        Returns:
            Optional[ParkingLot]: The reconstructed parking lot, or None if it doesn't exist or was deleted
        """
        logger.debug(f"Reconstructing parking lot state for id={lot_id}")
        events = self._get_events_for_lot(lot_id)
        
        if not events:
            logger.info(f"No events found for parking lot: {lot_id}. Assuming it doesn't exist.")
            return None
        
        # The first event should be ParkingLotCreated 
        if not isinstance(events[0], ParkingLotCreated):
            logger.error(f"First event for lot {lot_id} is not ParkingLotCreated. Cannot reconstruct state.")
            return None
        
        # Initialize the aggregate state from the creation event
        creation_event = events[0]
        lot_state = ParkingLot(
            id=creation_event.lot_id,
            name=creation_event.payload.get('name', 'Unnamed Lot'),
            image_path=creation_event.payload.get('image_path'),
            slots=[],
            detection_config=None,
            events=[]  # Not storing events in the aggregate anymore
        )
        
        # Apply subsequent events
        for event in events[1:]:
            if isinstance(event, ParkingLotDeleted):
                logger.info(f"Parking lot {lot_id} was deleted at sequence {event.sequence_id}. Reconstruction stopped.")
                return None  # Lot is considered deleted
            
            try:
                # Use the ParkingLot's apply method to update its state
                lot_state.apply(event)
                logger.debug(f"Applied event {event.event_type} (Seq: {event.sequence_id}) to lot {lot_id}")
            except Exception as e:
                logger.error(f"Error applying event {event.event_type} to lot {lot_id}: {e}", exc_info=True)
                # Continue with other events or return None based on requirements
        
        return lot_state
    
    def get_all_parking_lots(self) -> List[ParkingLot]:
        """
        Reconstruct all non-deleted parking lots.
        
        Returns:
            List[ParkingLot]: All existing parking lots
        """
        logger.debug("Getting all non-deleted parking lots")
        lot_ids = self._event_store.get_all_aggregate_ids()
        logger.info(f"Found {len(lot_ids)} potential parking lots to reconstruct")
        
        all_lots = []
        for lot_id in lot_ids:
            lot = self.get_parking_lot(lot_id)
            if lot:
                all_lots.append(lot)
        
        logger.info(f"Successfully reconstructed {len(all_lots)} non-deleted parking lots")
        return all_lots
    
    # --- Command Methods ---
    
    def create_parking_lot(self, name: str, image_path: Optional[str] = None) -> Optional[str]:
        """
        Create a new parking lot by emitting a ParkingLotCreated event.
        
        Args:
            name: The name of the new parking lot
            image_path: Optional path to an image for the lot
            
        Returns:
            Optional[str]: The ID of the created lot, or None if creation failed
        """
        lot_id = str(uuid.uuid4())  # Generate a new UUID
        logger.info(f"Creating new parking lot with ID {lot_id}")
        
        success = self._create_and_persist_event(
            ParkingLotCreated,
            lot_id=lot_id,
            payload={
                'name': name,
                'image_path': image_path
            }
        )
        
        return lot_id if success else None
    
    def create_parking_lot_with_id(self, lot_id, name: str, image_path: Optional[str] = None) -> Optional[str]:
        """
        Create a new parking lot by emitting a ParkingLotCreated event.
        
        Args:
            name: The name of the new parking lot
            image_path: Optional path to an image for the lot
            
        Returns:
            Optional[str]: The ID of the created lot, or None if creation failed
        """
        lot_id = str(lot_id)  # Ensure lot_id is a string
        if not lot_id:
            logger.error("Invalid lot_id provided for parking lot creation")
            return None
        logger.info(f"Creating new parking lot with ID {lot_id}")
        
        success = self._create_and_persist_event(
            ParkingLotCreated,
            lot_id=lot_id,
            payload={
                'name': name,
                'image_path': image_path
            }
        )
    
        return lot_id if success else None
    
    def rename_parking_lot(self, lot_id: str, new_name: str) -> bool:
        """
        Rename a parking lot by emitting a ParkingLotRenamed event.
        
        Args:
            lot_id: The ID of the lot to rename
            new_name: The new name for the lot
            
        Returns:
            bool: True if the rename was successful, False otherwise
        """
        return self._create_and_persist_event(
            ParkingLotRenamed,
            lot_id=lot_id,
            payload={'new_name': new_name}
        )
    
    def update_parking_lot_image(self, lot_id: str, new_image_path: Optional[str]) -> bool:
        """
        Update a parking lot's image by emitting a ParkingLotImageUpdated event.
        
        Args:
            lot_id: The ID of the lot to update
            new_image_path: The new image path, or None to clear it
            
        Returns:
            bool: True if the update was successful, False otherwise
        """
        return self._create_and_persist_event(
            ParkingLotImageUpdated,
            lot_id=lot_id,
            payload={'new_image_path': new_image_path}
        )
    
    def delete_parking_lot(self, lot_id: str) -> bool:
        """
        Delete a parking lot by emitting a ParkingLotDeleted event.
        
        Args:
            lot_id: The ID of the lot to delete
            
        Returns:
            bool: True if the deletion was successful, False otherwise
        """
        # Optionally check if lot exists before deletion
        lot = self.get_parking_lot(lot_id)
        if not lot:
            logger.warning(f"Cannot delete non-existent parking lot: {lot_id}")
            return False
        
        return self._create_and_persist_event(
            ParkingLotDeleted,
            lot_id=lot_id,
            payload={}
        )
    
    def add_parking_slot(self, lot_id: str, slot_name, zone_points: List[Dict[str, float]] = None,
                         overlap_threshold: float = 0.3, viewpoint_id: Optional[str] = None) -> Optional[str]:
        """
        Add a new parking slot to a lot by emitting a ParkingSlotAdded event.

        Args:
            lot_id: The ID of the lot to add the slot to
            slot_name: The name of the new slot
            zone_points: Optional list of detection zone points
            overlap_threshold: The overlap threshold for the new slot
            viewpoint_id: Optional ID of the viewpoint this slot belongs to

        Returns:
            Optional[str]: The ID of the created slot, or None if creation failed
        """
        # Check if lot exists
        lot = self.get_parking_lot(lot_id)
        if not lot:
            logger.warning(f"Cannot add slot to non-existent parking lot: {lot_id}")
            return None

        slot_id = str(uuid.uuid4())

        success = self._create_and_persist_event(
            ParkingSlotAdded,
            lot_id=lot_id,
            payload={
                'slot_id': slot_id,
                'slot_name': slot_name,
                'zone_points': zone_points or [],
                'overlap_threshold': overlap_threshold,
                'viewpoint_id': viewpoint_id
            }
        )

        return slot_id if success else None
    
    def update_slot_zone(self, lot_id: str, slot_id: str, zone_points: List[Dict[str, float]]) -> bool:
        """
        Update a parking slot's detection zone by emitting a ParkingSlotZoneUpdated event.
        
        Args:
            lot_id: The ID of the lot
            slot_id: The ID of the slot to update
            zone_points: The new detection zone points
            
        Returns:
            bool: True if the update was successful, False otherwise
        """
        # Check if slot exists
        lot = self.get_parking_lot(lot_id)
        if not lot:
            logger.warning(f"Cannot update slot in non-existent parking lot: {lot_id}")
            return False
        
        slot = next((s for s in lot.slots if s.id == slot_id), None)
        if not slot:
            logger.warning(f"Cannot update non-existent slot: {slot_id} in lot: {lot_id}")
            return False
        
        return self._create_and_persist_event(
            ParkingSlotZoneUpdated,
            lot_id=lot_id,
            payload={
                'slot_id': slot_id,
                'new_zone_points': zone_points
            }
        )
    
    def update_slot_threshold(self, lot_id: str, slot_id: str, new_threshold: float) -> bool:
        """
        Update a parking slot's overlap threshold by emitting a ParkingSlotThresholdUpdated event.
        
        Args:
            lot_id: The ID of the lot
            slot_id: The ID of the slot to update
            new_threshold: The new overlap threshold
            
        Returns:
            bool: True if the update was successful, False otherwise
        """
        # Validate threshold range
        if not (0.0 <= new_threshold <= 1.0):
            logger.warning(f"Invalid threshold value: {new_threshold}, must be between 0 and 1")
            return False
        
        # Validate slot exists
        lot = self.get_parking_lot(lot_id)
        if not lot or not any(s.id == slot_id for s in lot.slots):
            logger.warning(f"Cannot update threshold for non-existent slot: {slot_id} in lot: {lot_id}")
            return False
        
        return self._create_and_persist_event(
            ParkingSlotThresholdUpdated,
            lot_id=lot_id,
            payload={
                'slot_id': slot_id,
                'new_overlap_threshold': new_threshold
            }
        )
    
    def update_slot_occupancy(self, lot_id: str, slot_id: str, is_occupied: bool, 
                              detection_timestamp: Optional[datetime] = None) -> bool:
        """
        Update a parking slot's occupancy status by emitting a ParkingSlotOccupancyChanged event.
        
        Args:
            lot_id: The ID of the lot
            slot_id: The ID of the slot to update
            is_occupied: Whether the slot is occupied
            detection_timestamp: Optional timestamp of when the occupancy was detected
            
        Returns:
            bool: True if the update was successful, False otherwise
        """        # Validate slot exists
        lot = self.get_parking_lot(lot_id)
        if not lot or not any(s.id == slot_id for s in lot.slots):
            logger.warning(f"Cannot update occupancy for non-existent slot: {slot_id} in lot: {lot_id}")
            return False
        return self._create_and_persist_event(
            ParkingSlotOccupancyChanged,
            lot_id=lot_id,
            payload={
                'slot_id': slot_id,
                'is_occupied': is_occupied,
                'detection_timestamp': detection_timestamp.isoformat() if detection_timestamp else None
            }
        )
    
    def remove_parking_slot(self, lot_id: str, slot_id: str) -> bool:
        """
        Remove a parking slot by emitting a ParkingSlotRemoved event.
        
        Args:
            lot_id: The ID of the lot
            slot_id: The ID of the slot to remove
            
        Returns:
            bool: True if the removal was successful, False otherwise
        """        # Validate slot exists
        lot = self.get_parking_lot(lot_id)
        if not lot or not any(s.id == slot_id for s in lot.slots):
            logger.warning(f"Cannot remove non-existent slot: {slot_id} from lot: {lot_id}")
            return False
        return self._create_and_persist_event(
            ParkingSlotRemoved,
            lot_id=lot_id,
            payload={'slot_id': slot_id}
        )
    
    def create_detection_config(self, lot_id: str, engine_type: str, 
                               is_enabled: bool = True, update_interval: int = 30,
                               parameters: Dict[str, Any] = None) -> bool:
        """
        Create or update a detection configuration by emitting a DetectionConfigCreated/Updated event.
        
        Args:
            lot_id: The ID of the lot
            engine_type: The type of detection engine to use
            is_enabled: Whether detection is enabled
            update_interval: The update interval in seconds
            parameters: Additional parameters for the detection engine
            
        Returns:
            bool: True if the creation/update was successful, False otherwise
        """
        # Validate lot exists
        lot = self.get_parking_lot(lot_id)
        if not lot:
            logger.warning(f"Cannot create detection config for non-existent lot: {lot_id}")
            return False
        
        payload = {
            'engine_type': engine_type,
            'is_enabled': is_enabled,
            'update_interval': update_interval,
            'parameters': parameters or {}
        }
        
        # Determine if this is a create or update
        if lot.detection_config is None:
            event_class = DetectionConfigCreated
        else:
            event_class = DetectionConfigUpdated
        
        return self._create_and_persist_event(
            event_class,
            lot_id=lot_id,
            payload=payload
        )
    
    def delete_detection_config(self, lot_id: str) -> bool:
        """
        Delete a detection configuration by emitting a DetectionConfigDeleted event.
        
        Args:
            lot_id: The ID of the lot
            
        Returns:
            bool: True if the deletion was successful, False otherwise
        """
        # Validate lot and config exist
        lot = self.get_parking_lot(lot_id)
        if not lot:
            logger.warning(f"Cannot delete detection config for non-existent lot: {lot_id}")
            return False
        
        if lot.detection_config is None:
            logger.warning(f"No detection config exists for lot: {lot_id}")
            return False
        
        return self._create_and_persist_event(
            DetectionConfigDeleted,
            lot_id=lot_id,
            payload={}
        )
    
    # --- Viewpoint Management Commands ---
    
    def add_viewpoint(self, lot_id: str, name: str, image_path: str, 
                     camera_position: Optional[Dict[str, Any]] = None,
                     is_active: bool = True, sort_order: int = 0) -> Optional[str]:
        """
        Add a new viewpoint to a parking lot.
        
        Args:
            lot_id: The ID of the lot to add the viewpoint to
            name: The name of the viewpoint
            image_path: Path to the viewpoint image
            camera_position: Optional camera metadata
            is_active: Whether the viewpoint is active
            sort_order: Sort order for display
            
        Returns:
            Optional[str]: The ID of the created viewpoint, or None if creation failed
        """
        # Check if lot exists
        lot = self.get_parking_lot(lot_id)
        if not lot:
            logger.warning(f"Cannot add viewpoint to non-existent lot: {lot_id}")
            return None
        
        # Check for duplicate name
        for viewpoint in lot.viewpoints:
            if viewpoint.name == name:
                logger.warning(f"Viewpoint with name '{name}' already exists in lot {lot_id}")
                return None
        
        viewpoint_id = str(uuid.uuid4())
        logger.info(f"Adding viewpoint {viewpoint_id} to lot {lot_id}")
        
        success = self._create_and_persist_event(
            ViewpointAdded,
            lot_id=lot_id,
            payload={
                'viewpoint_id': viewpoint_id,
                'name': name,
                'image_path': image_path,
                'camera_position': camera_position,
                'is_active': is_active,
                'sort_order': sort_order
            }
        )
        
        return viewpoint_id if success else None
    
    def remove_viewpoint(self, lot_id: str, viewpoint_id: str) -> bool:
        """
        Remove a viewpoint from a parking lot.
        
        Args:
            lot_id: The ID of the lot
            viewpoint_id: The ID of the viewpoint to remove
            
        Returns:
            bool: True if removal was successful, False otherwise
        """
        # Validate lot and viewpoint exist
        lot = self.get_parking_lot(lot_id)
        if not lot:
            logger.warning(f"Cannot remove viewpoint from non-existent lot: {lot_id}")
            return False
        
        viewpoint = lot.get_viewpoint(viewpoint_id)
        if not viewpoint:
            logger.warning(f"Viewpoint {viewpoint_id} not found in lot {lot_id}")
            return False
        
        return self._create_and_persist_event(
            ViewpointRemoved,
            lot_id=lot_id,
            payload={'viewpoint_id': viewpoint_id}
        )
    
    def update_viewpoint(self, lot_id: str, viewpoint_id: str, **updates) -> bool:
        """
        Update viewpoint properties.
        
        Args:
            lot_id: The ID of the lot
            viewpoint_id: The ID of the viewpoint to update
            **updates: Properties to update (name, image_path, camera_position, is_active, sort_order)
            
        Returns:
            bool: True if update was successful, False otherwise
        """
        # Validate lot and viewpoint exist
        lot = self.get_parking_lot(lot_id)
        if not lot:
            logger.warning(f"Cannot update viewpoint in non-existent lot: {lot_id}")
            return False
        
        viewpoint = lot.get_viewpoint(viewpoint_id)
        if not viewpoint:
            logger.warning(f"Viewpoint {viewpoint_id} not found in lot {lot_id}")
            return False
        
        # Handle different types of updates with separate events
        success = True

        # Handle basic viewpoint updates
        basic_fields = {'name', 'image_path', 'camera_position', 'is_active', 'sort_order'}
        basic_payload = {'viewpoint_id': viewpoint_id}
        for key, value in updates.items():
            if key in basic_fields:
                basic_payload[key] = value

        if len(basic_payload) > 1:  # Has updates beyond viewpoint_id
            success &= self._create_and_persist_event(
                ViewpointUpdated,
                lot_id=lot_id,
                payload=basic_payload
            )

        # Handle stream configuration
        if 'stream_url' in updates and 'capture_fps' in updates:
            from src.models.events import ViewpointStreamConfigured
            success &= self._create_and_persist_event(
                ViewpointStreamConfigured,
                lot_id=lot_id,
                payload={
                    'viewpoint_id': viewpoint_id,
                    'stream_url': updates['stream_url'],
                    'capture_fps': updates['capture_fps']
                }
            )

        # Handle stream activation/deactivation
        if 'is_stream_active' in updates:
            from src.models.events import ViewpointStreamActivated, ViewpointStreamDeactivated
            event_class = ViewpointStreamActivated if updates['is_stream_active'] else ViewpointStreamDeactivated
            success &= self._create_and_persist_event(
                event_class,
                lot_id=lot_id,
                payload={'viewpoint_id': viewpoint_id}
            )

        if not any(key in updates for key in basic_fields | {'stream_url', 'capture_fps', 'is_stream_active'}):
            logger.warning(f"No valid updates provided for viewpoint {viewpoint_id}")
            return False

        return success

    def configure_viewpoint_stream(self, lot_id: str, viewpoint_id: str,
                                  stream_url: str, capture_fps: float = 1.0) -> bool:
        """
        Configure stream settings for a viewpoint.

        Args:
            lot_id: The ID of the lot
            viewpoint_id: The ID of the viewpoint
            stream_url: The stream URL
            capture_fps: Frame capture rate

        Returns:
            bool: True if configuration was successful, False otherwise
        """
        # Validate lot and viewpoint exist
        lot = self.get_parking_lot(lot_id)
        if not lot:
            logger.warning(f"Cannot configure stream for non-existent lot: {lot_id}")
            return False

        viewpoint = lot.get_viewpoint(viewpoint_id)
        if not viewpoint:
            logger.warning(f"Viewpoint {viewpoint_id} not found in lot {lot_id}")
            return False

        return self._create_and_persist_event(
            ViewpointStreamConfigured,
            lot_id=lot_id,
            payload={
                'viewpoint_id': viewpoint_id,
                'stream_url': stream_url,
                'capture_fps': capture_fps
            }
        )

    def activate_viewpoint_stream(self, lot_id: str, viewpoint_id: str) -> bool:
        """
        Activate stream capture for a viewpoint.

        Args:
            lot_id: The ID of the lot
            viewpoint_id: The ID of the viewpoint

        Returns:
            bool: True if activation was successful, False otherwise
        """
        # Validate lot and viewpoint exist
        lot = self.get_parking_lot(lot_id)
        if not lot:
            logger.warning(f"Cannot activate stream for non-existent lot: {lot_id}")
            return False

        viewpoint = lot.get_viewpoint(viewpoint_id)
        if not viewpoint:
            logger.warning(f"Viewpoint {viewpoint_id} not found in lot {lot_id}")
            return False

        return self._create_and_persist_event(
            ViewpointStreamActivated,
            lot_id=lot_id,
            payload={'viewpoint_id': viewpoint_id}
        )

    def deactivate_viewpoint_stream(self, lot_id: str, viewpoint_id: str) -> bool:
        """
        Deactivate stream capture for a viewpoint.

        Args:
            lot_id: The ID of the lot
            viewpoint_id: The ID of the viewpoint

        Returns:
            bool: True if deactivation was successful, False otherwise
        """
        # Validate lot and viewpoint exist
        lot = self.get_parking_lot(lot_id)
        if not lot:
            logger.warning(f"Cannot deactivate stream for non-existent lot: {lot_id}")
            return False

        viewpoint = lot.get_viewpoint(viewpoint_id)
        if not viewpoint:
            logger.warning(f"Viewpoint {viewpoint_id} not found in lot {lot_id}")
            return False

        return self._create_and_persist_event(
            ViewpointStreamDeactivated,
            lot_id=lot_id,
            payload={'viewpoint_id': viewpoint_id}
        )


    def add_viewpoint_zone(self, lot_id: str, viewpoint_id: str, slot_id: str, 
                          zone_points: List[Dict[str, float]]) -> bool:
        """
        Add or update a detection zone for a slot in a specific viewpoint.
        
        Args:
            lot_id: The ID of the lot
            viewpoint_id: The ID of the viewpoint
            slot_id: The ID of the slot
            zone_points: List of polygon points defining the detection zone
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Validate lot, viewpoint, and slot exist
        lot = self.get_parking_lot(lot_id)
        if not lot:
            logger.warning(f"Cannot add zone to non-existent lot: {lot_id}")
            return False
        
        viewpoint = lot.get_viewpoint(viewpoint_id)
        if not viewpoint:
            logger.warning(f"Viewpoint {viewpoint_id} not found in lot {lot_id}")
            return False
        
        slot = lot.get_slot(slot_id)
        if not slot:
            logger.warning(f"Slot {slot_id} not found in lot {lot_id}")
            return False
        
        # Determine if this is an add or update operation
        existing_zone = viewpoint.get_zone_for_slot(slot_id)
        event_class = ViewpointZoneUpdated if existing_zone else ViewpointZoneAdded
        
        return self._create_and_persist_event(
            event_class,
            lot_id=lot_id,
            payload={
                'viewpoint_id': viewpoint_id,
                'slot_id': slot_id,
                'zone_points': zone_points
            }
        )
    
    def remove_viewpoint_zone(self, lot_id: str, viewpoint_id: str, slot_id: str) -> bool:
        """
        Remove a detection zone for a slot from a specific viewpoint.
        
        Args:
            lot_id: The ID of the lot
            viewpoint_id: The ID of the viewpoint
            slot_id: The ID of the slot
            
        Returns:
            bool: True if removal was successful, False otherwise
        """
        # Validate lot, viewpoint, and slot exist
        lot = self.get_parking_lot(lot_id)
        if not lot:
            logger.warning(f"Cannot remove zone from non-existent lot: {lot_id}")
            return False
        
        viewpoint = lot.get_viewpoint(viewpoint_id)
        if not viewpoint:
            logger.warning(f"Viewpoint {viewpoint_id} not found in lot {lot_id}")
            return False
        
        existing_zone = viewpoint.get_zone_for_slot(slot_id)
        if not existing_zone:
            logger.warning(f"No zone found for slot {slot_id} in viewpoint {viewpoint_id}")
            return False
        
        return self._create_and_persist_event(
            ViewpointZoneRemoved,
            lot_id=lot_id,
            payload={
                'viewpoint_id': viewpoint_id,
                'slot_id': slot_id
            }
        )
    
    def set_default_viewpoint(self, lot_id: str, viewpoint_id: str) -> bool:
        """
        Set the default viewpoint for a parking lot.
        
        Args:
            lot_id: The ID of the lot
            viewpoint_id: The ID of the viewpoint to set as default
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Validate lot and viewpoint exist
        lot = self.get_parking_lot(lot_id)
        if not lot:
            logger.warning(f"Cannot set default viewpoint for non-existent lot: {lot_id}")
            return False
        
        viewpoint = lot.get_viewpoint(viewpoint_id)
        if not viewpoint:
            logger.warning(f"Viewpoint {viewpoint_id} not found in lot {lot_id}")
            return False
        
        return self._create_and_persist_event(
            DefaultViewpointSet,
            lot_id=lot_id,
            payload={'viewpoint_id': viewpoint_id}
        )

    # --- Deprecated Methods ---
    
    def update_parking_lot(self, lot_id: str, lot: ParkingLot) -> bool:
        """
        DEPRECATED: Direct state updates are incompatible with event sourcing.
        """
        logger.error("update_parking_lot is incompatible with event sourcing. Use specific commands instead.")
        raise NotImplementedError("Use specific command methods (e.g., rename_parking_lot) instead.")