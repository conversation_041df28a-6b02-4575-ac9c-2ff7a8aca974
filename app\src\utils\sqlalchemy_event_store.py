import os
import threading
from contextlib import contextmanager
from typing import List, Optional

from sqlalchemy import create_engine, func
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import IntegrityError # Import IntegrityError

# Import all ORM models to ensure they are registered with Base metadata
from src.models.orm import Base, ParkingEventORM, ParkingLotORM, ParkingSlotORM, DetectionZoneORM, PolygonPointORM, DetectionEngineConfigORM
from src.models.events import BaseEvent # Keep for type hinting if needed elsewhere
from src.utils.event_store_interface import EventStore
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

class SQLAlchemyEventStore(EventStore):
    """SQLAlchemy implementation of the EventStore interface."""
    _instance = None
    _engine = None
    _SessionLocal = None
    _lock = threading.Lock()

    def __new__(cls, db_url: Optional[str] = None):
        # Standard singleton pattern
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(SQLAlchemyEventStore, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self, db_url: Optional[str] = None):
        # Standard singleton initialization
        if self._initialized:
            return
        with self._lock:
            if self._initialized:
                return
            if db_url is None:
                db_url = self._get_default_db_url()

            logger.info(f"Initializing SQLAlchemyEventStore with DB URL: {db_url}")
            # Consider adding connect_args={'check_same_thread': False} for SQLite if needed
            self._engine = create_engine(db_url)
            self._SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self._engine)
            Base.metadata.create_all(self._engine) # Ensure tables are created - Base should now know about all imported models
            self._initialized = True

    def _get_default_db_url(self) -> str:
        """Gets the default SQLite database URL."""
        # Consistent path relative to this file's location
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../'))
        db_dir = os.path.join(project_root, 'data')
        os.makedirs(db_dir, exist_ok=True)
        db_path = os.path.join(db_dir, 'parking.db')
        return f"sqlite:///{db_path}"

    @classmethod
    def instance(cls, db_url: Optional[str] = None):
        """Returns the singleton instance of the store."""
        inst = cls(db_url)
        # Ensure __init__ runs if instance already existed but wasn't initialized
        if not inst._initialized:
             inst.__init__(db_url)
        return inst

    @classmethod
    def reset_instance_for_testing(cls):
        """Reset the singleton instance for testing purposes."""
        with cls._lock:
            if cls._instance:
                # Close any open connections and reset
                if cls._instance._engine:
                    cls._instance._engine.dispose()
                cls._instance._initialized = False
                cls._instance._engine = None
                cls._instance._SessionLocal = None
            cls._instance = None

    @contextmanager
    def _session_scope(self) -> Session:
        """Provide a transactional scope around a series of operations."""
        session = self._SessionLocal()
        try:
            yield session
            session.commit()
            logger.debug("Event Store: Session commit successful")
        except Exception as e:
            logger.error(f"Event Store: Session exception, rolling back. Error: {e}", exc_info=True)
            session.rollback()
            raise
        finally:
            session.close()
            logger.debug("Event Store: Session closed")

    def _get_next_sequence_id(self, session: Session, aggregate_id: str) -> int:
        """Gets the next sequence ID for a given aggregate_id within the session."""
        # Assuming aggregate_id corresponds to lot_id in ParkingEventORM
        max_sequence_id = session.query(func.max(ParkingEventORM.sequence_id)) \
            .filter(ParkingEventORM.lot_id == aggregate_id) \
            .scalar()
        return (max_sequence_id or 0) + 1

    def append_event(self, event_orm: ParkingEventORM) -> bool:
        """Appends a single event ORM object to the store."""
        logger.debug(f"Attempting to append event ORM for aggregate {event_orm.lot_id}: {event_orm.event_type}")
        try:
            # Store event info before session to avoid using detached instance later
            event_type = event_orm.event_type
            lot_id = event_orm.lot_id
            
            with self._session_scope() as session:
                # Assign sequence ID within the transaction
                sequence_id = self._get_next_sequence_id(session, lot_id)
                event_orm.sequence_id = sequence_id

                session.add(event_orm)
                logger.info(f"Event ORM staged for append: {event_type} (Seq: {sequence_id}) for aggregate {lot_id}")
            # Commit happens automatically via context manager
            logger.info(f"Event ORM successfully appended: {event_type} (Seq: {sequence_id}) for aggregate {lot_id}")
            return True
        except IntegrityError as e:
            # Specifically catch unique constraint violations (concurrency control)
            logger.warning(f"Failed to append event ORM due to IntegrityError (likely concurrency conflict) for aggregate {lot_id}: {e}")
            return False # Indicate concurrency failure
        except Exception as e:
            # Use the local variables if they exist, otherwise fall back to event_orm
            # which might cause another error but at least we logged the primary one
            log_lot_id = locals().get('lot_id', getattr(event_orm, 'lot_id', 'unknown'))
            logger.error(f"Failed to append event ORM for aggregate {log_lot_id}: {e}", exc_info=True)
            return False # Indicate general failure

    def get_raw_events_for_aggregate(self, aggregate_id: str) -> List[ParkingEventORM]:
        """Retrieves all raw event ORM objects for a given aggregate ID."""
        logger.debug(f"Fetching raw event ORMs for aggregate {aggregate_id}")
        try:
            with self._session_scope() as session:
                events_orm = session.query(ParkingEventORM) \
                    .filter_by(lot_id=aggregate_id) \
                    .order_by(ParkingEventORM.sequence_id.asc()) \
                    .all()
                
                # Create a new list of refreshed/detached instances to return outside the session
                detached_events = []
                for event in events_orm:
                    # Explicitly expunge the instance but keep it initialized
                    session.refresh(event)
                    # Make a copy of the event to ensure all attributes are loaded
                    detached_event = ParkingEventORM(
                        id=event.id,
                        lot_id=event.lot_id,
                        sequence_id=event.sequence_id,
                        event_type=event.event_type,
                        description=event.description,
                        payload=event.payload,
                        timestamp=event.timestamp
                    )
                    detached_events.append(detached_event)
                
                logger.info(f"Fetched {len(detached_events)} raw event ORMs for aggregate {aggregate_id}")
                return detached_events
        except Exception as e:
            logger.error(f"Failed to get raw event ORMs for aggregate {aggregate_id}: {e}", exc_info=True)
            return [] # Return empty list on error

    def get_all_aggregate_ids(self) -> List[str]:
        """Retrieves a list of all unique aggregate IDs."""
        logger.debug("Fetching all distinct aggregate IDs")
        try:
            with self._session_scope() as session:
                distinct_ids_result = session.query(ParkingEventORM.lot_id).distinct().all()
                aggregate_ids = [row[0] for row in distinct_ids_result]
                logger.info(f"Found {len(aggregate_ids)} distinct aggregate IDs.")
                return aggregate_ids
        except Exception as e:
            logger.error(f"Failed to fetch distinct aggregate IDs: {e}", exc_info=True)
            return []
