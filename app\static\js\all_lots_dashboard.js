document.addEventListener('DOMContentLoaded', function () {
    // Initialize Socket.IO connection
    const socket = io('/dashboard');
    window.socketConnection = socket;

    socket.on('connect', function() {
        console.log('All Lots Dashboard: Socket.IO connected successfully');
    });

    socket.on('connect_error', function(error) {
        console.error('All Lots Dashboard: Socket.IO connection error:', error);
    });

    // Handle image updates for stream viewpoints
    socket.on('image_updated', function(data) {
        console.log('All Lots Dashboard: Received image update:', data);
        
        // Find the lot image element
        const lotImage = document.querySelector(`img[data-lot-id="${data.lot_id}"]`);
        if (lotImage && data.viewpoint_id) {
            // This is a viewpoint-specific update, update the lot image
            const cacheBustedImageUrl = `/static/${data.image_path}?t=${new Date().getTime()}`;
            console.log(`All Lots Dashboard: Updating lot ${data.lot_id} image to: ${cacheBustedImageUrl}`);
            
            lotImage.src = cacheBustedImageUrl;
            lotImage.onload = () => {
                console.log('All Lots Dashboard: Image successfully updated for lot', data.lot_id);
                lotImage.onload = null;
            };
            lotImage.onerror = () => {
                console.error(`All Lots Dashboard: Error loading image for lot ${data.lot_id}: ${cacheBustedImageUrl}`);
                lotImage.onerror = null;
            };
        }
    });

    // Handle stream status updates
    const streamEvents = ['stream_started', 'stream_stopped', 'stream_error', 'stream_connecting'];
    
    streamEvents.forEach(eventType => {
        socket.on(eventType, function(data) {
            console.log(`All Lots Dashboard: ${eventType} for lot ${data.lot_id}, viewpoint ${data.viewpoint_id}`);
            updateStreamIndicator(data.lot_id, eventType);
        });
    });

    function updateStreamIndicator(lotId, eventType) {
        const lotCard = document.querySelector(`img[data-lot-id="${lotId}"]`)?.closest('.card');
        if (!lotCard) return;

        // Find or create stream indicator
        let streamIndicator = lotCard.querySelector('.stream-indicator');
        if (!streamIndicator) {
            streamIndicator = document.createElement('div');
            streamIndicator.className = 'stream-indicator';
            const imgContainer = lotCard.querySelector('.card-img-container');
            if (imgContainer) {
                imgContainer.appendChild(streamIndicator);
            }
        }

        // Update indicator based on stream status
        let badgeClass = 'bg-secondary';
        let badgeText = 'OFFLINE';

        switch (eventType) {
            case 'stream_started':
            case 'stream_active':
                badgeClass = 'bg-success';
                badgeText = 'LIVE';
                break;
            case 'stream_connecting':
                badgeClass = 'bg-warning';
                badgeText = 'CONNECTING';
                break;
            case 'stream_error':
                badgeClass = 'bg-danger';
                badgeText = 'ERROR';
                break;
            case 'stream_stopped':
                badgeClass = 'bg-secondary';
                badgeText = 'OFFLINE';
                break;
        }

        streamIndicator.innerHTML = `<span class="badge ${badgeClass}">${badgeText}</span>`;
    }

    // Handle slot status updates to refresh counters
    socket.on('slot_status_updated', function(data) {
        console.log('All Lots Dashboard: Received slot status update:', data);
        
        // Find the lot card and update counters
        const lotCard = document.querySelector(`img[data-lot-id="${data.lot_id}"]`)?.closest('.card');
        if (lotCard) {
            // Refresh the lot's statistics by making an API call
            refreshLotStats(data.lot_id, lotCard);
        }
    });

    function refreshLotStats(lotId, lotCard) {
        fetch(`/api/lots/${lotId}`)
            .then(response => response.json())
            .then(lot => {
                // Update free spots counter
                const freeSpan = lotCard.querySelector('.status-badge.free');
                if (freeSpan) {
                    freeSpan.textContent = lot.free_slot_count || 0;
                }

                // Update occupied spots counter
                const occupiedSpan = lotCard.querySelector('.status-badge.occupied');
                if (occupiedSpan) {
                    occupiedSpan.textContent = lot.busy_slot_count || 0;
                }

                // Update occupancy progress bar
                const progressBar = lotCard.querySelector('.progress-bar');
                const progressLabel = lotCard.querySelector('.progress-label');
                if (progressBar && progressLabel) {
                    const occupancyRate = lot.occupancy_rate || 0;
                    progressBar.style.width = `${occupancyRate}%`;
                    progressBar.setAttribute('aria-valuenow', occupancyRate);
                    progressLabel.textContent = `${occupancyRate.toFixed(1)}% Occupied`;
                }

                console.log(`All Lots Dashboard: Updated stats for lot ${lotId}`);
            })
            .catch(error => {
                console.error(`All Lots Dashboard: Error refreshing stats for lot ${lotId}:`, error);
            });
    }
});
