.card-img-container {
    height: 200px;
    width: 100%;
    overflow: hidden;
    position: relative;
    display: block;
    cursor: pointer;
}

.card-img-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, 
        rgba(255,255,255,0.1) 0%, 
        rgba(255,255,255,0) 100%
    );
    opacity: 0;
    transition: opacity 0.4s ease;
    pointer-events: none;
}

.card-img-container.hovered::after {
    opacity: 1;
}

.card-img-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.card-img-container.hovered img {
    transform: scale(1.02);
}

.fluent-hover-effect {
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    pointer-events: none;
    background: radial-gradient(circle, 
        rgba(255,255,255,0.12) 0%, 
        rgba(255,255,255,0.08) 35%, 
        rgba(255,255,255,0) 70%
    );
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.3s ease;
    will-change: transform, left, top;
    z-index: 2;
    mix-blend-mode: overlay;
}