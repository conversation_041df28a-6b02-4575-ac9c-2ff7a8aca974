Feature: Parking Lot Image Upload
  As a parking manager
  I want to upload an image of my parking lot
  So that I can configure detection zones

  Scenario: Upload valid parking lot image
    Given the user is on the image upload page
    When the user selects a valid JPEG image file
    And the user clicks the upload button
    Then the system should display the image in the zone editor
    And the system should save the image to storage

  Scenario: Attempt to upload unsupported file
    Given the user is on the image upload page
    When the user selects a file that is not an image
    And the user clicks the upload button
    Then the system should show an error message
    And the upload should be rejected

