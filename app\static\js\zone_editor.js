document.addEventListener('DOMContentLoaded', function () {
    // DOM Elements
    const imageContainer = document.getElementById('image-container');
    const parkingImage = document.getElementById('parking-image');
    const zonesList = document.getElementById('zones-list');
    const addZoneBtn = document.getElementById('add-zone-btn');
    const cancelDrawBtn = document.getElementById('cancel-draw-btn');
    const finishDrawBtn = document.getElementById('finish-draw-btn');
    const zoneForm = document.getElementById('zone-form');
    const slotIdInput = document.getElementById('slot-id');
    const saveZoneBtn = document.getElementById('save-zone-btn');
    const cancelZoneBtn = document.getElementById('cancel-zone-btn');
    const editZoneControls = document.getElementById('edit-zone-controls');
    const updateZoneBtn = document.getElementById('update-zone-btn');
    const cancelEditBtn = document.getElementById('cancel-edit-btn');
    const deleteZoneBtn = document.getElementById('delete-zone-btn');
    const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
    const confirmationModal = new bootstrap.Modal(document.getElementById('confirmation-modal'));
    const overlapThresholdInput = document.getElementById('overlap-threshold');
    const editOverlapThresholdInput = document.getElementById('edit-overlap-threshold');
    const overlapValueDisplay = document.getElementById('overlap-value');
    const editOverlapValueDisplay = document.getElementById('edit-overlap-value');

    // Viewpoint selector component will be accessed via getElementById when needed

    // Initialize global stream status tracking
    if (!window.streamStatuses) {
        window.streamStatuses = new Map();
    }

    // Update threshold displays when sliders change
    overlapThresholdInput.addEventListener('input', function() {
        overlapValueDisplay.textContent = `${Math.round(this.value * 100)}%`;
    });

    editOverlapThresholdInput.addEventListener('input', function() {
        editOverlapValueDisplay.textContent = `${Math.round(this.value * 100)}%`;
    });

    // Update range input colored track
    function updateRangeProgress(input) {
        const progress = (input.value - input.min) / (input.max - input.min) * 100;
        input.style.setProperty('--range-progress', `${progress}%`);
    }

    overlapThresholdInput.addEventListener('input', function() {
        const value = Math.round(this.value * 100);
        overlapValueDisplay.textContent = `${value}%`;
        updateRangeProgress(this);
    });

    editOverlapThresholdInput.addEventListener('input', function() {
        const value = Math.round(this.value * 100);
        editOverlapValueDisplay.textContent = `${value}%`;
        updateRangeProgress(this);
    });

    // Initialize range inputs
    updateRangeProgress(overlapThresholdInput);
    updateRangeProgress(editOverlapThresholdInput);

    // Application state
    const state = {
        mode: 'view', // 'view', 'draw', 'edit'
        currentPoints: [],
        zones: {}, // Zones for current viewpoint (for rendering)
        allZones: {}, // ALL zones from all viewpoints (for sidebar)
        activeZoneId: null,
        drawingHandler: null,
        zoomManager: null,
        currentViewpointId: (DEFAULT_VIEWPOINT_ID && DEFAULT_VIEWPOINT_ID !== "") ? DEFAULT_VIEWPOINT_ID : null,
        viewpoints: [],
        currentViewpointImage: null
    };

    // Initialize after image loads
    if (parkingImage.complete) {
        initialize();
        handleWindowResize(); // Set initial aspect ratio
    } else {
        parkingImage.onload = () => {
            initialize();
            handleWindowResize(); // Set initial aspect ratio
        };
    }

    function initialize() {
        state.drawingHandler = new PolygonDrawing(imageContainer, parkingImage);
        state.zoomManager = new ZoomManager(imageContainer, parkingImage);
        
        // Store zoom manager as a global instance for coordinate conversion
        window.zoomManagerInstance = state.zoomManager;
        
        setupEventListeners();
        setupViewpointSelectorListeners(); // Setup viewpoint selector event listeners
        setupSlotNameAutocomplete(); // Setup autocomplete for slot names
        loadViewpoints(); // Load viewpoints first, then zones will be loaded for the selected viewpoint
    }    function setupEventListeners() {
        // Drawing controls
        addZoneBtn.addEventListener('click', startDrawingMode);
        cancelDrawBtn.addEventListener('click', cancelDrawing);
        finishDrawBtn.addEventListener('click', finishDrawing);

        // Form controls
        saveZoneBtn.addEventListener('click', saveZone);
        cancelZoneBtn.addEventListener('click', cancelZoneForm);

        // Edit controls
        updateZoneBtn.addEventListener('click', () => saveUpdatedZone(state.activeZoneId));
        cancelEditBtn.addEventListener('click', cancelEditing);
        deleteZoneBtn.addEventListener('click', () => showDeleteConfirmation(state.activeZoneId));
        confirmDeleteBtn.addEventListener('click', deleteZone);

        // Image container click (for drawing)
        imageContainer.addEventListener('click', handleImageClick);

        // Add window resize event listener
        window.addEventListener('resize', handleWindowResize);

        // Event delegation for template-generated buttons with data-zone-id
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('edit-zone') || e.target.closest('.edit-zone')) {
                const button = e.target.classList.contains('edit-zone') ? e.target : e.target.closest('.edit-zone');
                const zoneId = button.getAttribute('data-zone-id');
                if (zoneId) {
                    e.preventDefault();
                    enterEditMode(zoneId);
                }
            }
            
            if (e.target.classList.contains('delete-zone') || e.target.closest('.delete-zone')) {
                const button = e.target.classList.contains('delete-zone') ? e.target : e.target.closest('.delete-zone');
                const zoneId = button.getAttribute('data-zone-id');
                if (zoneId) {
                    e.preventDefault();
                    showDeleteConfirmation(zoneId);
                }
            }
        });
    }

    // Handle window resize events
    function handleWindowResize() {
        // Set correct aspect ratio for the container
        if (parkingImage.naturalWidth && parkingImage.naturalHeight) {
            const aspectRatio = (parkingImage.naturalHeight / parkingImage.naturalWidth) * 100;
            imageContainer.style.paddingBottom = `${aspectRatio}%`;
        }

        // First redraw all existing zones
        if (Object.keys(state.zones).length > 0) {
            redrawAllZones();
        }

        // Update any active drawing or editing
        if (state.mode === 'draw' || state.mode === 'edit') {
            state.drawingHandler.imageRect = parkingImage.getBoundingClientRect();
            updateVertexPositions();
        }
        
        // Refresh zoom-related positioning
        if (state.zoomManager) {
            state.zoomManager.refreshZonePositions();
        }
    }

    function redrawAllZones() {
        // Save existing zones data
        const zonesCopy = {...state.zones};

        // Clear visual elements
        clearZoneVisuals();

        // Redraw each zone with current image dimensions
        Object.keys(zonesCopy).forEach(zoneId => {
            const zone = zonesCopy[zoneId];
            // Convert percentage points back to current pixels
            const pixelPoints = zone.percentagePoints.map(point => {
                return convertToPixels(point.x, point.y);
            });
            // Redraw the zone
            drawZoneToUI(zoneId, pixelPoints, zone.percentagePoints, zone.slot_name, zone.overlap_threshold);
        });
    }

    // Viewpoint Management Functions
    async function loadViewpoints() {
        try {
            const response = await fetch(`/api/lots/${LOT_ID}/viewpoints`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();
            state.viewpoints = data.viewpoints || [];

            console.log('Loaded viewpoints for zone editor:', state.viewpoints);

            // Initialize the viewpoint selector component
            const initializeSelector = () => {
                const selector = document.getElementById('viewpoint-selector');
                if (selector) {
                    console.log('Zone Editor: Initializing viewpoint selector with', state.viewpoints.length, 'viewpoints');
                    selector.viewpoints = state.viewpoints;

                    // Set initial viewpoint if not already set
                    if (!state.currentViewpointId && state.viewpoints.length > 0) {
                        const defaultViewpoint = state.viewpoints.find(v => v.is_default) || state.viewpoints[0];
                        selector.currentViewpoint = defaultViewpoint.id;
                        switchToViewpoint(defaultViewpoint.id);
                    } else if (state.currentViewpointId) {
                        // Load the current viewpoint
                        selector.currentViewpoint = state.currentViewpointId;
                        switchToViewpoint(state.currentViewpointId);
                    }
                } else {
                    console.warn('Zone Editor: Viewpoint selector not ready, retrying...');
                    setTimeout(initializeSelector, 100);
                }
            };

            // Wait for component to be ready
            if (customElements.get('viewpoint-selector')) {
                initializeSelector();
            } else {
                customElements.whenDefined('viewpoint-selector').then(initializeSelector);
            }
        } catch (error) {
            console.error('Failed to load viewpoints:', error);
        }
    }

    // Setup event listeners for viewpoint selector component
    function setupViewpointSelectorListeners() {
        // Wait for the component to be defined and ready
        const setupListener = () => {
            const selector = document.getElementById('viewpoint-selector');
            if (selector) {
                console.log('Zone Editor: Setting up viewpoint selector listeners');
                selector.addEventListener('viewpoint-changed', function(event) {
                    const { viewpointId, viewpoint } = event.detail;
                    console.log('Zone Editor: Viewpoint changed to:', viewpoint);

                    if (viewpointId && viewpointId !== state.currentViewpointId) {
                        switchToViewpoint(viewpointId);
                    }
                });
            } else {
                console.warn('Zone Editor: Viewpoint selector not found, retrying...');
                setTimeout(setupListener, 100);
            }
        };

        // Try to set up immediately, or wait for custom elements to be defined
        if (customElements.get('viewpoint-selector')) {
            setupListener();
        } else {
            customElements.whenDefined('viewpoint-selector').then(setupListener);
        }
    }

    async function switchToViewpoint(viewpointId) {
        const viewpoint = state.viewpoints.find(v => v.id === viewpointId);
        if (!viewpoint) {
            console.error('Viewpoint not found:', viewpointId);
            return;
        }

        console.log('Switching to viewpoint:', viewpoint.name);

        // Update state
        state.currentViewpointId = viewpointId;
        state.currentViewpointImage = viewpoint.image_path;

        // Update viewpoint selector component
        const selector = document.getElementById('viewpoint-selector');
        if (selector) {
            selector.currentViewpoint = viewpointId;
        }

        // Update the parking image with cache busting
        if (viewpoint.image_path) {
            const timestamp = new Date().getTime();
            const imageUrl = `/static/${viewpoint.image_path}?t=${timestamp}`;
            console.log('Zone Editor: Loading viewpoint image with cache buster:', imageUrl);
            parkingImage.src = imageUrl;

            // Wait for image to load before loading zones
            parkingImage.onload = () => {
                console.log('Viewpoint image loaded, loading zones...');
                loadZones(); // Reload zones for this viewpoint
            };
        } else {
            console.warn('Viewpoint has no image path');
            loadZones(); // Still try to load zones
        }

        // Clear current editing state
        enterViewMode();
    }

    // Slot Name Management Functions
    async function loadAllSlotNames() {
        try {
            const response = await fetch(`/api/lots/${LOT_ID}/slots`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const allSlots = await response.json();

            // Extract unique slot names
            const slotNames = [...new Set(allSlots.map(slot => slot.slot_name).filter(name => name && name.trim()))];

            console.log('Loaded slot names for autocomplete:', slotNames);
            return slotNames.sort();
        } catch (error) {
            console.error('Failed to load slot names:', error);
            return [];
        }
    }

    function setupSlotNameAutocomplete() {
        const slotIdInput = document.getElementById('slot-id');
        const slotMappingInfo = document.getElementById('slot-mapping-info');
        if (!slotIdInput) return;

        // Create datalist for autocomplete
        let datalist = document.getElementById('slot-names-datalist');
        if (!datalist) {
            datalist = document.createElement('datalist');
            datalist.id = 'slot-names-datalist';
            slotIdInput.parentNode.appendChild(datalist);
            slotIdInput.setAttribute('list', 'slot-names-datalist');
        }

        // Initial population of slot names
        refreshSlotNameAutocomplete();

        // Add event listener to show mapping information (only add once)
        if (!slotIdInput.hasAttribute('data-autocomplete-setup')) {
            slotIdInput.setAttribute('data-autocomplete-setup', 'true');
            slotIdInput.addEventListener('input', async (e) => {
                const slotName = e.target.value.trim();

                if (slotName.length > 0) {
                    const mappingInfo = await getSlotMappingInfo(slotName);
                    updateSlotMappingDisplay(mappingInfo, slotName);
                } else {
                    if (slotMappingInfo) {
                        slotMappingInfo.textContent = '';
                    }
                }
            });
        }
    }

    // Separate function to refresh autocomplete options
    function refreshSlotNameAutocomplete() {
        const datalist = document.getElementById('slot-names-datalist');
        if (!datalist) return;

        // Load and populate slot names
        loadAllSlotNames().then(slotNames => {
            console.log('Refreshing autocomplete with slot names:', slotNames);
            datalist.innerHTML = '';
            slotNames.forEach(name => {
                const option = document.createElement('option');
                option.value = name;
                option.textContent = name; // Add text content for better compatibility
                datalist.appendChild(option);
            });
            console.log(`Autocomplete updated with ${slotNames.length} slot names`);
        });
    }

    function updateSlotMappingDisplay(mappingInfo, slotName) {
        const slotMappingInfo = document.getElementById('slot-mapping-info');
        if (!slotMappingInfo) return;

        if (mappingInfo.totalSlots === 0) {
            slotMappingInfo.innerHTML = '<span class="text-success">✓ New slot name</span>';
        } else {
            const viewpointCount = mappingInfo.viewpointIds.length;
            const hasLegacy = mappingInfo.hasLegacySlots;

            let message = `<span class="text-info">📍 Exists in ${viewpointCount} viewpoint(s)`;
            if (hasLegacy) {
                message += ' + legacy zones';
            }
            message += '</span>';

            // Check if current viewpoint already has this slot
            const hasInCurrentViewpoint = mappingInfo.viewpointIds.includes(state.currentViewpointId);
            if (hasInCurrentViewpoint) {
                message += ' <span class="text-warning">⚠️ Already exists in the current viewpoint</span>';
            }

            slotMappingInfo.innerHTML = message;
        }
    }

    function getSlotMappingInfo(slotName) {
        // Get information about which viewpoints have zones for this slot name
        return fetch(`/api/lots/${LOT_ID}/slots`)
            .then(response => response.json())
            .then(allSlots => {
                const slotsWithName = allSlots.filter(slot => slot.slot_name === slotName);
                const viewpointIds = slotsWithName.map(slot => slot.viewpoint_id).filter(id => id);

                return {
                    totalSlots: slotsWithName.length,
                    viewpointIds: viewpointIds,
                    hasLegacySlots: slotsWithName.some(slot => !slot.viewpoint_id)
                };
            })
            .catch(error => {
                console.error('Error getting slot mapping info:', error);
                return { totalSlots: 0, viewpointIds: [], hasLegacySlots: false };
            });
    }

    // Coordinate conversion helpers - improved for zoom
    function convertToPercentage(x, y) {
        const width = parkingImage.offsetWidth;
        const height = parkingImage.offsetHeight;
        return {
            x: (x / width) * 100,
            y: (y / height) * 100
        };
    }

    function convertToPixels(x, y) {
        const width = parkingImage.offsetWidth;
        const height = parkingImage.offsetHeight;
        return {
            x: (x * width) / 100,
            y: (y * height) / 100
        };
    }

    // Convert display coordinates to natural image coordinates for API storage
    function convertDisplayToNaturalCoordinates(displayX, displayY) {
        if (!parkingImage.complete || !parkingImage.naturalWidth || !parkingImage.naturalHeight) {
            console.error('Image not loaded or natural dimensions not available');
            return { x: displayX, y: displayY };
        }

        // Get display dimensions
        const displayWidth = parkingImage.offsetWidth;
        const displayHeight = parkingImage.offsetHeight;

        // Get natural (original) dimensions
        const naturalWidth = parkingImage.naturalWidth;
        const naturalHeight = parkingImage.naturalHeight;

        // Calculate scaling factors
        const scaleX = naturalWidth / displayWidth;
        const scaleY = naturalHeight / displayHeight;

        // Convert to natural coordinates
        return {
            x: Math.round(displayX * scaleX),
            y: Math.round(displayY * scaleY)
        };
    }

    // Convert natural coordinates to display coordinates for UI rendering
    function convertNaturalToDisplayCoordinates(naturalX, naturalY) {
        if (!parkingImage.complete || !parkingImage.naturalWidth || !parkingImage.naturalHeight) {
            console.error('Image not loaded or natural dimensions not available');
            return { x: naturalX, y: naturalY };
        }

        // Get display dimensions
        const displayWidth = parkingImage.offsetWidth;
        const displayHeight = parkingImage.offsetHeight;

        // Get natural (original) dimensions
        const naturalWidth = parkingImage.naturalWidth;
        const naturalHeight = parkingImage.naturalHeight;

        // Calculate scaling factors
        const scaleX = displayWidth / naturalWidth;
        const scaleY = displayHeight / naturalHeight;

        // Convert to display coordinates
        return {
            x: naturalX * scaleX,
            y: naturalY * scaleY
        };
    }

    // State management functions
    function enterViewMode() {
        state.mode = 'view';
        state.currentPoints = [];
        state.activeZoneId = null;

        // Reset UI
        addZoneBtn.classList.remove('d-none');
        cancelDrawBtn.classList.add('d-none');
        finishDrawBtn.classList.add('d-none');
        zoneForm.classList.add('d-none');
        editZoneControls.classList.add('d-none');

        // Clear any active zones
        document.querySelectorAll('.zone-polygon.active').forEach(el => {
            el.classList.remove('active');
        });

        // Clear drawing elements
        state.drawingHandler.cancelDrawing();
        removeAllDrawingPoints();

        // Deselect list items
        document.querySelectorAll('#zones-list li.active').forEach(li => {
            li.classList.remove('active');
        });
    }

    function startDrawingMode() {
        enterViewMode();
        state.mode = 'draw';
        state.currentPoints = [];

        // Update UI
        addZoneBtn.classList.add('d-none');
        cancelDrawBtn.classList.remove('d-none');
        finishDrawBtn.classList.remove('d-none');
        imageContainer.classList.add('drawing-active');

        // Start drawing
        state.drawingHandler.startDrawing();
    }

    function enterEditMode(zoneId) {
        enterViewMode();

        state.mode = 'edit';
        state.activeZoneId = zoneId;

        // Initialize with existing zone points
        if (state.zones[zoneId]) {
            const zone = state.zones[zoneId];
            // Convert percentage points to current pixel coordinates for editing
            state.currentPoints = zone.percentagePoints.map(point => {
                const pixelPoint = convertToPixels(point.x, point.y);
                return {x: pixelPoint.x, y: pixelPoint.y};
            });

            // Hide the original polygon during editing
            if (zone.element) {
                zone.element.style.display = 'none';
            }

            // Initialize drawing with existing points
            state.drawingHandler.startDrawing();
            state.drawingHandler.currentPoints = [...state.currentPoints];
            state.drawingHandler.updateTemporaryPolygon();

            // Add draggable vertices to each point
            state.currentPoints.forEach((point, index) => {
                createDraggableVertex(point.x, point.y, index);
            });
        }

        // Update UI
        addZoneBtn.classList.add('d-none');
        editZoneControls.classList.remove('d-none');
        imageContainer.classList.add('drawing-active');

        // Highlight the zone
        const zoneElement = document.querySelector(`.zone-polygon[data-zone-id="${zoneId}"]`);
        if (zoneElement) {
            zoneElement.classList.add('active');
        }

        // Highlight list item
        const listItem = document.querySelector(`#zones-list li[data-slot-id="${zoneId}"]`);
        if (listItem) {
            listItem.classList.add('active');
        }

        // Set form values (slot name and overlap threshold)
        const zoneData = state.zones[zoneId];
        if (zoneData) {
            slotIdInput.value = zoneData.slot_name || zoneData.id; // Use slot_name, fallback to actual id
            
            editOverlapThresholdInput.value = zoneData.overlap_threshold || 0.3; // Default to 0.3 if not set
            editOverlapValueDisplay.textContent = `${Math.round(editOverlapThresholdInput.value * 100)}%`;
            updateRangeProgress(editOverlapThresholdInput); // Update slider progress bar
        } else {
            // Fallback if zoneData is not found in state.zones (e.g., error or inconsistency)
            slotIdInput.value = zoneId; // Original behavior was to show the ID passed to function
            
            // Set default overlap threshold
            editOverlapThresholdInput.value = 0.3; // Default
            editOverlapValueDisplay.textContent = `${Math.round(editOverlapThresholdInput.value * 100)}%`;
            updateRangeProgress(editOverlapThresholdInput); // Update slider progress bar
        }

        // Update UI for edit mode
        addZoneBtn.classList.add('d-none');
    }

    // Create draggable vertex with percentage-based positioning - improved for zoom
    function createDraggableVertex(x, y, index) {
        // Convert pixel position to percentage
        const percentX = (x / parkingImage.offsetWidth) * 100;
        const percentY = (y / parkingImage.offsetHeight) * 100;
        
        const vertex = document.createElement('div');
        vertex.className = 'vertex-point';
        vertex.style.left = `${percentX}%`;
        vertex.style.top = `${percentY}%`;
        vertex.dataset.index = index;
        vertex.dataset.vertexType = 'edit-handle';
        vertex.dataset.percentX = percentX;
        vertex.dataset.percentY = percentY;

        imageContainer.appendChild(vertex);

        // Make vertex draggable
        vertex.addEventListener('mousedown', startDragging);

        return vertex;
    }

    let vertexPopup = null;
    let isDragging = false;
    let dragStartTimeout = null;

    function createVertexPopup(x, y, vertexIndex) {
        removeVertexPopup();

        // Create popup
        vertexPopup = document.createElement('div');
        vertexPopup.className = 'vertex-popup';

        // Position popup relative to screen
        const imageContainer = document.getElementById('image-container');
        const containerRect = imageContainer.getBoundingClientRect();
        
        // Add delete option
        const deleteItem = document.createElement('div');
        deleteItem.className = 'vertex-popup-item delete';
        deleteItem.textContent = 'Delete Vertex';
        deleteItem.onclick = (e) => {
            e.stopPropagation();
            deleteVertex(vertexIndex);
            removeVertexPopup();
        };
        vertexPopup.appendChild(deleteItem);

        // Calculate popup position
        const popupX = x - containerRect.left;
        const popupY = y - containerRect.top;
        
        // Adjust position to prevent popup from going off-screen
        const maxX = containerRect.width - vertexPopup.offsetWidth;
        const maxY = containerRect.height - vertexPopup.offsetHeight;
        
        vertexPopup.style.left = `${Math.min(maxX, Math.max(0, popupX))}px`;
        vertexPopup.style.top = `${Math.min(maxY, Math.max(0, popupY))}px`;

        // Add to container
        imageContainer.appendChild(vertexPopup);
    }

    function removeVertexPopup() {
        if (vertexPopup) {
            vertexPopup.remove();
            vertexPopup = null;
        }
    }

    function deleteVertex(index) {
        if (state.currentPoints.length <= 3) {
            alert('A polygon must have at least 3 vertices.');
            return;
        }

        // Remove the vertex
        state.currentPoints.splice(index, 1);
        
        // Update the drawing
        state.drawingHandler.currentPoints = [...state.currentPoints];
        state.drawingHandler.updateTemporaryPolygon();
        
        // Recreate all vertices to update indices
        updateVertexPositions();
    }

    // Modify the startDragging function to handle drag detection
    function startDragging(e) {
        e.preventDefault();
        e.stopPropagation();
        const vertex = e.target;
        const index = parseInt(vertex.dataset.index);

        // Set up drag detection
        isDragging = false;
        dragStartTimeout = setTimeout(() => {
            isDragging = true;
            removeVertexPopup();
        }, 200);

        // Store initial drag position
        const initialX = e.clientX;
        const initialY = e.clientY;
        const initialUnzoomed = state.zoomManager.getUnzoomedCoordinates(initialX, initialY);
        const initialPoint = state.currentPoints[index];

        function handleDrag(moveEvent) {
            moveEvent.preventDefault();
            moveEvent.stopPropagation();
            
            clearTimeout(dragStartTimeout);
            isDragging = true;
            removeVertexPopup();

            // Calculate the change in position in screen coordinates
            const deltaX = moveEvent.clientX - initialX;
            const deltaY = moveEvent.clientY - initialY;

            // Apply the delta to the initial unzoomed point
            const newX = initialPoint.x + (deltaX / state.zoomManager.zoomLevel);
            const newY = initialPoint.y + (deltaY / state.zoomManager.zoomLevel);

            // Update vertex position using percentage coordinates
            const percentX = (newX / parkingImage.offsetWidth) * 100;
            const percentY = (newY / parkingImage.offsetHeight) * 100;

            vertex.style.left = `${percentX}%`;
            vertex.style.top = `${percentY}%`;
            vertex.dataset.percentX = percentX;
            vertex.dataset.percentY = percentY;

            // Update point in state
            state.currentPoints[index] = { x: newX, y: newY };

            // Force refresh of polygon visualization
            state.drawingHandler.currentPoints = [...state.currentPoints];
            state.drawingHandler.updateTemporaryPolygon();
        }

        function stopDragging(upEvent) {
            upEvent.preventDefault();
            upEvent.stopPropagation();
            
            clearTimeout(dragStartTimeout);
            if (!isDragging) {
                // If not dragging, it was a click - show popup
                const rect = vertex.getBoundingClientRect();
                createVertexPopup(rect.right + 5, rect.top, index);
            }
            isDragging = false;
            document.removeEventListener('mousemove', handleDrag);
            document.removeEventListener('mouseup', stopDragging);
        }

        document.addEventListener('mousemove', handleDrag);
        document.addEventListener('mouseup', stopDragging);
    }

    // Handler for image clicks during drawing - improved version
    function handleImageClick(e) {
        if (state.mode !== 'draw' && state.mode !== 'edit') return;
        
        // Don't add vertex if clicking on an existing vertex or popup
        if (e.target.classList.contains('vertex-point') || 
            e.target.classList.contains('vertex-popup') ||
            e.target.classList.contains('vertex-popup-item')) {
            return;
        }

        removeVertexPopup();

        // Get proper unzoomed coordinates using the zoom manager
        const point = state.zoomManager.getUnzoomedCoordinates(e.clientX, e.clientY);
        if (!point) return;

        if (state.mode === 'draw') {
            // Add point directly using the coordinates from zoom manager
            state.drawingHandler.addPoint(point.x, point.y);
            state.currentPoints = [...state.drawingHandler.currentPoints];
        } else if (state.mode === 'edit') {
            // Add new vertex when editing
            insertNewVertex(point);
        }
    }

    function insertNewVertex(point) {
        if (!state.currentPoints.length) return;

        let bestIndex = 0;
        let minDistance = Infinity;

        // Check each line segment
        for (let i = 0; i < state.currentPoints.length; i++) {
            const p1 = state.currentPoints[i];
            const p2 = state.currentPoints[(i + 1) % state.currentPoints.length];

            // Calculate distance from point to line segment
            const distance = distanceToLineSegment(point, p1, p2);
            if (distance < minDistance) {
                minDistance = distance;
                bestIndex = i + 1;
            }
        }

        // Insert the new point
        state.currentPoints.splice(bestIndex, 0, point);
        state.drawingHandler.currentPoints = [...state.currentPoints];
        state.drawingHandler.updateTemporaryPolygon();
        updateVertexPositions();
    }

    function distanceToLineSegment(point, lineStart, lineEnd) {
        const A = point.x - lineStart.x;
        const B = point.y - lineStart.y;
        const C = lineEnd.x - lineStart.x;
        const D = lineEnd.y - lineStart.y;

        const dot = A * C + B * D;
        const lenSq = C * C + D * D;
        let param = -1;

        if (lenSq !== 0) {
            param = dot / lenSq;
        }

        let xx, yy;

        if (param < 0) {
            xx = lineStart.x;
            yy = lineStart.y;
        } else if (param > 1) {
            xx = lineEnd.x;
            yy = lineEnd.y;
        } else {
            xx = lineStart.x + param * C;
            yy = lineStart.y + param * D;
        }

        const dx = point.x - xx;
        const dy = point.y - yy;

        return Math.sqrt(dx * dx + dy * dy);
    }

    // Add click handler to hide popup when clicking outside
    document.addEventListener('click', (e) => {
        if (!e.target.classList.contains('vertex-point') &&
            !e.target.classList.contains('vertex-popup') &&
            !e.target.classList.contains('vertex-popup-item')) {
            removeVertexPopup();
        }
    });

    // Add a method to handle zoom-adjusted points for drawing
    function addPointWithZoomAdjustment(clientX, clientY) {
        // Ensure we have fresh dimensions
        const imageRect = parkingImage.getBoundingClientRect();
        state.drawingHandler.imageRect = imageRect;
        
        // Get properly unzoomed coordinates
        const point = state.zoomManager.getUnzoomedCoordinates(clientX, clientY);
        
        if (point) {
            state.drawingHandler.addPoint(point.x, point.y);
            state.currentPoints = [...state.drawingHandler.currentPoints];
        }
    }

    // API interactions
    function loadZones() {
        if (!state.currentViewpointId) {
            console.warn('No current viewpoint selected, cannot load zones');
            renderZones([]); // Clear zones
            return;
        }

        console.log('Loading zones for viewpoint:', state.currentViewpointId);

        fetch(`/api/lots/${LOT_ID}/slots`)
            .then(response => response.json())
            .then(allZones => {
                // Store ALL zones for sidebar display
                state.allZones = {};
                allZones.forEach(zone => {
                    state.allZones[zone.id] = zone;
                });

                // Filter zones for the current viewpoint (for rendering)
                // Include zones that match the current viewpoint OR have no viewpoint_id (legacy zones)
                const viewpointZones = allZones.filter(zone =>
                    zone.viewpoint_id === state.currentViewpointId ||
                    !zone.viewpoint_id ||
                    zone.viewpoint_id === null ||
                    zone.viewpoint_id === undefined
                );

                console.log(`Loaded ${viewpointZones.length} zones for viewpoint ${state.currentViewpointId} (out of ${allZones.length} total)`);
                console.log('Zones breakdown:', {
                    withViewpointId: allZones.filter(z => z.viewpoint_id === state.currentViewpointId).length,
                    withoutViewpointId: allZones.filter(z => !z.viewpoint_id).length,
                    total: allZones.length
                });
                renderZones(viewpointZones);
            })
            .catch(error => {
                console.error('Error loading zones:', error);
                renderZones([]); // Clear zones on error
            });
    }

    function renderZones(apiZones) {
        // Clear existing zones
        clearAllZones();

        // Create zones in the UI
        apiZones.forEach(zone => {
            // Make sure we have valid zone data with points
            if (!zone.detection_zone || !zone.detection_zone.points || !Array.isArray(zone.detection_zone.points)) {
                console.warn(`Invalid zone data for zone ${zone.id}`);
                return;
            }

            // API returns natural image coordinates, convert to display coordinates first
            const naturalPoints = zone.detection_zone.points;
            const displayPoints = naturalPoints.map(point =>
                convertNaturalToDisplayCoordinates(point.x, point.y)
            );
            const percentagePoints = displayPoints.map(point => convertToPercentage(point.x, point.y));

            // Draw the zone to UI with actualId, displayPoints, percentagePoints, slotName, and overlapThreshold
            drawZoneToUI(zone.id, displayPoints, percentagePoints, zone.slot_name, zone.overlap_threshold);
        });

        // Update the zones list in sidebar
        updateZonesList();

        // Update slot mapping information for rendered zones
        updateRenderedZonesMappingInfo();
    }

    async function updateRenderedZonesMappingInfo() {
        // Get unique slot names from rendered zones
        const slotNames = [...new Set(Object.values(state.zones).map(zone => zone.slot_name))];

        // For each slot name, get mapping information and log it
        for (const slotName of slotNames) {
            try {
                const mappingInfo = await getSlotMappingInfo(slotName);
                console.log(`Slot "${slotName}" mapping:`, mappingInfo);
            } catch (error) {
                console.error(`Error getting mapping info for slot ${slotName}:`, error);
            }
        }
    }

    // Clear all zones from UI
    function clearAllZones() {
        // Clear zones from state
        state.zones = {};

        // Remove zone elements from DOM
        document.querySelectorAll('.zone-polygon, .zone-label').forEach(el => el.remove());

        // Reset active zone
        state.activeZoneId = null;
    }

    // Remove all drawing points from UI
    function removeAllDrawingPoints() {
        document.querySelectorAll('.drawing-point, .vertex-point, .overlay-line').forEach(el => el.remove());
    }

    // Clear zone visual elements but keep data
    function clearZoneVisuals() {
        document.querySelectorAll('.zone-polygon, .zone-label').forEach(el => el.remove());
    }

    // Draw zone to UI with proper zoom handling - improved
    function drawZoneToUI(actualId, pixelPoints, percentagePoints, slotName, overlapThreshold) {
        // Remove any existing elements with the same ID
        document.querySelectorAll(`.zone-polygon[data-zone-id="${actualId}"], .zone-label[data-zone-id="${actualId}"]`)
            .forEach(el => el.remove());

        // Create polygon element
        const polygon = document.createElement('div');
        polygon.className = 'zone-polygon';
        polygon.dataset.zoneId = actualId;

        // Set basic styles for the polygon
        polygon.style.position = 'absolute';
        polygon.style.top = '0';
        polygon.style.left = '0';
        polygon.style.width = '100%';
        polygon.style.height = '100%';
        polygon.style.backgroundColor = 'rgba(0, 123, 255, 0.2)';
        polygon.style.border = '2px solid rgba(0, 123, 255, 0.5)';

        // Set up clip-path for the polygon shape
        const clipPath = `polygon(${percentagePoints.map(p => `${p.x}% ${p.y}%`).join(', ')})`;
        polygon.style.clipPath = clipPath;
        polygon.style.webkitClipPath = clipPath;

        // Create label with percentage-based positioning
        const label = document.createElement('div');
        label.className = 'zone-label';
        label.textContent = slotName || actualId; // Use slot_name, fallback to actual id
        label.dataset.zoneId = actualId;

        // Calculate center position
        const centerX = percentagePoints.reduce((sum, p) => sum + p.x, 0) / percentagePoints.length;
        const centerY = percentagePoints.reduce((sum, p) => sum + p.y, 0) / percentagePoints.length;

        // Store center position as percentages for repositioning during zoom
        label.dataset.centerXPercent = centerX;
        label.dataset.centerYPercent = centerY;
        
        // Position label using percentages
        label.style.left = `${centerX}%`;
        label.style.top = `${centerY}%`;

        // Add elements to the image container
        imageContainer.appendChild(polygon);
        imageContainer.appendChild(label);

        // Store zone data in state
        state.zones[actualId] = {
            id: actualId,
            slot_name: slotName,
            pixelPoints: [...pixelPoints],
            percentagePoints: [...percentagePoints],
            overlap_threshold: overlapThreshold || 0.3, // Store overlap threshold, default if undefined
            element: polygon,
            label: label
        };

        // Add click handler for editing
        polygon.addEventListener('click', () => {
            if (state.mode === 'view') {
                enterEditMode(actualId);
            }
        });
    }

    // Update zones list in sidebar - group by slot name and show viewpoint badges
    function updateZonesList() {
        // Clear existing list
        zonesList.innerHTML = '';

        const allZoneIds = Object.keys(state.allZones);

        if (allZoneIds.length === 0) {
            // Show "no zones" message
            const noZones = document.createElement('li');
            noZones.id = 'no-zones-message';
            noZones.className = 'list-group-item';
            noZones.textContent = 'No parking slots defined yet';
            zonesList.appendChild(noZones);
            return;
        }

        // Group zones by slot name
        const zonesByName = {};
        allZoneIds.forEach(zoneId => {
            const zone = state.allZones[zoneId];
            const slotName = zone.slot_name || zone.id;

            if (!zonesByName[slotName]) {
                zonesByName[slotName] = [];
            }
            zonesByName[slotName].push(zone);
        });

        // Sort slot names alphabetically
        const sortedSlotNames = Object.keys(zonesByName).sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()));

        // Create list items for each unique slot name
        sortedSlotNames.forEach(slotName => {
            const zones = zonesByName[slotName];
            const listItem = document.createElement('li');

            // Check if any zone in this group belongs to current viewpoint
            const currentViewpointZone = zones.find(zone => zone.viewpoint_id === state.currentViewpointId);
            const hasCurrentViewpoint = !!currentViewpointZone;

            // Set base classes
            let itemClasses = 'list-group-item d-flex justify-content-between align-items-center';

            // Add disabled styling if no zone in current viewpoint
            if (!hasCurrentViewpoint) {
                itemClasses += ' disabled-zone';
            }

            listItem.className = itemClasses;

            // Use the current viewpoint zone ID if available, otherwise use first zone ID
            const primaryZone = currentViewpointZone || zones[0];
            listItem.dataset.slotId = primaryZone.id;

            // Create text container
            const textContainer = document.createElement('div');
            const nameSpan = document.createElement('span');
            nameSpan.textContent = slotName;
            nameSpan.className = hasCurrentViewpoint ? 'fw-bold' : 'text-muted';
            textContainer.appendChild(nameSpan);

            // Add viewpoint badges
            const badgesContainer = document.createElement('div');
            badgesContainer.className = 'ms-2';

            // Sort zones: current viewpoint first, then by viewpoint name
            const sortedZones = zones.sort((a, b) => {
                const isCurrentA = a.viewpoint_id === state.currentViewpointId;
                const isCurrentB = b.viewpoint_id === state.currentViewpointId;

                if (isCurrentA && !isCurrentB) return -1;
                if (!isCurrentA && isCurrentB) return 1;

                const nameA = getViewpointName(a.viewpoint_id);
                const nameB = getViewpointName(b.viewpoint_id);
                return nameA.localeCompare(nameB);
            });

            sortedZones.forEach(zone => {
                const isCurrentViewpoint = zone.viewpoint_id === state.currentViewpointId;
                const viewpointName = getViewpointName(zone.viewpoint_id);

                const badge = document.createElement('span');
                badge.className = isCurrentViewpoint
                    ? 'badge bg-primary me-1'
                    : 'badge bg-secondary me-1';

                const icon = isCurrentViewpoint ? 'bi-eye' : 'bi-eye-slash';
                badge.innerHTML = `<i class="bi ${icon}"></i> ${viewpointName}`;
                badge.title = isCurrentViewpoint ? 'Current viewpoint' : 'Other viewpoint';

                badgesContainer.appendChild(badge);
            });

            textContainer.appendChild(badgesContainer);
            listItem.appendChild(textContainer);

            // Action buttons - only enable if current viewpoint has this slot
            const buttonsDiv = document.createElement('div');
            buttonsDiv.className = 'btn-group';

            const editBtn = document.createElement('button');
            editBtn.className = 'btn btn-sm btn-outline-primary edit-zone me-2';
            editBtn.innerHTML = '<i class="bi bi-pencil-square"></i>';
            editBtn.title = hasCurrentViewpoint ? 'Edit Zone' : 'Switch to a viewpoint that has this slot to edit';
            editBtn.disabled = !hasCurrentViewpoint;
            if (hasCurrentViewpoint) {
                editBtn.onclick = () => enterEditMode(currentViewpointZone.id);
            }

            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'btn btn-sm btn-outline-danger delete-zone';
            deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';
            deleteBtn.title = hasCurrentViewpoint ? 'Delete Zone' : 'Switch to a viewpoint that has this slot to delete';
            deleteBtn.disabled = !hasCurrentViewpoint;
            if (hasCurrentViewpoint) {
                deleteBtn.onclick = () => showDeleteConfirmation(currentViewpointZone.id);
            }

            buttonsDiv.appendChild(editBtn);
            buttonsDiv.appendChild(deleteBtn);
            listItem.appendChild(buttonsDiv);

            zonesList.appendChild(listItem);
        });

        // Refresh autocomplete options after updating the zones list
        refreshSlotNameAutocomplete();
    }

    // Helper function to get viewpoint name by ID
    function getViewpointName(viewpointId) {
        if (!viewpointId) return 'Legacy';
        const viewpoint = state.viewpoints.find(v => v.id === viewpointId);
        return viewpoint ? viewpoint.name : 'Unknown Viewpoint';
    }

    // Helper function to refresh viewpoint selector stats
    function refreshViewpointSelectorStats() {
        const viewpointSelector = document.querySelector('viewpoint-selector');
        if (viewpointSelector && viewpointSelector.refreshStats) {
            console.log('Zone Editor: Refreshing viewpoint selector stats');
            viewpointSelector.refreshStats();
        }
    }

    // Update preview polygon while drawing
    function updatePreviewPolygon() {
        if (state.currentPoints.length < 3) return;

        state.drawingHandler.updateTemporaryPolygon();
    }

    // Drawing workflow functions
    function cancelDrawing() {
        state.drawingHandler.cancelDrawing();
        enterViewMode();
    }

    function finishDrawing() {
        if (state.currentPoints.length < 3) {
            alert('Please draw at least 3 points to create a zone.');
            return;
        }

        // Show zone form
        zoneForm.classList.remove('d-none');
        slotIdInput.value = '';
        slotIdInput.focus();

        // Hide drawing controls
        cancelDrawBtn.classList.add('d-none');
        finishDrawBtn.classList.add('d-none');
    }

    function cancelZoneForm() {
        zoneForm.classList.add('d-none');
        cancelDrawing();
    }

    // CRUD operations
    function saveZone() {
        const slotId = slotIdInput.value.trim();

        if (!slotId) {
            alert('Please enter a slot ID.');
            slotIdInput.focus();
            return;
        }

        if (!state.currentViewpointId) {
            alert('Please select a viewpoint first.');
            return;
        }

        // Check if slot already exists in current viewpoint
        const existingSlotInViewpoint = Object.values(state.zones).find(zone =>
            zone.slot_name === slotId &&
            (zone.viewpoint_id === state.currentViewpointId || !zone.viewpoint_id)
        );

        if (existingSlotInViewpoint) {
            alert('A slot with this name already exists in the current viewpoint. Please use a different name or edit the existing slot.');
            slotIdInput.focus();
            return;
        }

        // Check if slot exists in other viewpoints (for informational purposes)
        const existingSlotInOtherViewpoints = Object.values(state.zones).filter(zone =>
            zone.slot_name === slotId &&
            zone.viewpoint_id !== state.currentViewpointId &&
            zone.viewpoint_id !== null
        );

        if (existingSlotInOtherViewpoints.length > 0) {
            const viewpointNames = existingSlotInOtherViewpoints.map(zone => {
                const viewpoint = state.viewpoints.find(v => v.id === zone.viewpoint_id);
                return viewpoint ? viewpoint.name : 'Unknown';
            }).join(', ');

            const confirmMessage = `Slot "${slotId}" already exists in other viewpoint(s): ${viewpointNames}.\n\n` +
                                 `This will create the same logical slot in the current viewpoint "${getCurrentViewpointName()}".\n` +
                                 `The slot will be linked across viewpoints for detection purposes.\n\n` +
                                 `Do you want to continue?`;

            if (!confirm(confirmMessage)) {
                return;
            }

            console.log(`Creating linked slot "${slotId}" in viewpoint "${getCurrentViewpointName()}"`);
        }

        if (state.currentPoints.length < 3) {
            alert('Please draw at least 3 points to create a zone.');
            return;
        }

        // Convert display coordinates to natural image coordinates for the API: [[x, y], [x, y], ...]
        const coordinates = state.currentPoints.map(point => {
            const naturalCoords = convertDisplayToNaturalCoordinates(point.x, point.y);
            console.log(`Converting display (${point.x}, ${point.y}) to natural (${naturalCoords.x}, ${naturalCoords.y})`);
            return [naturalCoords.x, naturalCoords.y];
        });

        // Also convert to percentages for UI storage
        const percentagePoints = state.currentPoints.map(point => {
            return convertToPercentage(point.x, point.y);
        });

        // Prepare data with overlap threshold and viewpoint association
        const zoneData = {
            slot_name: slotId,
            coordinates: coordinates,
            overlap_threshold: parseFloat(overlapThresholdInput.value),
            viewpoint_id: state.currentViewpointId
        };

        // Send to server
        fetch(`/api/lots/${LOT_ID}/slots`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(zoneData)
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to save zone');
                }
                return response.json();
            })
            .then(result => {
                // Add the new zone to UI
                drawZoneToUI(slotId, state.currentPoints, percentagePoints, slotId, parseFloat(overlapThresholdInput.value));
                updateZonesList();
                refreshViewpointSelectorStats();
                enterViewMode();
            })
            .catch(error => {
                console.error('Error saving zone:', error);
                alert('Failed to save zone. Please try again.');
            });
    }    function saveUpdatedZone(zoneId) {
        console.log('saveUpdatedZone called with zoneId:', zoneId);
        console.log('state.activeZoneId:', state.activeZoneId);
        console.log('Available zones in state:', Object.keys(state.zones));
        
        const slotId = slotIdInput.value.trim();

        if (!slotId) {
            alert('Please enter a slot ID.');
            slotIdInput.focus();
            return;
        }

        // Use current points or existing zone points if no changes
        let pointsToUse = state.currentPoints.length > 0
            ? state.currentPoints
            : state.zones[zoneId].pixelPoints;

        if (pointsToUse.length < 3) {
            alert('A zone must have at least 3 points.');
            return;
        }

        // Convert display coordinates to natural image coordinates for the API: [[x, y], [x, y], ...]
        const coordinates = pointsToUse.map(point => {
            const naturalCoords = convertDisplayToNaturalCoordinates(point.x, point.y);
            return [naturalCoords.x, naturalCoords.y];
        });

        // Also convert to percentages for UI storage
        const percentagePoints = pointsToUse.map(point => {
            return convertToPercentage(point.x, point.y);
        });

        // Prepare data with overlap threshold and viewpoint association
        const zoneData = {
            coordinates: coordinates,
            overlap_threshold: parseFloat(editOverlapThresholdInput.value)
        };

        console.log('Making PUT request to:', `/api/lots/${LOT_ID}/slots/${zoneId}/zone`);
        console.log('With data:', zoneData);

        // Send to server
        fetch(`/api/lots/${LOT_ID}/slots/${zoneId}/zone`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(zoneData)
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to update zone');
                }
                return response.json();
            })
            .then(result => {
                // Remove existing elements from DOM
                document.querySelectorAll(`.zone-polygon[data-zone-id="${zoneId}"], .zone-label[data-zone-id="${zoneId}"]`).forEach(el => el.remove());

                // If ID changed, remove original ID elements as well
                if (slotId !== zoneId) {
                    document.querySelectorAll(`.zone-polygon[data-zone-id="${slotId}"], .zone-label[data-zone-id="${slotId}"]`).forEach(el => el.remove());
                    delete state.zones[zoneId];
                }

                // Ensure any hidden polygons are properly removed
                if (state.zones[zoneId]) {
                    if (state.zones[zoneId].element) {
                        state.zones[zoneId].element.remove();
                    }
                    delete state.zones[zoneId];
                }

                // Add new zone to UI and state
                drawZoneToUI(slotId, pointsToUse, percentagePoints, slotId, parseFloat(editOverlapThresholdInput.value));
                updateZonesList();
                refreshViewpointSelectorStats();
                enterViewMode();
            })
            .catch(error => {
                console.error('Error updating zone:', error);
                alert('Failed to update zone. Please try again.');
            });
    }

    function cancelEditing() {
        // Make sure to restore visibility of original polygon
        if (state.activeZoneId && state.zones[state.activeZoneId] && state.zones[state.activeZoneId].element) {
            state.zones[state.activeZoneId].element.style.display = '';
        }
        enterViewMode();
    }

    function showDeleteConfirmation(zoneId) {
        state.activeZoneId = zoneId;
        confirmationModal.show();
    }

    // Update vertex positions with percentage-based approach - improved for zoom
    function updateVertexPositions() {
        if (state.mode !== 'edit' || !state.currentPoints.length) return;

        // Remove existing vertex points
        document.querySelectorAll('.vertex-point').forEach(el => el.remove());

        // Recalculate pixel points from active zone's percentages
        if (state.activeZoneId && state.zones[state.activeZoneId]) {
            const zone = state.zones[state.activeZoneId];

            // Convert percentage points to current pixel coordinates
            state.currentPoints = zone.percentagePoints.map(point => {
                return {
                    x: (point.x * parkingImage.offsetWidth) / 100,
                    y: (point.y * parkingImage.offsetHeight) / 100
                };
            });
        }

        // Create vertex points at the updated positions
        state.currentPoints.forEach((point, index) => {
            createDraggableVertex(point.x, point.y, index);
        });

        // Update temporary polygon
        state.drawingHandler.currentPoints = [...state.currentPoints];
        state.drawingHandler.updateTemporaryPolygon();
    }

    function deleteZone() {
        if (!state.activeZoneId) return;

        fetch(`/api/lots/${LOT_ID}/slots/${state.activeZoneId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to delete zone');
                }
                return response.json();
            })
            .then(() => {
                // Remove zone from UI
                if (state.zones[state.activeZoneId]) {
                    const zone = state.zones[state.activeZoneId];
                    if (zone.element) zone.element.remove();
                    if (zone.label) zone.label.remove();
                    delete state.zones[state.activeZoneId];
                }

                updateZonesList();
                refreshViewpointSelectorStats();
                confirmationModal.hide();
                enterViewMode();
            })
            .catch(error => {
                console.error('Error deleting zone:', error);
                alert('Failed to delete zone. Please try again.');
            });
    }

    // Add method to the PolygonDrawing prototype if it doesn't exist
    if (!PolygonDrawing.prototype.handleDrawingClick) {
        PolygonDrawing.prototype.handleDrawingClick = function(x, y) {
            // Directly add a point using pixel coordinates
            this.addPoint(x, y);
            // Force update the temporary polygon
            this.updateTemporaryPolygon();
        };
    }

    // Listen for viewpoint image updates
    window.addEventListener('viewpoint-image-updated', (e) => {
        if (e.detail && e.detail.viewpointId) {
            console.log('Zone Editor: Received viewpoint-image-updated event for viewpoint', e.detail.viewpointId);

            // Update cached viewpoint data
            const viewpoint = state.viewpoints.find(v => v.id === e.detail.viewpointId);
            if (viewpoint) {
                console.log(`Zone Editor: Updating cached image path for viewpoint ${viewpoint.name}`);
                viewpoint.image_path = e.detail.imagePath;

                // If this is the currently selected viewpoint, update the displayed image
                if (state.currentViewpointId === e.detail.viewpointId) {
                    console.log('Zone Editor: Updated viewpoint is currently selected, refreshing image');
                    const timestamp = new Date().getTime();
                    const imageUrl = `/static/${e.detail.imagePath}?t=${timestamp}`;
                    parkingImage.src = imageUrl;
                }
            }
        }
    });
});
