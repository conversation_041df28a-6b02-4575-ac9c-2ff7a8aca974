import os
import unittest
from unittest.mock import MagicMock, patch
import uuid

from sqlalchemy import create_engine, text  # Added text import for raw SQL
from sqlalchemy.orm import sessionmaker

# Correct the import names
from app.src.models.events import ParkingLotCreated, ParkingSlotOccupancyChanged
from app.src.models.orm import Base, ParkingEventORM
from app.src.utils.sqlalchemy_event_store import SQLAlchemyEventStore
from app.src.utils.mapper import Mapper # Import Mapper


class TestSQLAlchemyEventStore(unittest.TestCase):
    """Test cases for SQLAlchemyEventStore"""

    def setUp(self):
        """Set up test database and event store"""
        # Arrange: Setup in-memory DB and store
        self.db_url = "sqlite:///:memory:"
        
        # Reset the SQLAlchemyEventStore singleton for this test
        SQLAlchemyEventStore.reset_instance_for_testing()
        
        # Create engine for direct table operations
        self.engine = create_engine(self.db_url)
        
        # Create tables
        Base.metadata.create_all(self.engine)
        
        # Create event store with test database URL
        self.event_store = SQLAlchemyEventStore(self.db_url)
        
        # Generate test data
        self.test_aggregate_id = str(uuid.uuid4())

    def tearDown(self):
        """Clean up test database"""
        # Arrange: Cleanup
        Base.metadata.drop_all(self.engine)
        self.engine.dispose()

    def test_append_event(self):
        """Test appending a single event"""
        # Arrange: Create a test event and its ORM representation
        event = ParkingLotCreated(
            lot_id=self.test_aggregate_id,
            payload={
                "name": "Test Parking Lot",
                "image_path": None
            }
        )
        event_orm = Mapper.dataclass_to_orm_event(event)

        # Act: Append the event
        result = self.event_store.append_event(event_orm)

        # Assert: Verify append result and stored event
        self.assertTrue(result)
        events = self.event_store.get_raw_events_for_aggregate(self.test_aggregate_id)
        self.assertEqual(len(events), 1)
        self.assertEqual(events[0].lot_id, self.test_aggregate_id)
        self.assertEqual(events[0].event_type, "ParkingLotCreated")
        self.assertEqual(events[0].sequence_id, 1) # Assuming sequence starts at 1

    def test_get_events_for_aggregate(self):
        """Test retrieving events for a specific aggregate"""
        # Arrange: Create and append multiple test events
        event1 = ParkingLotCreated(
            lot_id=self.test_aggregate_id,
            payload={"name": "Test Parking Lot", "image_path": None}
        )
        event_orm1 = Mapper.dataclass_to_orm_event(event1)
        self.event_store.append_event(event_orm1)

        event2 = ParkingSlotOccupancyChanged(
            lot_id=self.test_aggregate_id,
            payload={"slot_id": "space-1", "is_occupied": True, "detection_timestamp": None}
        )
        event_orm2 = Mapper.dataclass_to_orm_event(event2)
        self.event_store.append_event(event_orm2)

        # Act: Retrieve events for the aggregate
        events = self.event_store.get_raw_events_for_aggregate(self.test_aggregate_id)

        # Assert: Verify the retrieved events
        self.assertEqual(len(events), 2)
        self.assertEqual(events[0].event_type, "ParkingLotCreated")
        self.assertEqual(events[1].event_type, "ParkingSlotOccupancyChanged")
        self.assertEqual(events[0].sequence_id, 1)
        self.assertEqual(events[1].sequence_id, 2)

    def test_get_all_aggregate_ids(self):
        """Test retrieving all aggregate IDs"""
        # Arrange: Create and append events for multiple aggregates
        aggregate_id1 = str(uuid.uuid4())
        event1 = ParkingLotCreated(
            lot_id=aggregate_id1,
            payload={"name": "Parking Lot 1", "image_path": None}
        )
        event_orm1 = Mapper.dataclass_to_orm_event(event1)
        self.event_store.append_event(event_orm1)

        aggregate_id2 = str(uuid.uuid4())
        event2 = ParkingLotCreated(
            lot_id=aggregate_id2,
            payload={"name": "Parking Lot 2", "image_path": None}
        )
        event_orm2 = Mapper.dataclass_to_orm_event(event2)
        self.event_store.append_event(event_orm2)

        # Act: Retrieve all aggregate IDs
        ids = self.event_store.get_all_aggregate_ids()

        # Assert: Verify the retrieved IDs
        self.assertEqual(len(ids), 2)
        self.assertIn(aggregate_id1, ids)
        self.assertIn(aggregate_id2, ids)

    def test_concurrent_event_append(self):
        """Test handling of concurrent event appends (simulated)"""
        # Arrange: Create and append an initial event
        initial_event = ParkingLotCreated(
            lot_id=self.test_aggregate_id,
            payload={"name": "Test Parking Lot", "image_path": None}
        )
        initial_event_orm = Mapper.dataclass_to_orm_event(initial_event)
        self.event_store.append_event(initial_event_orm) # Sequence 1

        # Arrange: Prepare two more events for the same aggregate
        event1 = ParkingSlotOccupancyChanged(
            lot_id=self.test_aggregate_id,
            payload={"slot_id": "space-1", "is_occupied": True, "detection_timestamp": None}
        )
        event_orm1 = Mapper.dataclass_to_orm_event(event1)

        event2 = ParkingSlotOccupancyChanged(
            lot_id=self.test_aggregate_id,
            payload={"slot_id": "space-2", "is_occupied": True, "detection_timestamp": None}
        )
        event_orm2 = Mapper.dataclass_to_orm_event(event2)

        # Act: Append the two events sequentially (simulating concurrency)
        result1 = self.event_store.append_event(event_orm1) # Should get Sequence 2
        result2 = self.event_store.append_event(event_orm2) # Should get Sequence 3

        # Assert: Verify both appends succeeded and sequence numbers are correct
        self.assertTrue(result1)
        self.assertTrue(result2)
        events = self.event_store.get_raw_events_for_aggregate(self.test_aggregate_id)
        self.assertEqual(len(events), 3)
        self.assertEqual(events[0].sequence_id, 1)
        self.assertEqual(events[1].sequence_id, 2)
        self.assertEqual(events[2].sequence_id, 3)


if __name__ == "__main__":
    unittest.main()