#!/usr/bin/env python3
import sys
import os
sys.path.append('app')

from src.utils.event_sourced_storage import EventSourcedStorage
from src.utils.sqlalchemy_event_store import SQLAlchemyEventStore

def main():
    try:
        # Initialize storage
        event_store = SQLAlchemyEventStore()
        storage = EventSourcedStorage(event_store)

        # Get all parking lots
        lots = storage.get_all_parking_lots()
        print(f'Found {len(lots)} parking lots')

        for lot in lots:
            print(f'\nLot: {lot.name} (ID: {lot.id})')
            if lot.detection_config:
                print(f'  Detection Config:')
                print(f'    Engine Type: {lot.detection_config.engine_type}')
                print(f'    Enabled: {lot.detection_config.is_enabled}')
                print(f'    Update Interval: {lot.detection_config.update_interval}')
                print(f'    Parameters: {lot.detection_config.parameters}')
            else:
                print('  No detection config')
            
            # Get recent events for this lot
            events = storage._get_events_for_lot(lot.id)
            detection_events = [e for e in events if 'DetectionConfig' in e.event_type]
            print(f'  Detection Config Events: {len(detection_events)}')
            for event in detection_events[-3:]:  # Last 3 events
                print(f'    {event.timestamp}: {event.event_type}')
                if hasattr(event, 'payload') and 'parameters' in event.payload:
                    params = event.payload['parameters']
                    if 'confidence_threshold' in params:
                        threshold = params['confidence_threshold']
                        print(f'      confidence_threshold: {threshold}')
            
            # Check occupancy stats
            print(f'  Occupancy Stats:')
            print(f'    Total slots: {len(lot.slots)}')
            occupied_count = sum(1 for slot in lot.slots if slot.is_occupied)
            print(f'    Occupied slots: {occupied_count}')
            print(f'    Occupancy rate: {lot.get_occupancy_rate():.2%}')
            
            # Show slot details
            for slot in lot.slots[:5]:  # First 5 slots
                print(f'    Slot {slot.slot_name}: {"occupied" if slot.is_occupied else "free"}')

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
