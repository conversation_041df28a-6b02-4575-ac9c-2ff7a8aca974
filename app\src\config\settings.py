"""
Application settings and configuration management.
Replaces Flask's current_app configuration system with a centralized settings class.
"""

import os
from pathlib import Path
from typing import Optional, Dict, Any
from fastapi.templating import Jinja2Templates

# Import socketio with proper typing
try:
    import socketio
    SocketIOType = socketio.AsyncServer
except ImportError:
    SocketIOType = Any


class AppSettings:
    """
    Centralized application settings class.
    Replaces Flask's current_app.config and current_app.extensions.
    
    This class follows the singleton pattern to ensure consistent configuration
    access across the entire application.
    """
    
    _instance: Optional['AppSettings'] = None
    _initialized: bool = False
    
    def __new__(cls) -> 'AppSettings':
        """Ensure singleton pattern - only one instance is created."""
        if cls._instance is None:
            cls._instance = super(AppSettings, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """Initialize settings if not already initialized."""
        if not self._initialized:
            self._initialize()
            AppSettings._initialized = True
    
    def _initialize(self):
        """Initialize all configuration settings."""
        # Core application settings
        self.secret_key = os.environ.get('SECRET_KEY', 'dev_key_for_flash_messages')
        
        # File and path settings
        self.upload_folder = Path('static/uploads')
        self.upload_folder.mkdir(parents=True, exist_ok=True)
        
        # Model settings - support both MODEL_PATH and YOLO_MODEL_PATH
        self.model_path = os.environ.get('MODEL_PATH', 'model.pt')
        self.yolo_model_path = os.environ.get('YOLO_MODEL_PATH', 'yolov11n.pt')
        
        # Detection engine settings
        self.detection_confidence_threshold = float(os.environ.get('DETECTION_CONFIDENCE_THRESHOLD', '0.5'))
        self.detection_iou_threshold = float(os.environ.get('DETECTION_IOU_THRESHOLD', '0.45'))
        self.detection_width = int(os.environ.get('DETECTION_WIDTH', '640'))
        self.vehicle_classes = [2, 5, 7, 3]  # car, bus, truck, motorcycle
        
        # Extensions (replaces Flask's current_app.extensions)
        self._socketio: Optional[SocketIOType] = None
        self._templates: Optional[Jinja2Templates] = None

    @property
    def socketio(self) -> Optional[SocketIOType]:
        """Get SocketIO instance."""
        return self._socketio

    @socketio.setter
    def socketio(self, value: SocketIOType):
        """Set SocketIO instance."""
        self._socketio = value
    
    @property
    def templates(self) -> Optional[Jinja2Templates]:
        """Get Jinja2 templates instance."""
        return self._templates
    
    @templates.setter
    def templates(self, value: Jinja2Templates):
        """Set Jinja2 templates instance."""
        self._templates = value
    
    def get_config_dict(self) -> Dict[str, Any]:
        """
        Get configuration as a dictionary for compatibility with existing code.
        This method provides backward compatibility with FastAPI's app.state.config.
        """
        return {
            'SECRET_KEY': self.secret_key,
            'UPLOAD_FOLDER': self.upload_folder,
            'MODEL_PATH': self.model_path,
            'YOLO_MODEL_PATH': self.yolo_model_path,
            'DETECTION_CONFIDENCE_THRESHOLD': self.detection_confidence_threshold,
            'DETECTION_IOU_THRESHOLD': self.detection_iou_threshold,
            'DETECTION_WIDTH': self.detection_width,
            'VEHICLE_CLASSES': self.vehicle_classes,
        }
    
    def get_upload_folder_str(self) -> str:
        """Get upload folder as string."""
        return str(self.upload_folder)
    
    def get_model_path(self) -> str:
        """Get model path."""
        return self.model_path
    
    def get_yolo_model_path(self) -> str:
        """Get YOLO model path with fallback."""
        return self.yolo_model_path
    
    def update_from_dict(self, config_dict: Dict[str, Any]):
        """
        Update settings from a dictionary.
        Useful for testing or dynamic configuration updates.
        """
        if 'SECRET_KEY' in config_dict:
            self.secret_key = config_dict['SECRET_KEY']
        if 'UPLOAD_FOLDER' in config_dict:
            self.upload_folder = Path(config_dict['UPLOAD_FOLDER'])
        if 'MODEL_PATH' in config_dict:
            self.model_path = config_dict['MODEL_PATH']
        if 'YOLO_MODEL_PATH' in config_dict:
            self.yolo_model_path = config_dict['YOLO_MODEL_PATH']
        if 'DETECTION_CONFIDENCE_THRESHOLD' in config_dict:
            self.detection_confidence_threshold = float(config_dict['DETECTION_CONFIDENCE_THRESHOLD'])
        if 'DETECTION_IOU_THRESHOLD' in config_dict:
            self.detection_iou_threshold = float(config_dict['DETECTION_IOU_THRESHOLD'])
        if 'DETECTION_WIDTH' in config_dict:
            self.detection_width = int(config_dict['DETECTION_WIDTH'])
        if 'VEHICLE_CLASSES' in config_dict:
            self.vehicle_classes = config_dict['VEHICLE_CLASSES']


# Global settings instance
settings = AppSettings()


def get_settings() -> AppSettings:
    """
    Get the global settings instance.
    This function provides a convenient way to access settings throughout the application.
    """
    return settings
