#!/usr/bin/env python3
import sys
import os
sys.path.append('app')

from src.utils.event_sourced_storage import EventSourcedStorage
from src.utils.sqlalchemy_event_store import SQLAlchemyEventStore
from src.services.detection_manager import DetectionManager

def main():
    try:
        # Initialize storage
        event_store = SQLAlchemyEventStore()
        storage = EventSourcedStorage(event_store)

        # Get all parking lots
        lots = storage.get_all_parking_lots()
        print(f'Found {len(lots)} parking lots')

        for lot in lots:
            print(f'\nLot: {lot.name} (ID: {lot.id})')
            
            # Try to run detection
            print('Running detection...')
            try:
                results = DetectionManager.run_single_detection(lot.id)
                print(f'Detection results: {len(results)} slots processed')

                for slot_id, result in results.items():
                    slot = lot.get_slot(slot_id)
                    if slot:
                        print(f'  Slot {slot.slot_name}: {"occupied" if result else "free"}')
                    else:
                        print(f'  Slot {slot_id}: {"occupied" if result else "free"} (slot not found)')
                        
            except Exception as e:
                print(f'Error running detection: {e}')
                import traceback
                traceback.print_exc()
            
            # Check current occupancy after detection
            print('\nCurrent occupancy after detection:')
            # Refresh lot state
            lot_refreshed = storage.get_parking_lot(lot.id)
            if lot_refreshed:
                occupied_count = sum(1 for slot in lot_refreshed.slots if slot.is_occupied)
                print(f'  Occupied slots: {occupied_count}/{len(lot_refreshed.slots)}')
                for slot in lot_refreshed.slots[:5]:  # First 5 slots
                    print(f'    Slot {slot.slot_name}: {"occupied" if slot.is_occupied else "free"}')

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
