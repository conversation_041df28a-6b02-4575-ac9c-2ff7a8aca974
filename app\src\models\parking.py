from dataclasses import dataclass, field
from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any, Tuple
from src.models.geometry import Polygon, Point
from src.models.detection import DetectionEngineConfig
from src.models.events import BaseEvent, ParkingEvent  # Import BaseEvent
from src.models.viewpoint import Viewpoint  # Add viewpoint import
from datetime import datetime
from src.services.event_service import emit_parking_event
import math


@dataclass
class ParkingSlot:
    # When updating ParkingSlot, update ParkingSlotORM accordingly
    id: str
    slot_name: str
    detection_zone: Optional[Polygon] = None
    is_occupied: bool = False
    overlap_threshold: float = 0.3  # Default threshold of 30%
    viewpoint_id: Optional[str] = None  # Associates slot with specific viewpoint

    def to_dict(self) -> dict:
        return {
            "id": self.id,
            "slot_name": self.slot_name,
            "detection_zone": self.detection_zone.to_dict() if self.detection_zone else None,
            "is_occupied": self.is_occupied,
            "overlap_threshold": self.overlap_threshold,
            "viewpoint_id": self.viewpoint_id
        }

    @classmethod
    def from_dict(cls, data: dict) -> 'ParkingSlot':
        detection_zone = None
        if data.get("detection_zone"):
            detection_zone = Polygon.from_dict(data["detection_zone"])
        return cls(
            id=data["id"],
            slot_name=data["slot_name"],
            detection_zone=detection_zone,
            is_occupied=data.get("is_occupied", False),
            overlap_threshold=data.get("overlap_threshold", 0.3),
            viewpoint_id=data.get("viewpoint_id")
        )


@dataclass
class ParkingLot:
    # When updating ParkingLot, update ParkingLotORM accordingly
    id: str
    name: str
    image_path: Optional[str] = None
    slots: List[ParkingSlot] = field(default_factory=list)
    detection_config: Optional[DetectionEngineConfig] = None
    events: List[ParkingEvent] = field(default_factory=list)
    viewpoints: List[Viewpoint] = field(default_factory=list)
    default_viewpoint_id: Optional[str] = None

    def add_event(self, event_type, description):
        """Create and emit a new event."""
        event = ParkingEvent(self.id, event_type, description)
        event.timestamp = datetime.now()
        self.events.append(event)
        emit_parking_event(self.id, event)
        return event

    def apply(self, event: BaseEvent) -> None:
        """
        Apply a domain event to update this ParkingLot's state.
        This is used during event replay for state reconstruction.
        
        Args:
            event: The event to apply
        """
        from src.models.events import (
            ParkingLotRenamed, ParkingLotImageUpdated,
            ParkingSlotAdded, ParkingSlotRemoved, ParkingSlotZoneUpdated,
            ParkingSlotThresholdUpdated, ParkingSlotOccupancyChanged,
            DetectionConfigCreated, DetectionConfigUpdated, DetectionConfigDeleted,
            ViewpointAdded, ViewpointRemoved, ViewpointUpdated,
            ViewpointStreamConfigured, ViewpointStreamActivated, ViewpointStreamDeactivated,
            ViewpointStreamStatusChanged,
            ViewpointZoneAdded, ViewpointZoneUpdated, ViewpointZoneRemoved,
            DefaultViewpointSet
        )

        # ParkingLotCreated is not handled here as it's used to create the initial state

        if isinstance(event, ParkingLotRenamed):
            self.name = event.payload['new_name']
        elif isinstance(event, ParkingLotImageUpdated):
            self.image_path = event.payload['new_image_path']

        elif isinstance(event, ParkingSlotAdded):
            slot_name = event.payload['slot_name']
            new_slot = ParkingSlot(
                id=event.payload['slot_id'],
                slot_name=slot_name,
                overlap_threshold=event.payload.get('overlap_threshold', 0.3),
                is_occupied=False,  # The Initial state is not occupied
                viewpoint_id=event.payload.get('viewpoint_id')
            )

            # Create detection zone if zone_points are provided
            if 'zone_points' in event.payload and event.payload['zone_points']:
                points = []
                for point_data in event.payload['zone_points']:
                    points.append(Point(x=point_data['x'], y=point_data['y']))
                new_slot.detection_zone = Polygon(points)

            self.slots.append(new_slot)

        elif isinstance(event, ParkingSlotRemoved):
            self.slots = [s for s in self.slots if s.id != event.payload['slot_id']]

        elif isinstance(event, ParkingSlotZoneUpdated):
            for slot in self.slots:
                if slot.id == event.payload['slot_id']:
                    points = []
                    for point_data in event.payload['new_zone_points']:
                        points.append(Point(x=point_data['x'], y=point_data['y']))
                    slot.detection_zone = Polygon(points)
                    break
        elif isinstance(event, ParkingSlotThresholdUpdated):
            for slot in self.slots:
                if slot.id == event.payload['slot_id']:
                    slot.overlap_threshold = event.payload['new_overlap_threshold']
                    break

        elif isinstance(event, ParkingSlotOccupancyChanged):
            for slot in self.slots:
                if slot.id == event.payload['slot_id']:
                    slot.is_occupied = event.payload['is_occupied']
                    break

        elif isinstance(event, DetectionConfigCreated) or isinstance(event, DetectionConfigUpdated):
            self.detection_config = DetectionEngineConfig(
                engine_type=event.payload['engine_type'],
                is_enabled=event.payload['is_enabled'],
                update_interval=event.payload['update_interval'],
                parameters=event.payload.get('parameters', {})
            )

        elif isinstance(event, DetectionConfigDeleted):
            self.detection_config = None

        # --- Viewpoint Event Handling ---
        elif isinstance(event, ViewpointAdded):
            from src.models.viewpoint import Viewpoint
            new_viewpoint = Viewpoint(
                id=event.payload['viewpoint_id'],
                name=event.payload['name'],
                image_path=event.payload['image_path'],
                camera_position=event.payload.get('camera_position'),
                is_active=event.payload.get('is_active', True),
                sort_order=event.payload.get('sort_order', 0)
            )
            self.viewpoints.append(new_viewpoint)

        elif isinstance(event, ViewpointRemoved):
            self.viewpoints = [v for v in self.viewpoints if v.id != event.payload['viewpoint_id']]
            # Clear default if it was the removed viewpoint
            if self.default_viewpoint_id == event.payload['viewpoint_id']:
                self.default_viewpoint_id = None

        elif isinstance(event, ViewpointUpdated):
            for viewpoint in self.viewpoints:
                if viewpoint.id == event.payload['viewpoint_id']:
                    if 'name' in event.payload:
                        viewpoint.name = event.payload['name']
                    if 'image_path' in event.payload:
                        viewpoint.image_path = event.payload['image_path']
                    if 'camera_position' in event.payload:
                        viewpoint.camera_position = event.payload['camera_position']
                    if 'is_active' in event.payload:
                        viewpoint.is_active = event.payload['is_active']
                    if 'sort_order' in event.payload:
                        viewpoint.sort_order = event.payload['sort_order']
                    break

        elif isinstance(event, ViewpointStreamConfigured):
            for viewpoint in self.viewpoints:
                if viewpoint.id == event.payload['viewpoint_id']:
                    viewpoint.stream_url = event.payload['stream_url']
                    viewpoint.capture_fps = event.payload['capture_fps']
                    break

        elif isinstance(event, ViewpointStreamActivated):
            for viewpoint in self.viewpoints:
                if viewpoint.id == event.payload['viewpoint_id']:
                    viewpoint.is_stream_active = True
                    break

        elif isinstance(event, ViewpointStreamDeactivated):
            for viewpoint in self.viewpoints:
                if viewpoint.id == event.payload['viewpoint_id']:
                    viewpoint.is_stream_active = False
                    viewpoint.stream_status = "inactive"
                    break

        elif isinstance(event, ViewpointStreamStatusChanged):
            for viewpoint in self.viewpoints:
                if viewpoint.id == event.payload['viewpoint_id']:
                    viewpoint.stream_status = event.payload['status']
                    viewpoint.stream_error_message = event.payload.get('error_message')
                    viewpoint.last_frame_time = event.payload.get('last_frame_time')
                    break

        elif isinstance(event, DefaultViewpointSet):
            self.default_viewpoint_id = event.payload['viewpoint_id']

        # Add to events list for backward compatibility
        if isinstance(event, ParkingEvent):
            self.events.append(event)
            # Legacy events could also be handled here using the event type string
        # elif event.event_type == "some_legacy_event":
        #     ... handle legacy event ...

    def get_events(self, page=1, per_page=10) -> Tuple[List[ParkingEvent], int]:
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        selected_events = self.events[start_idx:end_idx]
        sorted_events = sorted(selected_events, key=lambda x: x.timestamp or datetime.min, reverse=True)
        return sorted_events, len(self.events)

    def get_total_event_pages(self, per_page=50) -> int:
        return math.ceil(len(self.events) / per_page)

    # --- Viewpoint Management Methods ---

    def get_viewpoint(self, viewpoint_id: str) -> Optional['Viewpoint']:
        """Get a viewpoint by ID."""
        for viewpoint in self.viewpoints:
            if viewpoint.id == viewpoint_id:
                return viewpoint
        return None

    def get_default_viewpoint(self) -> Optional['Viewpoint']:
        """Get the default viewpoint for this parking lot."""
        if self.default_viewpoint_id:
            return self.get_viewpoint(self.default_viewpoint_id)  # If no default set, return the first active viewpoint
        for viewpoint in self.viewpoints:
            if viewpoint.is_active:
                return viewpoint
        return None

    def get_active_viewpoints(self) -> List['Viewpoint']:
        """Get all active viewpoints, sorted by sort_order."""
        active = [v for v in self.viewpoints if v.is_active]
        return sorted(active, key=lambda v: v.sort_order)

    def get_display_image_path(self) -> Optional[str]:
        """
        Get the image path to display for this parking lot.

        Returns the default viewpoint image if available,
        otherwise returns the lot's main image.
        """
        # Check if we have a default viewpoint with an active stream
        default_viewpoint = self.get_default_viewpoint()
        if default_viewpoint and default_viewpoint.stream_url and default_viewpoint.stream_status == 'active':
            return default_viewpoint.image_path

        # Check if any viewpoint has an active stream
        for viewpoint in self.get_active_viewpoints():
            if viewpoint.stream_url and viewpoint.stream_status == 'active':
                return viewpoint.image_path

        # If no active streams, use default viewpoint image if available
        if default_viewpoint and default_viewpoint.image_path:
            return default_viewpoint.image_path

        # If no default viewpoint, use first active viewpoint image
        for viewpoint in self.get_active_viewpoints():
            if viewpoint.image_path:
                return viewpoint.image_path

        # Fall back to the lot's main image
        return self.image_path

    def has_active_streams(self) -> bool:
        """Check if this lot has any active stream viewpoints."""
        return any(v.stream_url and v.stream_status == 'active' for v in self.get_active_viewpoints())

    def get_viewpoints_for_slot(self, slot_id: str) -> List['Viewpoint']:
        """Get all viewpoints that have slots defined for the specified slot."""
        slot_viewpoint_ids = [slot.viewpoint_id for slot in self.slots if slot.id == slot_id and slot.viewpoint_id]
        return [v for v in self.viewpoints if v.id in slot_viewpoint_ids]

    def calculate_aggregated_occupancy_stats(self, selected_viewpoint_ids: Optional[List[str]] = None) -> Dict[
        str, Any]:
        """
        Calculate parking lot occupancy statistics across multiple viewpoints.
        
        Args:
            selected_viewpoint_ids: List of viewpoint IDs to consider. If None, uses all active viewpoints.
            
        Returns:
            Dict with occupancy statistics including slot-level details
        """
        if selected_viewpoint_ids is None:
            # Use all active viewpoints by default
            viewpoints_to_check = self.get_active_viewpoints()
            selected_viewpoint_ids = [v.id for v in viewpoints_to_check]
        else:
            viewpoints_to_check = [v for v in self.viewpoints if v.id in selected_viewpoint_ids and v.is_active]

        # Get slots that belong to the selected viewpoints
        relevant_slots = [slot for slot in self.slots if slot.viewpoint_id in selected_viewpoint_ids]

        # Calculate stats
        total_slots = len(self.slots)
        visible_slots = len(relevant_slots)
        occupied_slots = sum(1 for slot in relevant_slots if slot.is_occupied)
        available_slots = visible_slots - occupied_slots
        invisible_slots = total_slots - visible_slots

        return {
            'total_slots': total_slots,
            'visible_slots': visible_slots,
            'occupied_slots': occupied_slots,
            'available_slots': available_slots,
            'invisible_slots': invisible_slots,
            'occupancy_rate': (occupied_slots / visible_slots * 100) if visible_slots > 0 else 0,
            'visibility_rate': (visible_slots / total_slots * 100) if total_slots > 0 else 0,
            'slot_details': {
                slot.id: {
                    'slot_name': slot.slot_name,
                    'is_occupied': slot.is_occupied,
                    'viewpoint_id': slot.viewpoint_id,
                    'in_selected_viewpoints': slot.viewpoint_id in selected_viewpoint_ids
                }
                for slot in self.slots
            }
        }

    def get_occupancy_rate(self) -> float:
        if not self.slots:
            return 0.0
        occupied_count = sum(1 for slot in self.slots if slot.is_occupied)
        return occupied_count / len(self.slots)

    def get_available_slots(self) -> List[ParkingSlot]:
        """Get list of available (unoccupied) slots."""
        return [slot for slot in self.slots if not slot.is_occupied]

    def get_occupied_slots(self) -> List[ParkingSlot]:
        """Get list of occupied slots."""
        return [slot for slot in self.slots if slot.is_occupied]

    def get_free_slot_count(self) -> int:
        return sum(1 for slot in self.slots if not slot.is_occupied)

    def get_busy_slot_count(self) -> int:
        return sum(1 for slot in self.slots if slot.is_occupied)

    def get_grouped_slot_stats(self) -> Dict[str, Any]:
        """
        Calculate occupancy statistics based on grouped slots by name.
        Slots with the same name are considered as one logical slot.
        A slot group is considered occupied if ANY instance is occupied.

        Returns:
            Dict with grouped slot statistics
        """
        # Group slots by name
        slots_by_name = {}
        for slot in self.slots:
            slot_name = slot.slot_name
            if slot_name not in slots_by_name:
                slots_by_name[slot_name] = []
            slots_by_name[slot_name].append(slot)

        # Calculate stats for each group
        total_groups = len(slots_by_name)
        occupied_groups = 0

        for slot_name, slot_instances in slots_by_name.items():
            # A group is occupied if ANY instance is occupied
            group_occupied = any(slot.is_occupied for slot in slot_instances)
            if group_occupied:
                occupied_groups += 1

        available_groups = total_groups - occupied_groups
        occupancy_rate = (occupied_groups / total_groups * 100) if total_groups > 0 else 0

        return {
            'total_groups': total_groups,
            'occupied_groups': occupied_groups,
            'available_groups': available_groups,
            'occupancy_rate': occupancy_rate,
            'group_details': {
                slot_name: {
                    'instances': len(slot_instances),
                    'is_occupied': any(slot.is_occupied for slot in slot_instances),
                    'viewpoints': list(set(slot.viewpoint_id for slot in slot_instances if slot.viewpoint_id))
                }
                for slot_name, slot_instances in slots_by_name.items()
            }
        }

    def get_grouped_free_slot_count(self) -> int:
        """Get count of free slot groups (by name)."""
        stats = self.get_grouped_slot_stats()
        return stats['available_groups']

    def get_grouped_busy_slot_count(self) -> int:
        """Get count of occupied slot groups (by name)."""
        stats = self.get_grouped_slot_stats()
        return stats['occupied_groups']

    def add_slot(self, slot: ParkingSlot) -> None:
        self.slots.append(slot)
        self.add_event("SLOT_CREATED", slot.id)

    def toggle_slot(self, slot_id: str) -> bool:
        for slot in self.slots:
            if slot.id == slot_id:
                slot.is_occupied = not slot.is_occupied
                self.add_event("SLOT_OCCUPIED" if slot.is_occupied else "SLOT_VACATED", slot_id)
                return True
        return False

    def set_slot_occupied(self, slot_id: str, occupied: bool) -> bool:
        for slot in self.slots:
            if slot.id == slot_id:
                slot.is_occupied = occupied
                self.add_event("SLOT_OCCUPIED" if occupied else "SLOT_VACATED", slot_id)
                return True
        return False

    def update_slot(self, slot_id: str, detection_zone: Polygon) -> bool:
        for slot in self.slots:
            if slot.id == slot_id:
                slot.detection_zone = detection_zone
                self.add_event("SLOT_UPDATED", slot_id)
                return True
        return False

    def delete_slot(self, slot_id: str) -> bool:
        for i, slot in enumerate(self.slots):
            if slot.id == slot_id:
                self.slots.pop(i)
                self.add_event("SLOT_DELETED", slot_id)
                return True
        return False

    def get_slot(self, slot_id: str) -> Optional[ParkingSlot]:
        for slot in self.slots:
            if slot.id == slot_id:
                return slot
        return None

    def to_dict(self) -> dict:
        config_dict = None
        if self.detection_config:
            config_dict = self.detection_config.to_dict()

        return {
            "id": self.id,
            "name": self.name,
            "image_path": self.image_path,
            "slots": [slot.to_dict() for slot in self.slots],
            "detection_config": config_dict,
            "events": [event.to_dict() for event in self.events],
            "viewpoints": [viewpoint.to_dict() for viewpoint in self.viewpoints],
            "default_viewpoint_id": self.default_viewpoint_id
        }

    @classmethod
    def from_dict(cls, id: str, data: dict) -> 'ParkingLot':
        slots = [ParkingSlot.from_dict(slot_data) for slot_data in data.get("slots", [])]
        detection_config = None
        if data.get("detection_config"):
            detection_config = DetectionEngineConfig.from_dict(data["detection_config"])

        events = []
        for event_data in data.get("events", []):
            events.append(ParkingEvent.from_dict(event_data))

        viewpoints = [Viewpoint.from_dict(vp_data) for vp_data in data.get("viewpoints", [])]

        return cls(
            id=id,
            name=data.get("name", "Unnamed Lot"),
            image_path=data.get("image_path"),
            slots=slots,
            detection_config=detection_config,
            events=events,
            viewpoints=viewpoints,
            default_viewpoint_id=data.get("default_viewpoint_id")
        )
