from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from src.models.geometry import Polygon

@dataclass
class ViewpointZone:
    """Represents a detection zone for a specific slot within a viewpoint."""
    slot_id: str
    slot_name: str

    def to_dict(self) -> dict:
        return {
            "slot_id": self.slot_id,
            "slot_name": self.slot_name,
        }

    @classmethod
    def from_dict(cls, data: dict) -> 'ViewpointZone':
        return cls(
            slot_id=data["slot_id"],
            slot_name=data["slot_name"]
        )

@dataclass
class Viewpoint:
    """Represents a camera viewpoint for a parking lot."""
    id: str
    name: str
    image_path: str
    camera_position: Optional[Dict[str, Any]] = None
    is_active: bool = True
    sort_order: int = 0
    zones: List[ViewpointZone] = field(default_factory=list)
    # Stream-related fields
    stream_url: Optional[str] = None
    capture_fps: float = 1.0
    is_stream_active: bool = False
    stream_status: str = "inactive"  # inactive, connecting, active, error
    stream_error_message: Optional[str] = None
    last_frame_time: Optional[float] = None

    def to_dict(self) -> dict:
        return {
            "id": self.id,
            "name": self.name,
            "image_path": self.image_path,
            "camera_position": self.camera_position,
            "is_active": self.is_active,
            "sort_order": self.sort_order,
            "zones": [zone.to_dict() for zone in self.zones],
            "stream_url": self.stream_url,
            "capture_fps": self.capture_fps,
            "is_stream_active": self.is_stream_active,
            "stream_status": self.stream_status,
            "stream_error_message": self.stream_error_message,
            "last_frame_time": self.last_frame_time
        }

    @classmethod
    def from_dict(cls, data: dict) -> 'Viewpoint':
        return cls(
            id=data["id"],
            name=data["name"],
            image_path=data["image_path"],
            camera_position=data.get("camera_position"),
            is_active=data.get("is_active", True),
            sort_order=data.get("sort_order", 0),
            zones=[ViewpointZone.from_dict(zone) for zone in data.get("zones", [])],
            stream_url=data.get("stream_url"),
            capture_fps=data.get("capture_fps", 1.0),
            is_stream_active=data.get("is_stream_active", False),
            stream_status=data.get("stream_status", "inactive"),
            stream_error_message=data.get("stream_error_message"),
            last_frame_time=data.get("last_frame_time")
        )

    def get_zone_for_slot(self, slot_id) -> Optional[ViewpointZone]:
        """Get the detection zone for a specific slot ID."""
        for zone in self.zones:
            if zone.slot_id == slot_id:
                return zone
        return None
