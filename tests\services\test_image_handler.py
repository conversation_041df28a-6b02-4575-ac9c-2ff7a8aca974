"""
Unit tests for image_handler.py - Fixed to match current implementation
"""
import os
import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch, MagicMock, mock_open
from werkzeug.datastructures import FileStorage
from io import BytesIO
import numpy as np

from app.src.services.image_handler import ImageHandler

class TestImageHandler(unittest.TestCase):
    """Tests for the ImageHandler class"""

    def setUp(self):
        # Create a temporary directory for testing file operations
        self.temp_dir = tempfile.TemporaryDirectory()
        self.upload_folder = Path(self.temp_dir.name)
        self.handler = ImageHandler(storage_dir=str(self.upload_folder))

    def tearDown(self):
        # Clean up the temporary directory
        self.temp_dir.cleanup()

    def test_allowed_file(self):
        """Test allowed_file method with various file types"""
        # Test allowed extensions
        self.assertTrue(ImageHandler.allowed_file('test.jpg'))
        self.assertTrue(ImageHandler.allowed_file('test.jpeg'))
        self.assertTrue(ImageHandler.allowed_file('test.png'))
        self.assertTrue(ImageHandler.allowed_file('test.PNG'))  # Case insensitive
          # Test disallowed extensions
        self.assertFalse(ImageHandler.allowed_file('test.gif'))
        self.assertFalse(ImageHandler.allowed_file('test.pdf'))
        self.assertFalse(ImageHandler.allowed_file('test.exe'))
        self.assertFalse(ImageHandler.allowed_file('test'))  # No extension
        self.assertFalse(ImageHandler.allowed_file(''))  # Empty string



    def test_save_image_with_file_storage(self):
        """Test save_image method with FileStorage object (adapted for current implementation)"""
        # Arrange
        test_content = b'test image content'
        lot_id = "test-lot-1"
        original_filename = "test.jpg"
        
        # Act
        path = self.handler.save_image(test_content, lot_id, original_filename)
        
        # Assert
        expected_prefix = os.path.basename(str(self.upload_folder)) + '/'
        self.assertTrue(path.startswith(expected_prefix))
        self.assertTrue(f'{lot_id}.jpg' in path)
        saved_file_path = self.upload_folder / f'{lot_id}.jpg'
        self.assertTrue(saved_file_path.exists())
        with open(saved_file_path, 'rb') as f:
            self.assertEqual(f.read(), test_content)

    def test_save_image_with_bytes(self):
        """Test save_image method with bytes"""
        test_content = b'test image content'
        lot_id = "test-lot-2"
        original_filename = "test.jpg"
        
        path = self.handler.save_image(test_content, lot_id, original_filename)
        expected_prefix = os.path.basename(str(self.upload_folder)) + '/'
        self.assertTrue(path.startswith(expected_prefix))
        self.assertIn(f'{lot_id}.jpg', path)
        saved_file_path = self.upload_folder / f'{lot_id}.jpg'
        self.assertTrue(saved_file_path.exists())
        with open(saved_file_path, 'rb') as f:
            self.assertEqual(f.read(), test_content)

    def test_save_image_with_invalid_extension(self):
        """Test save_image method with invalid extension (should default to .jpg)"""
        # Arrange
        test_content = b'test image content'
        lot_id = "test-lot-3"
        original_filename = "test.gif"  # Invalid extension
        
        # Act
        path = self.handler.save_image(test_content, lot_id, original_filename)
        
        # Assert - should default to .jpg
        expected_prefix = os.path.basename(str(self.upload_folder)) + '/'
        self.assertTrue(path.startswith(expected_prefix))
        self.assertTrue(f'{lot_id}.jpg' in path)
        saved_file_path = self.upload_folder / f'{lot_id}.jpg'
        self.assertTrue(saved_file_path.exists())

    def test_update_image_with_file_storage_existing_path(self):
        """Test update_image method with FileStorage and existing path"""
        # Arrange - Create an existing file
        old_content = b'old content'
        lot_id = "existing_lot"
        old_filepath = self.upload_folder / f'{lot_id}.jpg'
        with open(old_filepath, 'wb') as f:
            f.write(old_content)
        
        # Create new file
        new_content = b'new content'
        new_file = FileStorage(
            stream=BytesIO(new_content),
            filename='new_image.png',
            content_type='image/png'
        )
          # Act
        path = ImageHandler.update_image(new_file, lot_id, str(self.upload_folder), f'uploads/{lot_id}.jpg')
        
        # Assert
        expected_prefix = os.path.basename(str(self.upload_folder)) + '/'
        self.assertTrue(path.startswith(expected_prefix))
        self.assertTrue(f'{lot_id}.png' in path)
        new_filepath = self.upload_folder / f'{lot_id}.png'
        self.assertTrue(new_filepath.exists())
        with open(new_filepath, 'rb') as f:
            self.assertEqual(f.read(), new_content)
        self.assertFalse(old_filepath.exists())  # Old file should be deleted

    def test_update_image_with_file_storage_no_existing_path(self):
        """Test update_image method with FileStorage and no existing path"""
        test_content = b'test image content'
        lot_id = "new_lot"
        test_file = FileStorage(
            stream=BytesIO(test_content),
            filename='test.jpg',
            content_type='image/jpeg'
        )
        path = ImageHandler.update_image(test_file, lot_id, str(self.upload_folder), None)
        expected_prefix = os.path.basename(str(self.upload_folder)) + '/'
        self.assertTrue(path.startswith(expected_prefix))
        self.assertIn(f'{lot_id}.jpg', path)
        saved_file_path = self.upload_folder / f'{lot_id}.jpg'
        self.assertTrue(saved_file_path.exists())
        with open(saved_file_path, 'rb') as f:
            self.assertEqual(f.read(), test_content)

    def test_update_image_error_removing_old_file(self):
        """Test update_image method when there's an error removing the old file"""
        # Arrange
        test_content = b'test image content'
        lot_id = "error_lot"
        test_file = FileStorage(
            stream=BytesIO(test_content),
            filename='test.jpg',
            content_type='image/jpeg'
        )
        
        # Mock os.path.exists to return True but os.remove to raise OSError
        with patch('os.path.exists', return_value=True):
            with patch('os.remove', side_effect=OSError("Test error")):                # Act - Should handle the error gracefully
                path = ImageHandler.update_image(test_file, lot_id, str(self.upload_folder), f'uploads/{lot_id}.jpg')
        
        # Assert
        expected_prefix = os.path.basename(str(self.upload_folder)) + '/'
        self.assertTrue(path.startswith(expected_prefix))
        self.assertTrue(f'{lot_id}.jpg' in path)
        saved_file_path = self.upload_folder / f'{lot_id}.jpg'
        self.assertTrue(saved_file_path.exists())
        with open(saved_file_path, 'rb') as f:
            self.assertEqual(f.read(), test_content)

    def test_save_image(self):
        """Test saving an image using the instance method."""
        # Arrange
        image_data = b'test image content'  # Use bytes instead of numpy array
        lot_id = "test-lot-1"
        original_filename = "test.jpg"
        
        # Act
        result_path = self.handler.save_image(image_data, lot_id, original_filename)
          # Assert
        expected_prefix = os.path.basename(str(self.upload_folder)) + '/'
        self.assertTrue(result_path.startswith(expected_prefix))
        self.assertIn(f'{lot_id}.jpg', result_path)
        saved_file_path = self.upload_folder / f'{lot_id}.jpg'
        self.assertTrue(saved_file_path.exists())
        with open(saved_file_path, 'rb') as f:
            self.assertEqual(f.read(), image_data)

    def test_save_image_creates_dir(self):
        """Test that save_image creates the directory when it doesn't exist."""
        # Arrange - Create handler with non-existing directory
        non_existing_dir = self.upload_folder / 'non_existing'
        handler = ImageHandler(storage_dir=str(non_existing_dir))
        image_data = b'test image content'
        lot_id = "test-lot-1"
        original_filename = "test.jpg"
        
        # Act
        result_path = handler.save_image(image_data, lot_id, original_filename)
        
        # Assert
        self.assertTrue(non_existing_dir.exists())  # Directory should be created
        saved_file_path = non_existing_dir / f'{lot_id}.jpg'
        self.assertTrue(saved_file_path.exists())

    def test_save_image_with_invalid_type(self):
        """Test save_image method with invalid input type"""
        # Arrange
        invalid_data = "not bytes"  # Should be bytes
        lot_id = "test-lot-1"
        original_filename = "test.jpg"
        
        # Act & Assert
        with self.assertRaises(ValueError):
            # This should raise ValueError because invalid_data is not bytes
            self.handler.save_image(invalid_data, lot_id, original_filename)  # type: ignore

    def test_update_image_with_invalid_file_type(self):
        """Test update_image method with invalid file type"""
        # Arrange
        test_content = b'test content'
        test_file = FileStorage(
            stream=BytesIO(test_content),
            filename='test.gif',  # Invalid extension
            content_type='image/gif'
        )
        lot_id = "test_lot"
        
        # Act & Assert
        with self.assertRaises(ValueError):
            ImageHandler.update_image(test_file, lot_id, str(self.upload_folder), None)

if __name__ == '__main__':
    unittest.main()
