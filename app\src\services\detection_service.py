"""
Detection service that coordinates when to run detection and where to emit results.
"""
import logging
import time
from datetime import datetime
from typing import List, Dict, Any, Optional

from src.models.detection import DetectionResult
from src.services.detection_manager import DetectionManager
from src.services.event_service import event_service
from src.services.image_handler import ImageHandler
from src.services.occupancy_manager import OccupancyManager
from src.utils import logger

LOGGER = logger.setup_logger(__name__)


class DetectionService:
    """Service to coordinate the running of detection and handling the results."""
    
    def __init__(
        self,
        detection_manager: DetectionManager,
        image_handler: Optional[ImageHandler] = None,
        occupancy_manager: Optional[OccupancyManager] = None
    ):
        """
        Initialize the detection service.
        
        Args:
            detection_manager: The manager for the detection engine
            image_handler: Optional handler for images
            occupancy_manager: Optional manager for occupancy state
        """
        self.detection_manager = detection_manager
        self.image_handler = image_handler or ImageHandler()
        self.occupancy_manager = occupancy_manager or OccupancyManager()
    
    def run_detection_for_lot(self, lot_id: str) -> Dict[str, Any]:
        """
        Run detection for a specific parking lot and update occupancy status.
        
        Args:
            lot_id: ID of the parking lot to run detection on
            
        Returns:
            Dictionary with detection results
        """
        # Start time for performance metrics
        start_time = time.time()
        
        # Emit detection started event
        event_service.emit_detection_status(lot_id, "processing")
        
        try:
            # Run batch detection for all viewpoints using the manager
            LOGGER.debug(f"Running batch detection for all viewpoints in lot {lot_id}")
            batch_results = self.detection_manager.run_batch_detection(lot_id)

            if not batch_results:
                # Emit detection error event if no results
                event_service.emit_detection_status(
                    lot_id,
                    "error",
                    error="No detection results returned from batch processing"
                )
                return {
                    "success": False,
                    "error": "No detection results returned from batch processing",
                    "detection_time": time.time() - start_time
                }

            # Flatten batch results into a single list for compatibility
            results: List[DetectionResult] = []
            for viewpoint_id, viewpoint_results in batch_results.items():
                results.extend(viewpoint_results)
                LOGGER.debug(f"Added {len(viewpoint_results)} results from viewpoint {viewpoint_id}")

            LOGGER.info(f"Batch detection completed for lot {lot_id}: {len(results)} total results from {len(batch_results)} viewpoints")

            if not results:
                # Emit detection error event if no flattened results
                event_service.emit_detection_status(
                    lot_id,
                    "error",
                    error="No detection results after processing all viewpoints"
                )
                return {
                    "success": False,
                    "error": "No detection results after processing all viewpoints",
                    "detection_time": time.time() - start_time
                }
                
            # Update occupancy status based on detection results
            updates = self.occupancy_manager.update_from_detection_results(lot_id, results)
            
            # Calculate stats for the response
            total_slots = len(results)
            occupied_slots = sum(1 for r in results if r.is_occupied)
            free_slots = total_slots - occupied_slots
            
            # Calculate how many slots changed
            changed_count = sum(1 for slot_id, success in updates.items() if success)
            
            # Emit successful detection event
            event_service.emit_detection_status(
                lot_id,
                "completed",
                updates={
                    "total_slots": total_slots,
                    "occupied_slots": occupied_slots,
                    "free_slots": free_slots,
                    "changed_slots": changed_count
                }
            )
            
            # Return success with stats
            return {
                "success": True,
                "total_slots": total_slots,
                "occupied_slots": occupied_slots,
                "free_slots": free_slots,
                "changed_slots": changed_count,
                "detection_time": time.time() - start_time
            }
            
        except Exception as e:
            LOGGER.exception(f"Error running detection for lot {lot_id}: {str(e)}")
            
            # Emit detection error event
            event_service.emit_detection_status(
                lot_id,
                "error",
                error=str(e)
            )
            
            # Return error information
            return {
                "success": False,
                "error": str(e),
                "detection_time": time.time() - start_time
            }