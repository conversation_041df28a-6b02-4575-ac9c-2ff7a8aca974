from dataclasses import dataclass, field
from typing import Dict, List, Optional

from src.config.settings import get_settings


@dataclass
class DetectionResult:
    # Not stored in DB directly
    slot_id: str
    slot_name: str
    is_occupied: bool
    confidence: float


@dataclass
class DetectionEngineConfig:
    # When updating DetectionEngineConfig, update DetectionEngineConfigORM accordingly
    engine_type: str = "yolo"
    is_enabled: bool = False
    update_interval: int = 60
    parameters: dict = field(default_factory=dict)

    def to_dict(self) -> Dict:
        """Convert the config object to a dictionary for serialization."""
        return {
            "engine_type": self.engine_type,
            "is_enabled": self.is_enabled,
            "update_interval": self.update_interval,
            "parameters": self.parameters or {}
        }

    def dict(self) -> Dict:
        """Alias for to_dict() for compatibility with tests."""
        return self.to_dict()

    @classmethod
    def from_dict(cls, data: Dict) -> 'DetectionEngineConfig':
        """Create a DetectionEngineConfig object from a dictionary."""
        return cls(
            engine_type=data.get("engine_type", "yolo"),
            is_enabled=data.get("is_enabled", False),
            update_interval=data.get("update_interval", 60),
            parameters=data.get("parameters", {})
        )

    @classmethod
    def for_yolo(cls, model_path: str| None = None, confidence_threshold: float = 0.5,
                    overlap_threshold: float = 0.3, iou_threshold: float = 0.45,
                    vehicle_classes: List[int] | None = None, detection_width: int = 640) -> 'DetectionEngineConfig':
        """Create a YOLO-specific configuration."""
        if vehicle_classes is None:
            vehicle_classes = [2, 5, 7, 3]  # Default vehicle classes for YOLO

        if model_path is None:
            model_path = get_settings().get_yolo_model_path()
        return cls(
            engine_type="yolo",
            parameters={
                "model_path": model_path,
                "confidence_threshold": confidence_threshold,
                "overlap_threshold": overlap_threshold,
                "iou_threshold": iou_threshold,
                "vehicle_classes": vehicle_classes,
                "detection_width": detection_width
            }
        )

class DetectionEngine:
    # Not stored in DB directly
    """Base class for parking detection engines."""

    def detect(self, parking_lot_id: str, image_path: Optional[str] = None) -> List[DetectionResult]:
        """Run detection on a parking lot and return results."""
        raise NotImplementedError("Subclasses must implement this method")

    def get_configuration(self) -> Dict:
        """Get current configuration parameters."""
        raise NotImplementedError("Subclasses must implement this method")

    def configure(self, config: Dict) -> bool:
        """Update engine configuration parameters."""
        raise NotImplementedError("Subclasses must implement this method")

    def is_available(self) -> bool:
        """Check if the detection engine is available and operational."""
        raise NotImplementedError("Subclasses must implement this method")

    def get_classes(self) -> List[str]:
        """Get the list of classes the detection engine can detect."""
        raise NotImplementedError("Subclasses must implement this method")
