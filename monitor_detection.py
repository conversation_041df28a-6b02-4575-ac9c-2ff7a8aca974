#!/usr/bin/env python3
import sys
import os
import time
sys.path.append('app')

from src.utils.event_sourced_storage import EventSourcedStorage
from src.utils.sqlalchemy_event_store import SQLAlchemyEventStore
from src.services.detection_manager import DetectionManager

def main():
    try:
        # Initialize storage
        event_store = SQLAlchemyEventStore()
        storage = EventSourcedStorage(event_store)

        # Get all parking lots
        lots = storage.get_all_parking_lots()
        print(f'Found {len(lots)} parking lots')

        for lot in lots:
            print(f'\nLot: {lot.name} (ID: {lot.id})')
            
            # Check if detection is now running
            print(f'  Detection Thread Status:')
            if lot.id in DetectionManager._detection_threads:
                thread = DetectionManager._detection_threads[lot.id]
                print(f'    Thread exists: {thread.is_alive()}')
            else:
                print('    No detection thread found')
            
            # Get initial occupancy event count
            events = storage._get_events_for_lot(lot.id)
            initial_occupancy_events = len([e for e in events if 'Occupancy' in e.event_type])
            print(f'  Initial occupancy events: {initial_occupancy_events}')
            
            # Get initial occupancy state
            initial_occupied = sum(1 for slot in lot.slots if slot.is_occupied)
            print(f'  Initial occupied slots: {initial_occupied}/{len(lot.slots)}')
            
            # Wait for a detection cycle (update interval is 60 seconds, but let's wait a bit)
            print(f'  Waiting 10 seconds for detection to run...')
            time.sleep(10)
            
            # Check if new occupancy events were created
            refreshed_lot = storage.get_parking_lot(lot.id)
            events_after = storage._get_events_for_lot(lot.id)
            final_occupancy_events = len([e for e in events_after if 'Occupancy' in e.event_type])
            
            print(f'  Final occupancy events: {final_occupancy_events}')
            print(f'  New events created: {final_occupancy_events - initial_occupancy_events}')
            
            if refreshed_lot:
                final_occupied = sum(1 for slot in refreshed_lot.slots if slot.is_occupied)
                print(f'  Final occupied slots: {final_occupied}/{len(refreshed_lot.slots)}')
                print(f'  Occupancy rate: {refreshed_lot.get_occupancy_rate():.2%}')
                
                # Show any slots that changed
                for slot in refreshed_lot.slots:
                    original_slot = lot.get_slot(slot.id)
                    if original_slot and original_slot.is_occupied != slot.is_occupied:
                        print(f'    Slot {slot.slot_name} changed: {"free" if original_slot.is_occupied else "occupied"} -> {"occupied" if slot.is_occupied else "free"}')

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
