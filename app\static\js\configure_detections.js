// Update threshold value displays when sliders change
function updateSliderDisplay(sliderId, displayId) {
    document.getElementById(sliderId).addEventListener('input', function() {
        if (sliderId === 'detection_width') {
            document.getElementById(displayId).textContent = this.value + 'px';
        } else {
            document.getElementById(displayId).textContent = this.value;
        }
    });
}

// Show/hide engine-specific settings based on engine type selection
function toggleEngineSettings() {
    const engineType = document.getElementById('engine_type').value;
    document.querySelectorAll('.engine-specific-settings').forEach(el => {
        el.style.display = 'none';
    });

    const specificSettings = document.getElementById(engineType + '_settings');
    if (specificSettings) {
        specificSettings.style.display = 'block';
    }
}

// Reset resolution to default value (640px)
function resetResolution() {
    const resolutionSlider = document.getElementById('detection_width');
    resolutionSlider.value = 640;
    document.getElementById('resolution_value').textContent = '640px';
}

// Fetch classes supported by the current model
async function fetchSupportedClasses() {
    const lotId = document.querySelector('[data-lot-id]').dataset.lotId;
    try {
        const response = await fetch(`/dashboard/${lotId}/classes`);
        if (!response.ok) {
            throw new Error('Failed to fetch classes');
        }
        const data = await response.json();
        return data.classes || [];
    } catch (error) {
        console.error('Error fetching classes:', error);
        return [];
    }
}

// Setup class selection with autocomplete
async function setupClassAutocomplete() {
    const classes = await fetchSupportedClasses();
    const classInput = document.getElementById('class_input');
    const classesList = document.getElementById('classes_list');
    const selectedClassesContainer = document.getElementById('selected_classes');
    const selectedClassesInput = document.getElementById('selected_classes_input');
    
    if (!classInput || !classesList || !selectedClassesContainer || !selectedClassesInput) {
        console.error('Class selection elements not found');
        return;
    }
    
    let selectedClasses = [];
    
    // Initialize from existing vehicle classes checkboxes
    document.querySelectorAll('input[name="vehicle_classes"]:checked').forEach(checkbox => {
        const classId = parseInt(checkbox.value);
        const className = checkbox.nextElementSibling.textContent.trim();
        addClassToSelection(classId, className);
    });
    
    function updateSelectedClassesInput() {
        selectedClassesInput.value = JSON.stringify(selectedClasses.map(c => c.id));
    }
    
    function addClassToSelection(id, name) {
        // Check if already in selection
        if (selectedClasses.some(c => c.id === id)) return;
        
        selectedClasses.push({ id, name });
        updateSelectedClassesInput();
        
        // Create tag element
        const tag = document.createElement('div');
        tag.className = 'selected-class-tag';
        tag.innerHTML = `
            <span>${name} (${id})</span>
            <button type="button" class="remove-class-btn" data-class-id="${id}">&times;</button>
        `;
        selectedClassesContainer.appendChild(tag);
        
        // Add event listener to remove button
        tag.querySelector('.remove-class-btn').addEventListener('click', function() {
            const classId = parseInt(this.dataset.classId);
            selectedClasses = selectedClasses.filter(c => c.id !== classId);
            updateSelectedClassesInput();
            tag.remove();
            
            // Uncheck corresponding checkbox if it exists
            const checkbox = document.querySelector(`input[name="vehicle_classes"][value="${classId}"]`);
            if (checkbox) checkbox.checked = false;
        });
    }
    
    // Setup autocomplete
    if (classes.length > 0) {
        // Create dropdown options
        classes.forEach((className, index) => {
            const option = document.createElement('div');
            option.className = 'class-option';
            option.textContent = `${className} (${index})`;
            option.dataset.className = className;
            option.dataset.classId = index;
            classesList.appendChild(option);
            
            // Add click event
            option.addEventListener('click', function() {
                const id = parseInt(this.dataset.classId);
                const name = this.dataset.className;
                addClassToSelection(id, name);
                classInput.value = '';
                classesList.style.display = 'none';
            });
        });
        
        // Show/hide dropdown based on input
        classInput.addEventListener('input', function() {
            const value = this.value.toLowerCase();
            if (value.length === 0) {
                classesList.style.display = 'none';
                return;
            }
            
            let hasMatches = false;
            classesList.querySelectorAll('.class-option').forEach(option => {
                const text = option.textContent.toLowerCase();
                if (text.includes(value)) {
                    option.style.display = 'block';
                    hasMatches = true;
                } else {
                    option.style.display = 'none';
                }
            });
            
            classesList.style.display = hasMatches ? 'block' : 'none';
        });
        
        // Handle focus/blur
        classInput.addEventListener('focus', function() {
            if (this.value.length > 0) {
                classesList.style.display = 'block';
            }
        });
        
        document.addEventListener('click', function(event) {
            if (!classesList.contains(event.target) && event.target !== classInput) {
                classesList.style.display = 'none';
            }
        });
    }
    
    // Sync the checkboxes with selected classes
    document.querySelectorAll('input[name="vehicle_classes"]').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const classId = parseInt(this.value);
            const className = this.nextElementSibling.textContent.trim();
            
            if (this.checked) {
                addClassToSelection(classId, className);
            } else {
                // Remove class from selection
                selectedClasses = selectedClasses.filter(c => c.id !== classId);
                updateSelectedClassesInput();
                
                // Remove tag
                const tag = selectedClassesContainer.querySelector(`.remove-class-btn[data-class-id="${classId}"]`);
                if (tag) tag.parentElement.remove();
            }
        });
    });
}

// Initialize all event listeners, even for initially hidden elements
function initializeEventListeners() {
    // Set up all slider displays
    updateSliderDisplay('probability_threshold', 'threshold_value');
    updateSliderDisplay('confidence_threshold', 'confidence_value');
    updateSliderDisplay('overlap_threshold', 'overlap_value');
    updateSliderDisplay('iou_threshold', 'iou_value');  // Add the IoU slider
    updateSliderDisplay('detection_width', 'resolution_value');
    
    // Add reset resolution button handler
    const resetButton = document.getElementById('reset_resolution');
    if (resetButton) {
        resetButton.addEventListener('click', resetResolution);
    } else {
        console.error("Reset resolution button not found");
    }
}

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all event listeners
    initializeEventListeners();
    
    // Initialize engine type selector
    document.getElementById('engine_type').addEventListener('change', toggleEngineSettings);
    
    // Show proper settings section on page load
    toggleEngineSettings();
    
    // Setup class autocomplete for YOLO
    setupClassAutocomplete();
    
    // Form submission handler
    document.getElementById('engine_config_form').addEventListener('submit', function(e) {
        const selectedClassesInput = document.getElementById('selected_classes_input');

        // If no classes are selected for YOLO, show warning but allow submission (defaults will be used)
        if (document.getElementById('engine_type').value === 'yolo' &&
            (!selectedClassesInput.value || selectedClassesInput.value === '[]')) {
            console.warn('No detection classes selected, server will use default classes');
        }
    });
});
