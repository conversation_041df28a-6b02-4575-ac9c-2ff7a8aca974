/**
 * ViewpointSelector Web Component
 * Provides a dropdown interface for selecting and managing viewpoints
 */
class ViewpointSelector extends HTMLElement {
    constructor() {
        super();
        this.attachShadow({ mode: 'open' });
        this.viewpoints = [];
        this.currentViewpoint = null;
        this.lotId = null;
        this.allSlots = [];
        this.viewpointStats = new Map();
        this.streamStatuses = new Map(); // Track stream statuses
    }

    static get observedAttributes() {
        return ['lot-id', 'current-viewpoint'];
    }

    connectedCallback() {
        console.log('ViewpointSelector: Connected to DOM');
        this.lotId = this.getAttribute('lot-id');
        this.currentViewpoint = this.getAttribute('current-viewpoint');
        console.log(`ViewpointSelector: Initialized with lot-id=${this.lotId}, current-viewpoint=${this.currentViewpoint}`);

        this.render();
        this.setupEventListeners();
        this.setupGlobalEventListeners();

        // Load viewpoints and slots after everything is set up
        this.loadViewpoints();
        this.loadSlotStats();
    }

    attributeChangedCallback(name, oldValue, newValue) {
        if (name === 'lot-id') {
            this.lotId = newValue;
            if (this.shadowRoot) {
                this.loadViewpoints();
            }
        } else if (name === 'current-viewpoint') {
            this.currentViewpoint = newValue;
            if (this.shadowRoot) {
                this.updateSelection();
            }
        }
    }

    render() {
        this.shadowRoot.innerHTML = `
            <style>
                :host {
                    display: block;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    position: static;
                    z-index: 1;
                }

                .viewpoint-selector {
                    position: static;
                    min-width: 200px;
                    z-index: 1;
                }

                .selector-button {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    width: 100%;
                    padding: 8px 12px;
                    border: 1px solid #dee2e6;
                    border-radius: 6px;
                    background: white;
                    cursor: pointer;
                    font-size: 14px;
                    transition: all 0.2s ease;
                }

                .selector-button:hover {
                    border-color: #0d6efd;
                    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
                }

                .selector-button:focus {
                    outline: none;
                    border-color: #0d6efd;
                    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
                }

                .current-viewpoint {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                .viewpoint-icon {
                    width: 16px;
                    height: 16px;
                    background: #6c757d;
                    border-radius: 3px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 10px;
                    color: white;
                    font-weight: bold;
                }

                .viewpoint-icon.default {
                    background: #0d6efd;
                }

                .dropdown-arrow {
                    /* No transition to prevent hover conflicts */
                }

                .selector-button.open .dropdown-arrow {
                    transform: rotate(180deg);
                }

                .dropdown {
                    position: fixed;
                    background: white;
                    border: 2px solid #007bff;
                    border-radius: 6px;
                    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
                    z-index: 99999;
                    max-height: 300px;
                    overflow-y: auto;
                    display: none;
                    min-height: 50px;
                    min-width: 250px;
                    /* Ensure dropdown is not affected by parent transforms */
                    transform: none !important;
                    will-change: auto;
                    /* Create new stacking context */
                    isolation: isolate;
                }

                .dropdown.open {
                    display: block;
                }

                .viewpoint-option {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 12px 15px;
                    cursor: pointer;
                    transition: background-color 0.2s ease;
                    border-bottom: 1px solid #dee2e6;
                    background: white;
                    min-height: 40px;
                }

                .viewpoint-option:last-child {
                    border-bottom: none;
                }

                /* Hover styles handled by external CSS */

                .viewpoint-option.selected {
                    background-color: #e7f3ff;
                    color: #0d6efd;
                }

                .viewpoint-info {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                .viewpoint-name {
                    font-weight: 500;
                }

                .viewpoint-meta {
                    font-size: 12px;
                    color: #6c757d;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    flex-wrap: wrap;
                }

                .viewpoint-stats {
                    display: flex;
                    gap: 6px;
                    font-size: 11px;
                    margin-top: 2px;
                }

                .stat-item {
                    display: flex;
                    align-items: center;
                    gap: 2px;
                    padding: 1px 4px;
                    border-radius: 3px;
                    font-weight: 500;
                }

                .stat-item.available {
                    background-color: #d1edff;
                    color: #0969da;
                }

                .stat-item.occupied {
                    background-color: #ffebe9;
                    color: #cf222e;
                }

                .stat-icon {
                    font-size: 10px;
                }

                .default-badge {
                    background: #0d6efd;
                    color: white;
                    font-size: 10px;
                    padding: 2px 6px;
                    border-radius: 10px;
                    font-weight: 500;
                }

                .viewpoint-actions {
                    display: flex;
                    gap: 4px;
                }

                .action-btn {
                    background: none;
                    border: none;
                    padding: 4px;
                    border-radius: 3px;
                    cursor: pointer;
                    font-size: 12px;
                    color: #6c757d;
                    transition: all 0.2s ease;
                }

                /* Action button hover styles handled by external CSS */

                .add-viewpoint {
                    padding: 10px 12px;
                    border-top: 1px solid #dee2e6;
                    color: #0d6efd;
                    font-weight: 500;
                    background-color: #f8f9fa;
                }

                /* Add viewpoint hover styles handled by external CSS */

                .loading {
                    padding: 10px 12px;
                    text-align: center;
                    color: #6c757d;
                    font-size: 14px;
                }

                .error {
                    padding: 10px 12px;
                    color: #dc3545;
                    font-size: 14px;
                }

                .viewpoint-badges {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    margin-top: 2px;
                }

                .type-badge {
                    font-size: 10px;
                    padding: 2px 4px;
                    border-radius: 3px;
                    font-weight: 500;
                    text-transform: uppercase;
                }

                .type-badge.stream {
                    background-color: #0d6efd;
                    color: white;
                }

                .type-badge.photo {
                    background-color: #6c757d;
                    color: white;
                }

                .stream-status {
                    font-size: 12px;
                    margin-left: 2px;
                }

                .stream-status.status-inactive {
                    color: #6c757d; /* Gray for inactive/deactivated */
                }

                .stream-status.status-connecting {
                    color: #0d6efd; /* Blue for connecting/starting */
                }

                .stream-status.status-active {
                    color: #198754; /* Green for active/frames arriving */
                }

                .stream-status.status-error {
                    color: #dc3545; /* Red for error */
                }
            </style>

            <div class="viewpoint-selector">
                <button class="selector-button" type="button">
                    <div class="current-viewpoint">
                        <div class="viewpoint-icon">📹</div>
                        <span class="viewpoint-name">Select Viewpoint</span>
                    </div>
                    <div class="dropdown-arrow">▼</div>
                </button>

                <div class="dropdown">
                    <div class="loading">Loading viewpoints...</div>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        const button = this.shadowRoot.querySelector('.selector-button');
        const dropdown = this.shadowRoot.querySelector('.dropdown');

        button.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleDropdown();
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            // Check if the click is outside the component
            if (!this.contains(e.target) && !this.shadowRoot.contains(e.target)) {
                this.closeDropdown();
            }
        });

        dropdown.addEventListener('click', (e) => {
            e.stopPropagation();
        });
    }

    setupGlobalEventListeners() {
        // Listen for global viewpoints-changed events
        window.addEventListener('viewpoints-changed', (e) => {
            if (e.detail && e.detail.lotId === this.lotId) {
                console.log('ViewpointSelector: Received viewpoints-changed event for lot', this.lotId);
                this.refresh();
            }
        });

        // Listen for viewpoint image updates
        window.addEventListener('viewpoint-image-updated', (e) => {
            if (e.detail && e.detail.viewpointId) {
                console.log('ViewpointSelector: Received viewpoint-image-updated event for viewpoint', e.detail.viewpointId);
                this.updateViewpointImagePath(e.detail.viewpointId, e.detail.imagePath);
            }
        });

        // Listen for stream status updates via WebSocket
        if (window.socketConnection) {
            this.setupStreamStatusListeners();
        } else {
            // Wait for socket connection to be established
            window.addEventListener('socket-connected', () => {
                this.setupStreamStatusListeners();
            });
        }
    }

    setupStreamStatusListeners() {
        if (!window.socketConnection) {
            console.warn('ViewpointSelector: No socket connection available for stream status updates');
            return;
        }

        // Listen for stream status change events and update status immediately
        const streamEvents = ['stream_started', 'stream_stopped', 'stream_error', 'stream_connecting', 'stream_active'];

        streamEvents.forEach(eventType => {
            window.socketConnection.on(eventType, (data) => {
                if (data.lot_id === this.lotId) {
                    console.log(`ViewpointSelector: ${eventType} for viewpoint`, data.viewpoint_id);

                    // Update viewpoint status immediately based on event
                    const viewpoint = this.viewpoints.find(v => v.id === data.viewpoint_id);
                    if (viewpoint && viewpoint.stream_url) {
                        switch (eventType) {
                            case 'stream_connecting':
                                viewpoint.stream_status = 'connecting';
                                break;
                            case 'stream_started':
                            case 'stream_active':
                                viewpoint.stream_status = 'active';
                                viewpoint.last_frame_time = Date.now() / 1000;
                                break;
                            case 'stream_stopped':
                                viewpoint.stream_status = 'inactive';
                                break;
                            case 'stream_error':
                                viewpoint.stream_status = 'error';
                                viewpoint.stream_error_message = data.error || 'Stream error';
                                break;
                        }

                        // Re-render to show updated status
                        this.renderViewpoints();
                    }
                }
            });
        });

        // Also listen for image updates - if we're getting image updates, the stream is active
        window.socketConnection.on('image_updated', (data) => {
            if (data.lot_id === this.lotId && data.viewpoint_id) {
                console.log(`ViewpointSelector: Image update received for viewpoint ${data.viewpoint_id} - stream is active`);

                // Immediately update the viewpoint status to active in our local data
                const viewpoint = this.viewpoints.find(v => v.id === data.viewpoint_id);
                if (viewpoint && viewpoint.stream_url) {
                    viewpoint.stream_status = 'active';
                    viewpoint.last_frame_time = Date.now() / 1000;

                    // Re-render to show updated status
                    this.renderViewpoints();
                }
            }
        });

        // Initial load of viewpoint data
        this.refreshViewpointData();
    }

    refreshViewpointData() {
        // Refresh viewpoint data to get updated persistent stream status
        if (!this.lotId) return;

        fetch(`/api/lots/${this.lotId}/viewpoints/`)
            .then(response => response.json())
            .then(data => {
                if (data.viewpoints) {
                    // Update viewpoints with fresh data including stream status
                    this.viewpoints = data.viewpoints;
                    this.renderViewpoints(); // Re-render with updated status
                }
            })
            .catch(error => {
                console.error('ViewpointSelector: Failed to refresh viewpoint data:', error);
            });
    }

    updateViewpointImagePath(viewpointId, newImagePath) {
        const viewpoint = this.viewpoints.find(v => v.id === viewpointId);
        if (viewpoint) {
            console.log(`ViewpointSelector: Updating image path for viewpoint ${viewpoint.name} from ${viewpoint.image_path} to ${newImagePath}`);
            viewpoint.image_path = newImagePath;

            // If this is the currently selected viewpoint, re-render to update the display
            if (this.currentViewpoint === viewpointId) {
                console.log('ViewpointSelector: Updated viewpoint is currently selected, re-rendering');
                this.updateSelection();
            }
        }
    }

    async loadViewpoints() {
        if (!this.lotId) {
            console.warn('ViewpointSelector: No lot ID provided');
            return;
        }

        console.log(`ViewpointSelector: Loading viewpoints for lot ${this.lotId}`);

        try {
            const response = await fetch(`/api/lots/${this.lotId}/viewpoints`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();
            this.viewpoints = data.viewpoints || [];
            console.log(`ViewpointSelector: Loaded ${this.viewpoints.length} viewpoints:`, this.viewpoints);

            // Load slot stats after viewpoints are loaded
            await this.loadSlotStats();
            this.renderViewpoints();

            // Dispatch viewpoints-loaded event for external listeners
            this.dispatchEvent(new CustomEvent('viewpoints-loaded', {
                detail: {
                    viewpoints: this.viewpoints,
                    lotId: this.lotId
                },
                bubbles: true
            }));

            // Set current viewpoint if not already set
            if (!this.currentViewpoint && this.viewpoints.length > 0) {
                const defaultViewpoint = this.viewpoints.find(v => v.is_default) || this.viewpoints[0];
                console.log('ViewpointSelector: Setting default viewpoint:', defaultViewpoint);
                this.selectViewpoint(defaultViewpoint.id);
            }
        } catch (error) {
            console.error('ViewpointSelector: Failed to load viewpoints:', error);
            this.renderError('Failed to load viewpoints');
        }
    }

    async loadSlotStats() {
        if (!this.lotId) {
            console.warn('ViewpointSelector: No lot ID provided for slot stats');
            return;
        }

        console.log(`ViewpointSelector: Loading slot stats for lot ${this.lotId}`);

        try {
            const response = await fetch(`/api/lots/${this.lotId}/slots`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            this.allSlots = await response.json();
            console.log(`ViewpointSelector: Loaded ${this.allSlots.length} slots for stats`);

            // Calculate stats for each viewpoint
            this.calculateViewpointStats();

        } catch (error) {
            console.error('ViewpointSelector: Failed to load slot stats:', error);
        }
    }

    calculateViewpointStats() {
        this.viewpointStats.clear();

        // Calculate overall stats
        const totalSlots = this.allSlots.length;
        const totalOccupied = this.allSlots.filter(slot => slot.is_occupied).length;
        const totalAvailable = totalSlots - totalOccupied;

        this.viewpointStats.set('overall', {
            total: totalSlots,
            available: totalAvailable,
            occupied: totalOccupied
        });

        // Calculate stats for each viewpoint
        this.viewpoints.forEach(viewpoint => {
            // Filter slots for this viewpoint (same logic as zone editor)
            const viewpointSlots = this.allSlots.filter(slot =>
                slot.viewpoint_id === viewpoint.id ||
                !slot.viewpoint_id ||
                slot.viewpoint_id === null ||
                slot.viewpoint_id === undefined
            );

            const occupied = viewpointSlots.filter(slot => slot.is_occupied).length;
            const available = viewpointSlots.length - occupied;

            this.viewpointStats.set(viewpoint.id, {
                total: viewpointSlots.length,
                available: available,
                occupied: occupied
            });
        });

        console.log('ViewpointSelector: Calculated stats:', Object.fromEntries(this.viewpointStats));

        // Dispatch event with overall stats for main stats card
        this.dispatchEvent(new CustomEvent('stats-updated', {
            detail: {
                overall: this.viewpointStats.get('overall'),
                viewpoints: Object.fromEntries(this.viewpointStats)
            },
            bubbles: true
        }));
    }

    renderViewpoints() {
        console.log('ViewpointSelector: renderViewpoints called with', this.viewpoints.length, 'viewpoints');

        const dropdown = this.shadowRoot.querySelector('.dropdown');
        if (!dropdown) {
            console.error('ViewpointSelector: dropdown element not found in shadow DOM');
            return;
        }

        if (this.viewpoints.length === 0) {
            console.log('ViewpointSelector: No viewpoints, showing empty state');
            dropdown.innerHTML = `
                <div class="error">No viewpoints available</div>
                <div class="add-viewpoint">+ Add First Viewpoint</div>
            `;
        } else {
            console.log('ViewpointSelector: Rendering', this.viewpoints.length, 'viewpoints');

            const optionsHtml = this.viewpoints.map((viewpoint, index) => {
                console.log(`ViewpointSelector: Rendering viewpoint ${index + 1}:`, viewpoint.name);

                // Get stats for this viewpoint
                const stats = this.viewpointStats.get(viewpoint.id) || { total: 0, available: 0, occupied: 0 };

                // Determine viewpoint type and status
                const isStream = viewpoint.stream_url && viewpoint.stream_url.trim() !== '';
                const streamStatus = this.getStreamStatus(viewpoint);

                return `
                <div class="viewpoint-option ${viewpoint.id === this.currentViewpoint ? 'selected' : ''}"
                     data-viewpoint-id="${viewpoint.id}">
                    <div class="viewpoint-info">
                        <div class="viewpoint-icon ${viewpoint.is_default ? 'default' : ''}">
                            ${viewpoint.is_default ? '★' : '📹'}
                        </div>
                        <div>
                            <div class="viewpoint-name">
                                ${viewpoint.name}
                                <div class="viewpoint-badges">
                                    <span class="type-badge ${isStream ? 'stream' : 'photo'}" title="${isStream ? 'Live Stream' : 'Static Photo'}">
                                        ${isStream ? '📡 LIVE' : '📷 PHOTO'}
                                    </span>
                                    ${isStream ? `<span class="stream-status ${streamStatus.class}" title="${streamStatus.title}" data-status="${viewpoint.stream_status}">●</span>` : ''}
                                </div>
                            </div>
                            <div class="viewpoint-meta">
                                <div class="viewpoint-stats">
                                    <div class="stat-item available">
                                        <span class="stat-icon">🅿️</span>
                                        <span>${stats.available}</span>
                                    </div>
                                    <div class="stat-item occupied">
                                        <span class="stat-icon">🚗</span>
                                        <span>${stats.occupied}</span>
                                    </div>
                                </div>
                                ${viewpoint.is_default ? '<span class="default-badge">Default</span>' : ''}
                                ${isStream ? `<div class="stream-info">FPS: ${viewpoint.capture_fps || 1.0}</div>` : ''}
                            </div>
                        </div>
                    </div>
                    <div class="viewpoint-actions">
                        <button class="action-btn edit-btn" data-action="edit" title="Edit">✏️</button>
                        ${!viewpoint.is_default ? '<button class="action-btn delete-btn" data-action="delete" title="Delete">🗑️</button>' : ''}
                    </div>
                </div>
            `;
            }).join('');

            const finalHtml = `
                ${optionsHtml}
                <div class="add-viewpoint">+ Add New Viewpoint</div>
            `;

            console.log('ViewpointSelector: Setting dropdown innerHTML, length:', finalHtml.length);
            dropdown.innerHTML = finalHtml;

            // Verify the options were added
            const addedOptions = dropdown.querySelectorAll('.viewpoint-option');
            console.log('ViewpointSelector: Added', addedOptions.length, 'viewpoint options to dropdown');
        }

        this.setupDropdownEventListeners();
        this.updateSelection();
    }

    getStreamStatus(viewpoint) {
        // Check if it's a stream viewpoint
        if (!viewpoint.stream_url || viewpoint.stream_url.trim() === '') {
            return { class: '', title: '' };
        }

        // Use ephemeral stream status (not persisted, updated via WebSocket events)
        const status = viewpoint.stream_status || 'inactive';
        const errorMessage = viewpoint.stream_error_message;
        const lastFrameTime = viewpoint.last_frame_time;

        // Debug logging to see what status we're getting
        console.log(`ViewpointSelector: Stream status for ${viewpoint.name}:`, {
            status,
            stream_status: viewpoint.stream_status,
            errorMessage,
            lastFrameTime
        });

        // Calculate time since last frame for additional context
        let timeSinceLastFrame = '';
        if (lastFrameTime) {
            const secondsAgo = Math.floor((Date.now() / 1000) - lastFrameTime);
            if (secondsAgo < 60) {
                timeSinceLastFrame = ` (${secondsAgo}s ago)`;
            } else if (secondsAgo < 3600) {
                timeSinceLastFrame = ` (${Math.floor(secondsAgo / 60)}m ago)`;
            } else {
                timeSinceLastFrame = ` (${Math.floor(secondsAgo / 3600)}h ago)`;
            }
        }

        switch (status) {
            case 'active':
                return {
                    class: 'status-active',
                    title: `Stream is active and capturing frames${timeSinceLastFrame}`
                };
            case 'connecting':
                return {
                    class: 'status-connecting',
                    title: 'Stream is connecting...'
                };
            case 'error':
                return {
                    class: 'status-error',
                    title: `Stream connection failed${errorMessage ? ': ' + errorMessage : ''}`
                };
            case 'inactive':
            default:
                return {
                    class: 'status-inactive',
                    title: 'Stream is not active'
                };
        }
    }

    setupDropdownEventListeners() {
        const dropdown = this.shadowRoot.querySelector('.dropdown');

        // Viewpoint selection
        dropdown.querySelectorAll('.viewpoint-option').forEach(option => {
            option.addEventListener('click', (e) => {
                if (!e.target.closest('.action-btn')) {
                    const viewpointId = option.dataset.viewpointId;
                    this.selectViewpoint(viewpointId);
                }
            });
        });

        // Action buttons
        dropdown.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const action = btn.dataset.action;
                const viewpointId = btn.closest('.viewpoint-option').dataset.viewpointId;
                this.handleAction(action, viewpointId);
            });
        });

        // Add viewpoint
        const addBtn = dropdown.querySelector('.add-viewpoint');
        if (addBtn) {
            addBtn.addEventListener('click', () => {
                this.handleAction('add');
            });
        }
    }

    selectViewpoint(viewpointId) {
        this.currentViewpoint = viewpointId;
        this.updateSelection();
        this.closeDropdown();

        // Dispatch custom event
        this.dispatchEvent(new CustomEvent('viewpoint-changed', {
            detail: {
                viewpointId: viewpointId,
                viewpoint: this.viewpoints.find(v => v.id === viewpointId)
            },
            bubbles: true
        }));
    }

    updateSelection() {
        if (!this.shadowRoot) {
            console.warn('ViewpointSelector: updateSelection called before shadow DOM is ready');
            return;
        }

        const button = this.shadowRoot.querySelector('.selector-button');
        if (!button) {
            console.warn('ViewpointSelector: selector-button not found in shadow DOM');
            return;
        }

        const currentViewpointEl = button.querySelector('.current-viewpoint');
        if (!currentViewpointEl) {
            console.warn('ViewpointSelector: current-viewpoint element not found');
            return;
        }

        if (this.currentViewpoint) {
            const viewpoint = this.viewpoints.find(v => v.id === this.currentViewpoint);
            if (viewpoint) {
                const icon = currentViewpointEl.querySelector('.viewpoint-icon');
                const name = currentViewpointEl.querySelector('.viewpoint-name');

                if (icon && name) {
                    icon.textContent = viewpoint.is_default ? '★' : '📹';
                    icon.className = `viewpoint-icon ${viewpoint.is_default ? 'default' : ''}`;
                    name.textContent = viewpoint.name;
                }
            }
        }

        // Update selected state in dropdown
        this.shadowRoot.querySelectorAll('.viewpoint-option').forEach(option => {
            option.classList.toggle('selected', option.dataset.viewpointId === this.currentViewpoint);
        });
    }

    toggleDropdown() {
        console.log('ViewpointSelector: toggleDropdown called');

        const button = this.shadowRoot.querySelector('.selector-button');
        const dropdown = this.shadowRoot.querySelector('.dropdown');

        if (!button || !dropdown) {
            console.error('ViewpointSelector: button or dropdown not found in toggleDropdown');
            return;
        }

        const isOpen = dropdown.classList.contains('open');
        console.log('ViewpointSelector: dropdown is currently', isOpen ? 'open' : 'closed');

        if (isOpen) {
            this.closeDropdown();
        } else {
            console.log('ViewpointSelector: opening dropdown');
            button.classList.add('open');
            dropdown.classList.add('open');

            // Position the dropdown relative to the button with improved positioning
            // that accounts for potential parent transforms
            const buttonRect = button.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            let top = buttonRect.bottom + 2;
            let left = buttonRect.left;

            // Ensure dropdown doesn't go off-screen
            const dropdownWidth = Math.max(buttonRect.width, 250);
            if (left + dropdownWidth > viewportWidth) {
                left = viewportWidth - dropdownWidth - 10;
            }

            if (top + 300 > viewportHeight) {
                // Position above the button if there's not enough space below
                top = buttonRect.top - 300 - 2;
                if (top < 0) {
                    // If still not enough space, position at top of viewport
                    top = 10;
                }
            }

            dropdown.style.top = `${top}px`;
            dropdown.style.left = `${left}px`;
            dropdown.style.width = `${dropdownWidth}px`;
            dropdown.style.position = 'fixed';
            dropdown.style.zIndex = '99999'; // Ensure it's above everything

            console.log('ViewpointSelector: positioned dropdown at', {
                top: dropdown.style.top,
                left: dropdown.style.left,
                width: dropdown.style.width
            });

            // Log dropdown content and styles
            const options = dropdown.querySelectorAll('.viewpoint-option');
            console.log('ViewpointSelector: dropdown now contains', options.length, 'options');

            // Debug dropdown visibility
            setTimeout(() => {
                const computedStyle = window.getComputedStyle(dropdown);
                console.log('ViewpointSelector: dropdown computed styles:', {
                    display: computedStyle.display,
                    visibility: computedStyle.visibility,
                    opacity: computedStyle.opacity,
                    height: computedStyle.height,
                    zIndex: computedStyle.zIndex,
                    position: computedStyle.position,
                    top: computedStyle.top,
                    left: computedStyle.left,
                    width: computedStyle.width
                });

                console.log('ViewpointSelector: dropdown bounding rect:', dropdown.getBoundingClientRect());

                // Log each option
                options.forEach((option, index) => {
                    const name = option.querySelector('.viewpoint-name')?.textContent || 'Unknown';
                    const rect = option.getBoundingClientRect();
                    console.log(`ViewpointSelector: Option ${index + 1} "${name}":`, {
                        visible: rect.width > 0 && rect.height > 0,
                        rect: rect
                    });
                });
            }, 100);
        }
    }

    closeDropdown() {
        console.log('ViewpointSelector: closeDropdown called');
        const button = this.shadowRoot.querySelector('.selector-button');
        const dropdown = this.shadowRoot.querySelector('.dropdown');

        if (button && dropdown) {
            button.classList.remove('open');
            dropdown.classList.remove('open');
        }

        // Clear positioning styles
        dropdown.style.top = '';
        dropdown.style.left = '';
        dropdown.style.width = '';
    }

    handleAction(action, viewpointId = null) {
        this.closeDropdown();

        // Dispatch action event
        this.dispatchEvent(new CustomEvent('viewpoint-action', {
            detail: {
                action: action,
                viewpointId: viewpointId,
                viewpoint: viewpointId ? this.viewpoints.find(v => v.id === viewpointId) : null
            },
            bubbles: true
        }));
    }

    renderError(message) {
        const dropdown = this.shadowRoot.querySelector('.dropdown');
        dropdown.innerHTML = `<div class="error">${message}</div>`;
    }

    // Public API methods
    refresh() {
        this.loadViewpoints();
    }

    refreshStats() {
        this.loadSlotStats();
    }

    setCurrentViewpoint(viewpointId) {
        this.setAttribute('current-viewpoint', viewpointId);
    }
}

// Register the custom element
customElements.define('viewpoint-selector', ViewpointSelector);
