# FastAPI core dependencies
fastapi
uvicorn[standard]
python-socketio
jinja2
python-multipart
aiofiles
httpx  # Required for FastAPI TestClient

# Business logic dependencies (unchanged)
behave
selenium
pillow
requests
ultralytics
supervision
sqlalchemy
pyodbc
sqlalchemy[mssql] # Ensure mssql extras are installed for SQLAlchemy
alembic

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0