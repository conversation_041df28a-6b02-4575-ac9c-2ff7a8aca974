"""
Tests for the grouped slot statistics API endpoints
"""
import pytest
import json
from unittest.mock import Mock, patch
import uuid
from fastapi.testclient import TestClient

from app.src.config.settings import get_settings
from app.src.models.parking import ParkingLot, ParkingSlot


class TestGroupedStatsAPI:
    """Tests for the grouped slot statistics API endpoint"""

    @pytest.fixture
    def app(self):
        """Create a test FastAPI app"""
        from app.main import create_app
        return create_app()

    @pytest.fixture
    def client(self, app):
        """Create a test client"""
        return TestClient(app)

    @pytest.fixture
    def sample_lot_with_grouped_slots(self):
        """Create a sample parking lot with grouped slots for testing"""
        lot = ParkingLot(id=str(uuid.uuid4()), name="Test Lot")
        
        # Create slots with duplicate names across different viewpoints
        slots = [
            ParkingSlot(id="slot1", slot_name="A1", is_occupied=True, viewpoint_id="vp1"),
            ParkingSlot(id="slot2", slot_name="A1", is_occupied=False, viewpoint_id="vp2"),  # Same name, different viewpoint
            ParkingSlot(id="slot3", slot_name="A2", is_occupied=False, viewpoint_id="vp1"),
            ParkingSlot(id="slot4", slot_name="A3", is_occupied=True, viewpoint_id="vp1"),
            ParkingSlot(id="slot5", slot_name="A3", is_occupied=False, viewpoint_id="vp2"),  # Same name, different viewpoint
        ]
        
        for slot in slots:
            lot.slots.append(slot)
        
        return lot

    @patch('app.src.utils.storage.StorageService.get_parking_lot')
    def test_get_grouped_stats_success(self, mock_get_lot, client, sample_lot_with_grouped_slots):
        """Test successful retrieval of grouped slot statistics"""
        # Arrange
        lot_id = sample_lot_with_grouped_slots.id
        mock_get_lot.return_value = sample_lot_with_grouped_slots
        
        # Act
        response = client.get(f'/api/lots/{lot_id}/grouped-stats')
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        
        assert data['total_groups'] == 3
        assert data['occupied_groups'] == 2
        assert data['available_groups'] == 1
        assert data['occupancy_rate'] == pytest.approx(66.7, abs=0.1)
        
        # Check group details structure
        assert 'group_details' in data
        assert 'A1' in data['group_details']
        assert 'A2' in data['group_details']
        assert 'A3' in data['group_details']
        
        # Verify group details content
        assert data['group_details']['A1']['is_occupied'] is True
        assert data['group_details']['A1']['instances'] == 2
        assert data['group_details']['A2']['is_occupied'] is False
        assert data['group_details']['A2']['instances'] == 1
        assert data['group_details']['A3']['is_occupied'] is True
        assert data['group_details']['A3']['instances'] == 2

    @patch('app.src.utils.storage.StorageService.get_parking_lot')
    def test_get_grouped_stats_lot_not_found(self, mock_get_lot, client):
        """Test grouped stats API when parking lot is not found"""
        # Arrange
        lot_id = str(uuid.uuid4())
        mock_get_lot.return_value = None

        # Act
        response = client.get(f'/api/lots/{lot_id}/grouped-stats')

        # Assert
        assert response.status_code == 404
        data = response.json()
        assert 'detail' in data
        assert data['detail'] == "Parking lot not found"

    @patch('app.src.utils.storage.StorageService.get_parking_lot')
    def test_get_grouped_stats_empty_lot(self, mock_get_lot, client):
        """Test grouped stats API with an empty parking lot"""
        # Arrange
        empty_lot = ParkingLot(id=str(uuid.uuid4()), name="Empty Lot")
        mock_get_lot.return_value = empty_lot

        # Act
        response = client.get(f'/api/lots/{empty_lot.id}/grouped-stats')

        # Assert
        assert response.status_code == 200
        data = response.json()
        
        assert data['total_groups'] == 0
        assert data['occupied_groups'] == 0
        assert data['available_groups'] == 0
        assert data['occupancy_rate'] == 0.0
        assert data['group_details'] == {}

    @patch('app.src.utils.storage.StorageService.get_parking_lot')
    def test_get_grouped_stats_all_occupied(self, mock_get_lot, client):
        """Test grouped stats API when all slot groups are occupied"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Full Lot")
        slots = [
            ParkingSlot(id="slot1", slot_name="A1", is_occupied=True, viewpoint_id="vp1"),
            ParkingSlot(id="slot2", slot_name="A1", is_occupied=True, viewpoint_id="vp2"),
            ParkingSlot(id="slot3", slot_name="A2", is_occupied=True, viewpoint_id="vp1"),
        ]

        for slot in slots:
            lot.slots.append(slot)

        mock_get_lot.return_value = lot

        # Act
        response = client.get(f'/api/lots/{lot.id}/grouped-stats')

        # Assert
        assert response.status_code == 200
        data = response.json()
        
        assert data['total_groups'] == 2
        assert data['occupied_groups'] == 2
        assert data['available_groups'] == 0
        assert data['occupancy_rate'] == 100.0

    @patch('app.src.utils.storage.StorageService.get_parking_lot')
    def test_get_grouped_stats_all_free(self, mock_get_lot, client):
        """Test grouped stats API when all slot groups are free"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Empty Lot")
        slots = [
            ParkingSlot(id="slot1", slot_name="A1", is_occupied=False, viewpoint_id="vp1"),
            ParkingSlot(id="slot2", slot_name="A1", is_occupied=False, viewpoint_id="vp2"),
            ParkingSlot(id="slot3", slot_name="A2", is_occupied=False, viewpoint_id="vp1"),
        ]

        for slot in slots:
            lot.slots.append(slot)

        mock_get_lot.return_value = lot

        # Act
        response = client.get(f'/api/lots/{lot.id}/grouped-stats')

        # Assert
        assert response.status_code == 200
        data = response.json()
        
        assert data['total_groups'] == 2
        assert data['occupied_groups'] == 0
        assert data['available_groups'] == 2
        assert data['occupancy_rate'] == 0.0

    @patch('app.src.utils.storage.StorageService.get_parking_lot')
    def test_get_grouped_stats_consistency(self, mock_get_lot, client, sample_lot_with_grouped_slots):
        """Test that grouped stats API returns consistent results across multiple calls"""
        # Arrange
        lot_id = sample_lot_with_grouped_slots.id
        mock_get_lot.return_value = sample_lot_with_grouped_slots

        # Act - Make multiple calls
        responses = []
        for _ in range(3):
            response = client.get(f'/api/lots/{lot_id}/grouped-stats')
            responses.append(response.json())

        # Assert - All responses should be identical
        first_response = responses[0]
        for response in responses[1:]:
            assert response == first_response

    @patch('app.src.utils.storage.StorageService.get_parking_lot')
    def test_get_grouped_stats_exception_handling(self, mock_get_lot, client):
        """Test grouped stats API exception handling"""
        # Arrange
        lot_id = str(uuid.uuid4())
        mock_get_lot.side_effect = Exception("Database error")

        # Act
        response = client.get(f'/api/lots/{lot_id}/grouped-stats')

        # Assert
        assert response.status_code == 500
        # The response might be HTML error page or JSON depending on FastAPI configuration
        # Just verify it's a 500 error for now
        assert response.status_code == 500

    @patch('app.src.utils.storage.StorageService.get_parking_lot')
    def test_get_grouped_stats_viewpoint_independence(self, mock_get_lot, client):
        """Test that grouped stats are independent of viewpoint selection"""
        # Arrange
        lot = ParkingLot(id=str(uuid.uuid4()), name="Multi-Viewpoint Lot")
        
        # Create slots where the same slot name appears in multiple viewpoints
        slots = [
            ParkingSlot(id="slot1", slot_name="SHARED", is_occupied=True, viewpoint_id="front"),
            ParkingSlot(id="slot2", slot_name="SHARED", is_occupied=False, viewpoint_id="back"),
            ParkingSlot(id="slot3", slot_name="SHARED", is_occupied=False, viewpoint_id="side"),
            ParkingSlot(id="slot4", slot_name="UNIQUE", is_occupied=False, viewpoint_id="front"),
        ]
        
        for slot in slots:
            lot.slots.append(slot)
        
        mock_get_lot.return_value = lot
        
        # Act
        response = client.get(f'/api/lots/{lot.id}/grouped-stats')
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        
        # Should have 2 groups: SHARED and UNIQUE
        assert data['total_groups'] == 2
        assert data['occupied_groups'] == 1  # SHARED group is occupied (any instance occupied)
        assert data['available_groups'] == 1  # UNIQUE group is free
        
        # Verify SHARED group details
        shared_details = data['group_details']['SHARED']
        assert shared_details['instances'] == 3
        assert shared_details['is_occupied'] is True  # Any instance occupied makes group occupied
        assert set(shared_details['viewpoints']) == {"front", "back", "side"}
        
        # Verify UNIQUE group details
        unique_details = data['group_details']['UNIQUE']
        assert unique_details['instances'] == 1
        assert unique_details['is_occupied'] is False
        assert unique_details['viewpoints'] == ["front"]
