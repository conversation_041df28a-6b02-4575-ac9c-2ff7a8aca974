from dataclasses import dataclass
from typing import List, Dict, Any


@dataclass
class Point:
    # When updating Point, update PolygonPointORM accordingly
    x: float
    y: float

    def to_dict(self) -> dict:
        return {"x": self.x, "y": self.y}

    @classmethod
    def from_dict(cls, data: dict) -> 'Point':
        return cls(x=float(data["x"]), y=float(data["y"]))


@dataclass
class Polygon:
    # When updating Polygon, update DetectionZoneORM and PolygonPointORM accordingly
    # List of points defining the polygon defined as percentages from the top-left corner
    points: List[Point]

    def to_dict(self) -> dict:
        return {"points": [p.to_dict() for p in self.points]}

    @classmethod
    def from_dict(cls, data: dict) -> 'Polygon':
        points = [Point.from_dict(p) for p in data["points"]]
        return cls(points=points)