/**
 * ParkingSlot Web Component
 * 
 * A reusable component for rendering individual parking slots with proper styling,
 * positioning, and state management.
 * 
 * Usage:
 * <parking-slot 
 *   slot-id="123" 
 *   slot-name="A1" 
 *   is-occupied="false"
 *   points="[{x:10,y:20},{x:30,y:20},{x:30,y:40},{x:10,y:40}]">
 * </parking-slot>
 */
class ParkingSlot extends HTMLElement {
    constructor() {
        super();
        this.attachShadow({ mode: 'open' });
        
        // Component state
        this.slotId = null;
        this.slotName = null;
        this.isOccupied = false;
        this.points = [];
        this.centerX = 0;
        this.centerY = 0;
    }

    static get observedAttributes() {
        return ['slot-id', 'slot-name', 'is-occupied', 'points'];
    }

    attributeChangedCallback(name, oldValue, newValue) {
        if (oldValue !== newValue) {
            switch (name) {
                case 'slot-id':
                    this.slotId = newValue;
                    break;
                case 'slot-name':
                    this.slotName = newValue;
                    break;
                case 'is-occupied':
                    this.isOccupied = newValue === 'true';
                    break;
                case 'points':
                    try {
                        this.points = JSON.parse(newValue);
                        this.calculateCenter();
                    } catch (e) {
                        console.error('ParkingSlot: Invalid points JSON:', newValue);
                        this.points = [];
                    }
                    break;
            }
            this.render();
        }
    }

    connectedCallback() {
        this.render();
    }

    calculateCenter() {
        if (this.points.length === 0) {
            this.centerX = 0;
            this.centerY = 0;
            return;
        }

        this.centerX = this.points.reduce((sum, p) => sum + p.x, 0) / this.points.length;
        this.centerY = this.points.reduce((sum, p) => sum + p.y, 0) / this.points.length;
    }

    render() {
        if (!this.slotId || this.points.length === 0) {
            this.shadowRoot.innerHTML = '';
            return;
        }

        // Create clip-path from points
        const clipPath = `polygon(${this.points.map(p => `${p.x}% ${p.y}%`).join(', ')})`;
        
        this.shadowRoot.innerHTML = `
            <style>
                :host {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    pointer-events: none;
                    z-index: 10;
                }

                .slot-polygon {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    pointer-events: none;
                    clip-path: ${clipPath};
                    -webkit-clip-path: ${clipPath};
                    transition: background-color 0.3s ease, border-color 0.3s ease;
                }

                .slot-polygon.free {
                    background-color: rgba(40, 167, 69, 0.3);
                    border: 2px solid rgba(40, 167, 69, 0.8);
                }

                .slot-polygon.occupied {
                    background-color: rgba(220, 53, 69, 0.3);
                    border: 2px solid rgba(220, 53, 69, 0.8);
                }

                .slot-label {
                    position: absolute;
                    left: ${this.centerX}%;
                    top: ${this.centerY}%;
                    transform: translate(-50%, -50%);
                    background: rgba(0, 0, 0, 0.7);
                    color: white;
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-size: 12px;
                    font-weight: bold;
                    white-space: nowrap;
                    pointer-events: none;
                    z-index: 11;
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                }

                /* Animation for state changes */
                .slot-polygon.changed {
                    animation: slotChange 0.5s ease-in-out;
                }

                @keyframes slotChange {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                    100% { transform: scale(1); }
                }
            </style>
            
            <div class="slot-polygon ${this.isOccupied ? 'occupied' : 'free'}" 
                 data-slot-id="${this.slotId}">
            </div>
            
            <div class="slot-label" data-slot-id="${this.slotId}">
                ${this.slotName || this.slotId}
            </div>
        `;
    }

    // Public API methods
    updateOccupancy(isOccupied, animate = true) {
        if (this.isOccupied !== isOccupied) {
            this.isOccupied = isOccupied;
            this.setAttribute('is-occupied', isOccupied.toString());
            
            if (animate) {
                const polygon = this.shadowRoot.querySelector('.slot-polygon');
                if (polygon) {
                    polygon.classList.add('changed');
                    setTimeout(() => polygon.classList.remove('changed'), 500);
                }
            }
        }
    }

    updatePoints(points) {
        this.points = points;
        this.calculateCenter();
        this.setAttribute('points', JSON.stringify(points));
    }

    updateName(name) {
        this.slotName = name;
        this.setAttribute('slot-name', name);
    }

    // Getters
    get occupied() {
        return this.isOccupied;
    }

    get name() {
        return this.slotName;
    }

    get id() {
        return this.slotId;
    }
}

// Register the custom element
customElements.define('parking-slot', ParkingSlot);
