"""
FastAPI router for dashboard routes
Migrated from routes/dashboard.py
"""

from fastapi import APIRouter, Request, HTTPException, Depends, Form
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from typing import List

# Import business logic (unchanged from Flask)
from src.utils.storage import StorageService
from src.utils.logger import setup_logger
from src.dependencies import get_templates, get_base_template_context, get_socketio

# Initialize logger
LOGGER = setup_logger(__name__)

# Create router
router = APIRouter(prefix="/dashboard", tags=["dashboard"])

@router.get("/", response_class=HTMLResponse)
async def dashboard_home(
    request: Request,
    templates: Jinja2Templates = Depends(get_templates)
):
    """Dashboard home showing all parking lots."""
    try:
        parking_lots = StorageService.get_all_parking_lots()
        context = get_base_template_context(request)
        context.update({
            "parking_lots": parking_lots
        })
        return templates.TemplateResponse('dashboard.html', context)
    except Exception as e:
        LOGGER.error(f"Error loading dashboard: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/{lot_id}", response_class=HTMLResponse)
async def lot_details(
    request: Request,
    lot_id: str,
    templates: Jinja2Templates = Depends(get_templates),
    socketio = Depends(get_socketio)
):
    """Show detailed view of a specific parking lot with occupancy status."""
    try:
        lot = StorageService.get_parking_lot(lot_id)
        if not lot:
            LOGGER.warning(f"Parking lot with ID {lot_id} not found")
            # Emit notification via SocketIO instead of flash
            await socketio.emit('notification', {
                'type': 'error',
                'message': 'Parking lot not found'
            }, namespace='/dashboard')
            return RedirectResponse(url="/dashboard/", status_code=302)

        # Get detection engine configuration
        from src.services.detection_manager import DetectionManager
        from src.models.detection import DetectionEngineConfig

        engine_config = DetectionManager.get_engine_config(lot_id)
        if not engine_config:
            engine_config = DetectionEngineConfig(engine_type="mock")

        # Get viewpoint information
        viewpoints = lot.get_active_viewpoints()
        default_viewpoint = lot.get_default_viewpoint()

        # Calculate aggregated stats for all active viewpoints
        aggregated_stats = lot.calculate_aggregated_occupancy_stats()

        # Calculate grouped stats (viewport-independent)
        grouped_stats = lot.get_grouped_slot_stats()

        context = get_base_template_context(request)
        context.update({
            "lot": lot,
            "lot_id": lot_id,
            "engine_config": engine_config,
            "available_engines": DetectionManager.get_available_engine_types(),
            "viewpoints": viewpoints,
            "default_viewpoint": default_viewpoint,
            "aggregated_stats": aggregated_stats,
            "grouped_stats": grouped_stats
        })

        return templates.TemplateResponse('lot_dashboard.html', context)

    except Exception as e:
        LOGGER.error(f"Error loading lot details for {lot_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/{lot_id}/update")
async def manual_update(
    lot_id: str,
    socketio = Depends(get_socketio)
):
    """Manually trigger a detection update for a parking lot using event sourcing."""
    try:
        lot = StorageService.get_parking_lot(lot_id)
        if not lot:
            LOGGER.warning(f"Parking lot with ID {lot_id} not found for manual update")
            return JSONResponse(
                content={"error": "Parking lot not found"},
                status_code=404
            )

        # Emit detection started event
        await socketio.emit('detection_status', {
            'lot_id': lot_id,
            'status': 'started'
        }, namespace='/dashboard')

        try:
            # Run detection using event-sourced method with batch processing for multiple viewpoints
            from src.services.detection_manager import DetectionManager

            # Check if lot has multiple active viewpoints to use batch detection
            active_viewpoints = lot.get_active_viewpoints()
            if len(active_viewpoints) > 1:
                LOGGER.info(f"Using batch detection for lot {lot_id} with {len(active_viewpoints)} viewpoints")
                batch_results = DetectionManager.run_batch_detection(lot_id)
                # Count total updates across all viewpoints
                total_updates = sum(len(results) for results in batch_results.values()) if batch_results else 0
                updates = total_updates
            else:
                LOGGER.info(f"Using single detection for lot {lot_id} with {len(active_viewpoints)} viewpoint(s)")
                updates = DetectionManager.run_single_detection(lot_id)

            # Emit detection completed event
            await socketio.emit('detection_status', {
                'lot_id': lot_id,
                'status': 'completed',
                'updates': updates if isinstance(updates, int) else len(updates) if updates else 0
            }, namespace='/dashboard')

            LOGGER.info(f"Manual detection completed for lot {lot_id} with {updates} updates")
            return JSONResponse(content={
                "success": True,
                "updates": updates if isinstance(updates, int) else len(updates) if updates else 0
            })

        except Exception as e:
            LOGGER.error(f"Error in manual detection for lot {lot_id}: {str(e)}")

            # Emit detection error event
            await socketio.emit('detection_status', {
                'lot_id': lot_id,
                'status': 'error',
                'error': str(e)
            }, namespace='/dashboard')

            return JSONResponse(
                content={"error": str(e)},
                status_code=500
            )

    except Exception as e:
        LOGGER.error(f"Error in manual update endpoint for lot {lot_id}: {str(e)}")
        return JSONResponse(
            content={"error": "Internal server error"},
            status_code=500
        )

@router.get("/{lot_id}/configure", response_class=HTMLResponse)
async def configure_engine_get(
    request: Request,
    lot_id: str,
    templates: Jinja2Templates = Depends(get_templates),
    socketio = Depends(get_socketio)
):
    """Show detection engine configuration form."""
    try:
        lot = StorageService.get_parking_lot(lot_id)
        if not lot:
            LOGGER.warning(f"Parking lot with ID {lot_id} not found for configuration")
            await socketio.emit('notification', {
                'type': 'error',
                'message': 'Parking lot not found'
            }, namespace='/dashboard')
            return RedirectResponse(url="/dashboard/", status_code=302)

        # Get detection engine configuration
        from src.services.detection_manager import DetectionManager
        from src.models.detection import DetectionEngineConfig

        engine_config = DetectionManager.get_engine_config(lot_id)
        if not engine_config:
            engine_config = DetectionEngineConfig.for_yolo()

        # Get available engine types
        available_engines = DetectionManager.get_available_engine_types()

        # Get image dimensions for resolution slider
        image_width = 3840  # Default for 4K streams

        # Try to get dimensions from active stream viewpoints first
        active_viewpoints = lot.get_active_viewpoints()
        for viewpoint in active_viewpoints:
            if viewpoint.stream_url and viewpoint.image_path:
                try:
                    from PIL import Image
                    import os
                    image_path = os.path.join("static", "uploads", viewpoint.image_path)
                    if os.path.exists(image_path):
                        with Image.open(image_path) as img:
                            image_width = img.width
                            LOGGER.debug(f"Got image width {image_width} from stream viewpoint {viewpoint.name}")
                            break
                except Exception as e:
                    LOGGER.warning(f"Could not get image dimensions from viewpoint {viewpoint.name}: {e}")

        # Fall back to lot's main image if no stream dimensions found
        if image_width == 3840 and lot.image_path:
            try:
                from PIL import Image
                import os
                image_path = os.path.join("static", "uploads", lot.image_path)
                if os.path.exists(image_path):
                    with Image.open(image_path) as img:
                        image_width = img.width
                        LOGGER.debug(f"Got image width {image_width} from lot main image")
            except Exception as e:
                LOGGER.warning(f"Could not get image dimensions from lot main image: {e}")

        context = get_base_template_context(request)
        context.update({
            "lot": lot,
            "lot_id": lot_id,
            "config": engine_config,
            "available_engines": available_engines,
            "image_width": image_width
        })

        return templates.TemplateResponse('configure_detection.html', context)

    except Exception as e:
        LOGGER.error(f"Error loading configuration page for lot {lot_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/{lot_id}/configure")
async def configure_engine_post(
    lot_id: str,
    engine_type: str = Form(...),
    is_enabled: bool = Form(True),
    update_interval: int = Form(30),
    # YOLO-specific parameters
    confidence_threshold: float = Form(0.5),
    iou_threshold: float = Form(0.45),
    overlap_threshold: float = Form(0.3),
    detection_width: int = Form(640),
    model_path: str = Form("yolov8n.pt"),
    all_classes: str = Form("[]"),
    socketio = Depends(get_socketio)
):
    """Handle detection engine configuration update."""
    try:
        lot = StorageService.get_parking_lot(lot_id)
        if not lot:
            LOGGER.warning(f"Parking lot with ID {lot_id} not found for configuration update")
            await socketio.emit('notification', {
                'type': 'error',
                'message': 'Parking lot not found'
            }, namespace='/dashboard')
            return RedirectResponse(url="/dashboard/", status_code=302)

        # Create detection config
        from src.models.detection import DetectionEngineConfig
        from src.services.detection_manager import DetectionManager
        import json

        # Build parameters based on engine type
        parameters = {}
        if engine_type == "yolo":
            # Parse selected classes from autocomplete only (no more checkboxes)
            selected_classes = []

            # Parse classes from autocomplete
            try:
                if all_classes:
                    selected_classes = json.loads(all_classes)
                    # Ensure all values are integers
                    selected_classes = [int(cls) for cls in selected_classes if isinstance(cls, (int, str)) and str(cls).isdigit()]
            except (json.JSONDecodeError, ValueError, TypeError):
                LOGGER.warning(f"Invalid classes format: {all_classes}")
                selected_classes = []

            # Ensure we have at least some default classes if none selected
            if not selected_classes:
                selected_classes = [2, 5, 7, 3]  # Default: car, bus, truck, motorcycle
                LOGGER.info(f"No classes selected, using defaults: {selected_classes}")

            parameters = {
                "model_path": model_path,
                "confidence_threshold": confidence_threshold,
                "iou_threshold": iou_threshold,
                "overlap_threshold": overlap_threshold,
                "detection_width": detection_width,
                "vehicle_classes": selected_classes
            }

        config = DetectionEngineConfig(
            engine_type=engine_type,
            is_enabled=is_enabled,
            update_interval=update_interval,
            parameters=parameters
        )

        # Update using event sourcing
        success = DetectionManager.configure_engine(lot_id, config)
        if success:
            LOGGER.info(f"Detection engine configured for lot {lot_id}: {engine_type}")
            await socketio.emit('notification', {
                'type': 'success',
                'message': 'Detection engine configured successfully'
            }, namespace='/dashboard')
        else:
            LOGGER.warning(f"Failed to configure detection engine for lot {lot_id}")
            await socketio.emit('notification', {
                'type': 'error',
                'message': 'Failed to configure detection engine'
            }, namespace='/dashboard')

        return RedirectResponse(url=f"/dashboard/{lot_id}", status_code=302)

    except Exception as e:
        LOGGER.error(f"Error configuring engine for lot {lot_id}: {str(e)}")
        await socketio.emit('notification', {
            'type': 'error',
            'message': f'Error configuring engine: {str(e)}'
        }, namespace='/dashboard')
        return RedirectResponse(url=f"/dashboard/{lot_id}", status_code=302)
