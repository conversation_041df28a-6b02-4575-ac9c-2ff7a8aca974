from threading import Thread

from selenium import webdriver
from pathlib import Path
from PIL import Image


def before_all(context):
    # Create test files directory
    Path("test_files").mkdir(exist_ok=True)

    # Create test files
    context.valid_image_path = Path("test_files/valid_image.jpg")
    if not context.valid_image_path.exists():
        img = Image.new('RGB', (800, 800), color='red')
        img.save(context.valid_image_path)

    context.invalid_file_path = Path("test_files/invalid.txt")
    if not context.invalid_file_path.exists():
        with open(context.invalid_file_path, 'w') as f:
            f.write("This is not an image file")

    # Set base URL
    context.base_url = "http://localhost:5000"

    # Initialize WebDriver
    context.driver = webdriver.Chrome()
    context.driver.implicitly_wait(5)


def after_all(context):
    # Clean up WebDriver
    if hasattr(context, 'driver'):
        context.driver.quit()

    # Clean up test files
    import shutil
    test_dir = Path("test_files")
    if test_dir.exists():
        shutil.rmtree(test_dir)