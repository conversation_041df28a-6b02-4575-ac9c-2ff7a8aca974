.class-autocomplete-container {
    position: relative;
    margin-bottom: 1.5rem;
}

.class-input-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 0.75rem;
}

.class-input-container .form-control {
    flex-grow: 1;
}

.classes-dropdown {
    position: absolute;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    background-color: white;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: none;
}

.class-option {
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.class-option:hover {
    background-color: #f8f9fa;
}

.selected-classes-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.selected-class-tag {
    display: flex;
    align-items: center;
    background-color: #e9ecef;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.selected-class-tag span {
    margin-right: 0.5rem;
}

.remove-class-btn {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    font-size: 1rem;
    line-height: 1;
    transition: color 0.2s;
}

.remove-class-btn:hover {
    color: #dc3545;
}

.class-autocomplete-help {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}
