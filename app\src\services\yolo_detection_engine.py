import os
from typing import Dict, List, Optional, <PERSON>ple

import cv2
import numpy as np
from supervision.detection.tools.inference_slicer import In<PERSON><PERSON>licer
from supervision.detection.core import Detections
from supervision.detection.overlap_filter import OverlapFilter
from ultralytics import YOLO  # type: ignore

from src.models.detection import DetectionEngine, DetectionResult
from src.models.parking import ParkingSlot
from src.utils import logger
from src.utils.storage import StorageService
from src.config.settings import get_settings

LOGGER = logger.setup_logger(__name__)


class YOLODetectionEngine(DetectionEngine):
    """YOLO-based implementation of vehicle detection for parking lots."""

    def __init__(self):
        """Initialize the YOLO detection engine."""
        self.model: YOLO | None = None
        settings = get_settings()
        self.model_path = settings.model_path
        self.config = {
            "model_path": self.model_path,
            "confidence_threshold": settings.detection_confidence_threshold,
            "iou_threshold": settings.detection_iou_threshold,
            "vehicle_classes": settings.vehicle_classes,
            "detection_width": settings.detection_width
        }
        # Load model if available
        self._load_model()
        self.classes = []
        self._slicer = InferenceSlicer(callback=self._slicer_callback, slice_wh=(640, 640), iou_threshold=0.25,
                                       overlap_filter=OverlapFilter.NON_MAX_MERGE, overlap_ratio_wh=(0.25, 0.25))

    def _slicer_callback(self, image_slice: np.ndarray) -> Detections:
        """Callback for the inference slicer."""
        w, h = image_slice.shape[:2]
        LOGGER.debug("Slicer callback called with image slice of shape: %s", image_slice.shape)
        imgsz = (w, h)
        if self.model is None:
            LOGGER.error("Model is not loaded")
            return Detections.empty()
        result = self.model(source=image_slice, classes=self.config["vehicle_classes"], imgsz=imgsz, iou=0.4)[0]
        detections = Detections.from_ultralytics(result)
        if detections.confidence is not None:
            LOGGER.debug("Slicer callback returned %d detections", len(detections.confidence))
        else:
            LOGGER.debug("Slicer callback returned detections with no confidence scores")
        return detections

    def _load_model(self) -> bool:
        """Load the YOLO model."""
        try:
            # Use the model path from config or a standard model name if not found
            if not os.path.exists(self.model_path):
                LOGGER.warning("Model not found at %s, using standard model 'yolov8n.pt' instead", self.model_path)
                self.model_path = "yolov8n.pt"  # Standard model name that will be fetched from the web

            # Create the YOLO model with the model path (either original or fallback)
            self.model = YOLO(self.model_path)
            self.classes = list(self.model.names.values())
            return True
        except Exception as e:
            LOGGER.error("Failed to load YOLO model: %s", e)
            # Try to load the standard model directly if the specified model failed
            try:
                if self.model_path != "yolov8n.pt":
                    LOGGER.warning("Attempting to load standard model 'yolov8n.pt' as fallback")
                    self.model_path = "yolov8n.pt"
                    self.model = YOLO(self.model_path)
                    self.classes = list(self.model.names.values())
                    return True
            except Exception as inner_e:
                LOGGER.error("Failed to load fallback YOLO model: %s", inner_e)

            self.model = None
            return False

    def _get_image_for_lot(self, lot_id: str) -> str:
        """Get the image path for a parking lot.

        This method exists to be mocked in tests.

        Args:
            lot_id: ID of the parking lot
        Returns:
            Path to the image file
        """
        parking_lot = StorageService.get_parking_lot(lot_id)
        if not parking_lot or not parking_lot.image_path:
            return ""
        return os.path.join("app", "static", parking_lot.image_path)

    def detect(self, parking_lot_id: str, image_path: Optional[str] = None) -> List[DetectionResult]:
        """Detect vehicle occupancy in parking slots.

        Args:
            parking_lot_id: ID of the parking lot
            image_path: Optional specific image path. If None, uses the lot's default image.

        Returns:
            List of DetectionResult objects for each slot
        """
        try:
            if not self.is_available():
                LOGGER.error("Detection engine is not available.")
                return []

            # Get parking lot
            lot = StorageService.get_parking_lot(parking_lot_id)
            if not lot:
                LOGGER.error("Parking lot %s not found", parking_lot_id)
                return []

            # Get image path if not provided
            if not image_path:
                LOGGER.debug("Using default image path for parking lot %s", parking_lot_id)
                image_path = self._get_image_for_lot(parking_lot_id)

            # Make sure image exists
            if not image_path or not os.path.exists(image_path):
                LOGGER.error("Image file %s does not exist", image_path)
                return []

            # Load the image to get dimensions
            img = cv2.imread(image_path)
            if img is None:
                LOGGER.error("Failed to read image file %s", image_path)
                return []

            height, width = img.shape[:2]
            LOGGER.debug("Image dimensions: height=%d, width=%d", height, width)

            # Calculate detection size while maintaining aspect ratio
            detection_width = 640  # Default value
            if lot.detection_config and hasattr(lot.detection_config, 'parameters'):
                detection_width = lot.detection_config.parameters.get("detection_width", self.config.get("detection_width", 640))
            else:
                detection_width = self.config.get("detection_width", 640)

            detection_height = int((detection_width / width) * height)
            detection_size = (detection_width, detection_height)

            LOGGER.debug(f"Running detection at resolution: {detection_size}")

            # Get the IoU threshold from config
            iou_threshold = self.config.get("iou_threshold", 0.45)

            # Run YOLO detection with the specified resolution and IoU threshold
            if self.model is None:
                LOGGER.error("YOLO model is not available")
                return []

            LOGGER.info("🔍 SINGLE PROCESSING: Running YOLO inference on single image")
            results = self.model(source=img, classes=self.config["vehicle_classes"],
                                imgsz=detection_size, iou=iou_threshold)[0]

            # Extract vehicle detections
            vehicles = self._extract_vehicles(results)
            LOGGER.info("🚗 DETECTED VEHICLES: Found %d vehicles in image", len(vehicles))
            for i, vehicle in enumerate(vehicles):
                x1, y1, x2, y2 = vehicle['bbox']
                LOGGER.info("🚗 VEHICLE %d: bbox=(%d,%d,%d,%d) class=%d conf=%.2f",
                           i, x1, y1, x2, y2, vehicle['class'], vehicle['confidence'])

            # Check each slot for occupancy
            detection_results = []
            for slot in lot.slots:
                if slot is None:
                    continue

                points = slot.detection_zone.points if slot.detection_zone else None
                # Skip slots with no defined zone
                if not points or len(points) < 3:
                    LOGGER.warning("Slot %s has no defined detection zone or less than 3 points", slot.slot_name)
                    continue

                try:
                    # Check if slot is occupied
                    is_occupied, confidence = self._check_slot_occupancy(vehicles, slot, (height, width))

                    # Create detection result
                    detection_results.append(
                        DetectionResult(
                            slot_id=slot.id,
                            slot_name=slot.slot_name,
                            is_occupied=is_occupied,
                            confidence=confidence
                        )
                    )
                except Exception as e:
                    LOGGER.error("Error checking slot %s occupancy: %s", slot.slot_name, e)
                    # Add a default result for this slot
                    detection_results.append(
                        DetectionResult(
                            slot_id=slot.id,
                            slot_name=slot.slot_name,
                            is_occupied=False,
                            confidence=0.0
                        )
                    )

            return detection_results
        except Exception as e:
            LOGGER.error("Error in YOLO detection: %s", e)
            return []

    def _extract_detection_vehicles(self, detections: Detections) -> List[Dict]:
        """Extract vehicle bounding boxes from YOLO results."""
        vehicles = []

        boxes = detections.xyxy
        confidences = detections.confidence
        class_idxs = detections.class_id

        # Check if we have valid detections
        if class_idxs is None or confidences is None:
            return vehicles

        for i in range(len(boxes)):
            box = boxes[i]
            # Get class label
            cls_idx = class_idxs[i].item() if class_idxs[i] is not None else 0

            # Check if this is a vehicle class we're interested in
            if cls_idx in self.config["vehicle_classes"]:
                # Get confidence
                conf = float(confidences[i].item()) if confidences[i] is not None else 0.0

                # Skip low confidence detections
                if conf < self.config["confidence_threshold"]:
                    continue

                # Get bounding box coordinates (convert to integers)
                x1, y1, x2, y2 = map(int, box.tolist())

                vehicles.append({
                    'class': cls_idx,
                    'confidence': conf,
                    'bbox': (x1, y1, x2, y2)
                })
        return vehicles

    def _extract_vehicles(self, yolo_results) -> List[Dict]:
        """Extract vehicle bounding boxes from YOLO results."""
        vehicles = []
        for r in yolo_results:
            boxes = r.boxes
            for box in boxes:
                # Get class ID and convert to integer
                cls_idx = int(box.cls[0].item())

                # Check if this is a vehicle class we're interested in
                if cls_idx in self.config["vehicle_classes"]:
                    # Get confidence
                    conf = float(box.conf[0].item())

                    # Skip low confidence detections
                    if conf < self.config["confidence_threshold"]:
                        continue

                    # Get bounding box coordinates (convert to integers)
                    x1, y1, x2, y2 = map(int, box.xyxy[0].tolist())

                    vehicles.append({
                        'class': cls_idx,
                        'confidence': conf,
                        'bbox': (x1, y1, x2, y2)
                    })

        return vehicles

    @staticmethod
    def _convert_points_to_pixels(points, width, height) -> List[Tuple[int, int]]:
        """Convert relative percentage points to absolute pixel coordinates."""
        pixel_points = []
        for p in points:
            # Handle both object-style points (with x and y attributes) and dictionary-style points (with 'x' and 'y' keys)
            if hasattr(p, 'x') and hasattr(p, 'y'):
                # Object with attributes
                x = int(p.x * width / 100)
                y = int(p.y * height / 100)
            elif isinstance(p, dict) and 'x' in p and 'y' in p:
                # Dictionary with keys
                x = int(p['x'] * width / 100)
                y = int(p['y'] * height / 100)
            else:
                # Unknown format, log and skip
                continue
            pixel_points.append((x, y))
        return pixel_points

    def _check_slot_occupancy(self, vehicles, slot: ParkingSlot, image_shape) -> Tuple[bool, float]:
        """
        Check if a parking slot is occupied by any detected vehicle.

        Args:
            vehicles: List of detected vehicles with bounding boxes
            slot: Parking slot object containing detection zone points
            image_shape: Tuple of (height, width) of the image

        Returns:
            Tuple of (is_occupied, confidence)
        """
        LOGGER.debug("🔍 CHECKING SLOT '%s': %d vehicles detected", slot.slot_name, len(vehicles))

        # If no vehicles detected, return not occupied
        if not vehicles:
            LOGGER.debug("🔍 SLOT '%s': No vehicles detected", slot.slot_name)
            return False, 0.0

        height, width = image_shape
        if not slot.detection_zone or not slot.detection_zone.points:
            LOGGER.warning("🔍 SLOT '%s': No detection zone defined", slot.slot_name)
            return False, 0.0

        slot_points = slot.detection_zone.points
        LOGGER.debug("🔍 SLOT '%s': Zone has %d points", slot.slot_name, len(slot_points))

        # Convert percentage points to pixel coordinates
        try:
            pixel_points = [[p.x, p.y] for p in slot_points]

            slot_polygon = np.array(pixel_points)

            for i, vehicle in enumerate(vehicles):
                # Get vehicle bounding box
                x1, y1, x2, y2 = vehicle['bbox']
                vehicle_rect = np.array([x1, y1, x2, y2])
                confidence = vehicle['confidence']

                LOGGER.debug("🔍 SLOT '%s': Checking vehicle %d at bbox (%d,%d,%d,%d) conf=%.2f",
                           slot.slot_name, i, x1, y1, x2, y2, confidence)

                # Check intersection
                intersection = self._is_intersecting(vehicle_rect, slot_polygon)

                # Use slot-specific overlap threshold
                overlap_threshold = getattr(slot, 'overlap_threshold', 0.3)  # Default to 0.3 if not present
                is_intersecting = intersection > overlap_threshold

                LOGGER.debug("🔍 SLOT '%s': Vehicle %d overlap=%.3f, threshold=%.3f, intersecting=%s",
                           slot.slot_name, i, intersection, overlap_threshold, is_intersecting)

                if is_intersecting:
                    LOGGER.info("✅ SLOT '%s': OCCUPIED by vehicle %d (overlap=%.3f > threshold=%.3f)",
                              slot.slot_name, i, intersection, overlap_threshold)
                    return True, confidence
        except Exception as e:
            LOGGER.error("Error in _check_slot_occupancy for slot '%s': %s", slot.slot_name, e)

        LOGGER.debug("🔍 SLOT '%s': No overlapping vehicles found - marking as FREE", slot.slot_name)
        return False, 0.0

    def _is_intersecting(self, vehicle_rect: np.ndarray, slot_polygon: np.ndarray) -> float:
        """Calculate the intersection ratio between a vehicle rectangle and a parking slot polygon.

        Args:
            vehicle_rect: Rectangle (x1, y1, x2, y2) representing the vehicle bounding box
            slot_polygon: Sympy Polygon object representing the parking slot

        Returns:
            Float between 0 and 1 representing the intersection ratio
        """
        # Convert rectangle to polygon
        x1, y1, x2, y2 = vehicle_rect[0], vehicle_rect[1], vehicle_rect[2], vehicle_rect[3]
        vehicle_points = np.array([
            [x1, y1], [x2, y1],
            [x2, y2], [x1, y2]
        ], dtype=np.int32)

        # Calculate the slot area
        slot_area = cv2.contourArea(slot_polygon)
        if slot_area <= 0:
            return 0.0  # Avoid division by zero

        # Create masks for calculating the intersection
        x_min = min(np.min(slot_polygon[:, 0]), x1)
        y_min = min(np.min(slot_polygon[:, 1]), y1)
        x_max = max(np.max(slot_polygon[:, 0]), x2)
        y_max = max(np.max(slot_polygon[:, 1]), y2)

        # Create appropriately sized masks
        width, height = int(x_max - x_min + 10), int(y_max - y_min + 10)
        offset = (int(x_min), int(y_min))

        # Adjust points for the offset
        slot_points_adj = slot_polygon - offset
        vehicle_points_adj = vehicle_points - offset

        # Create and fill masks
        slot_mask = np.zeros((height, width), dtype=np.uint8)
        vehicle_mask = np.zeros((height, width), dtype=np.uint8)
        cv2.fillPoly(slot_mask, [slot_points_adj], (255,))
        cv2.fillPoly(vehicle_mask, [vehicle_points_adj], (255,))

        # Calculate intersection
        intersection = cv2.bitwise_and(slot_mask, vehicle_mask)
        intersection_area = cv2.countNonZero(intersection)

        # Calculate intersection ratio
        return min(1.0, intersection_area / slot_area)

    def get_configuration(self) -> Dict:
        """Get current configuration parameters."""
        return {
            "model_path": self.model_path,
            **self.config
        }

    def configure(self, config: Dict) -> bool:
        """Update engine configuration parameters."""
        try:
            # Update model path if provided
            if "model_path" in config and config["model_path"] != self.model_path:
                self.model_path = config["model_path"]
                self._load_model()

            # Update other config parameters
            for key in ["confidence_threshold", "overlap_threshold", "iou_threshold", "vehicle_classes", "detection_width"]:
                if key in config:
                    self.config[key] = config[key]

            return True
        except Exception as e:
            print(f"Error configuring YOLO engine: {e}")
            return False

    def is_available(self) -> bool:
        """Check if the detection engine is available and operational."""
        return self.model is not None

    def get_classes(self) -> List[str]:
        """Get the list of classes the detection engine can detect."""
        if self.model:
            return list(self.model.names.values())
        return []

    def detect_batch(self, parking_lot_id: str, viewpoint_ids: Optional[List[str]] = None) -> Dict[str, List[DetectionResult]]:
        """
        Detect vehicle occupancy across multiple viewpoints using batch inference.
        
        Args:
            parking_lot_id: ID of the parking lot
            viewpoint_ids: List of viewpoint IDs to process. If None, uses all active viewpoints.
            
        Returns:
            Dict mapping viewpoint_id to detection results
        """
        try:
            if not self.is_available():
                LOGGER.error("Detection engine is not available.")
                return {}

            # Get parking lot
            lot = StorageService.get_parking_lot(parking_lot_id)
            if not lot:
                LOGGER.error("Parking lot %s not found", parking_lot_id)
                return {}

            # Determine viewpoints to process
            if viewpoint_ids is None:
                viewpoints_to_process = lot.get_active_viewpoints()
            else:
                viewpoints_to_process = [v for v in lot.viewpoints if v.id in viewpoint_ids and v.is_active]

            if not viewpoints_to_process:
                LOGGER.warning("No active viewpoints found for batch detection")
                return {}

            # Prepare images for batch processing
            viewpoint_images = []
            viewpoint_map = {}

            for viewpoint in viewpoints_to_process:
                # Construct full image path like in single detection
                if viewpoint.image_path:
                    full_image_path = os.path.join("app", "static", viewpoint.image_path)
                    if os.path.exists(full_image_path):
                        img = cv2.imread(full_image_path)
                        if img is not None:
                            viewpoint_images.append(img)
                            viewpoint_map[len(viewpoint_images) - 1] = viewpoint.id
                            LOGGER.debug("Loaded image for viewpoint %s: %s", viewpoint.id, full_image_path)
                        else:
                            LOGGER.warning("Failed to load image for viewpoint %s: %s", viewpoint.id, full_image_path)
                    else:
                        LOGGER.warning("Image file does not exist for viewpoint %s: %s", viewpoint.id, full_image_path)
                else:
                    LOGGER.warning("No image path configured for viewpoint %s", viewpoint.id)

            if not viewpoint_images:
                LOGGER.error("No valid images found for batch processing")
                return {}

            # Batch inference - this is the key optimization
            LOGGER.info("🚀 BATCH PROCESSING: Running YOLO inference on %d images simultaneously", len(viewpoint_images))
            LOGGER.debug("Batch detection config - classes: %s, imgsz: %s, iou: %s",
                        self.config["vehicle_classes"], self.config["detection_width"], self.config["iou_threshold"])

            results = self.model(viewpoint_images, classes=self.config["vehicle_classes"],
                               imgsz=self.config["detection_width"],
                               iou=self.config["iou_threshold"])

            LOGGER.info("✅ BATCH PROCESSING: Completed inference for %d viewpoints", len(results))
            
            # Process results per viewpoint
            viewpoint_results = {}
            for idx, result in enumerate(results):
                if idx in viewpoint_map:
                    viewpoint_id = viewpoint_map[idx]
                    image_shape = viewpoint_images[idx].shape[:2]  # (height, width)
                    
                    # Extract vehicles from this viewpoint's result
                    vehicles = self._extract_vehicles([result])
                    LOGGER.info("🚗 BATCH VEHICLES: Found %d vehicles in viewpoint %s", len(vehicles), viewpoint_id)
                    for j, vehicle in enumerate(vehicles):
                        x1, y1, x2, y2 = vehicle['bbox']
                        LOGGER.info("🚗 BATCH VEHICLE %d: bbox=(%d,%d,%d,%d) class=%d conf=%.2f",
                                   j, x1, y1, x2, y2, vehicle['class'], vehicle['confidence'])
                    
                    # Get slots for this viewpoint
                    viewpoint_slots = [slot for slot in lot.slots if slot.viewpoint_id == viewpoint_id]
                    
                    # Check occupancy for each slot
                    detection_results = []
                    for slot in viewpoint_slots:
                        if slot.detection_zone and slot.detection_zone.points:
                            try:
                                is_occupied, confidence = self._check_slot_occupancy(vehicles, slot, image_shape)
                                detection_results.append(
                                    DetectionResult(
                                        slot_id=slot.id,
                                        slot_name=slot.slot_name,
                                        is_occupied=is_occupied,
                                        confidence=confidence
                                    )
                                )
                            except Exception as e:
                                LOGGER.error("Error checking slot %s occupancy in viewpoint %s: %s", 
                                           slot.slot_name, viewpoint_id, e)
                                detection_results.append(
                                    DetectionResult(
                                        slot_id=slot.id,
                                        slot_name=slot.slot_name,
                                        is_occupied=False,
                                        confidence=0.0
                                    )
                                )
                        else:
                            LOGGER.warning("Slot %s in viewpoint %s has no detection zone", 
                                         slot.slot_name, viewpoint_id)
                    
                    viewpoint_results[viewpoint_id] = detection_results

            return viewpoint_results
            
        except Exception as e:
            LOGGER.error("Error in batch YOLO detection: %s", e)
            return {}

    def detect_viewpoint(self, parking_lot_id: str, viewpoint_id: str) -> List[DetectionResult]:
        """
        Detect vehicle occupancy for a specific viewpoint.
        
        Args:
            parking_lot_id: ID of the parking lot
            viewpoint_id: ID of the specific viewpoint to process
            
        Returns:
            List of detection results for the viewpoint
        """
        batch_results = self.detect_batch(parking_lot_id, [viewpoint_id])
        return batch_results.get(viewpoint_id, [])
