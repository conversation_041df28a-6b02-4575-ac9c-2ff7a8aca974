{% extends "base_layout.html" %}

{% block title %}{{ lot.name }} Dashboard{% endblock %}

{% block extra_css %}
    <link href="/static/css/shared_dashboard.css" rel="stylesheet">
    <link href="/static/css/lot_dashboard.css" rel="stylesheet">
    <link href="/static/css/zoom_styles.css" rel="stylesheet">
    <link href="/static/css/viewpoint-selector.css" rel="stylesheet">
{% endblock %}

{% block content %}
    <div class="lot-dashboard">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>{{ lot.name }}</h1>
            <div class="d-flex gap-2">
                <a href="/lot/{{ lot_id }}/edit" class="btn btn-secondary">Edit Zones</a>
                <a href="/dashboard/{{ lot_id }}/configure" class="btn btn-primary">Configure
                    Detection</a>
                <button class="btn btn-primary" id="run-detection-btn">
                    <i class="bi bi-camera"></i> Run Detection
                </button>
                <a href="/update/{{ lot_id }}" class="btn btn-info">
                    <i class="bi bi-arrow-repeat"></i> Update Image
                </a>
            </div>
        </div>

        <!-- Viewpoint Selector -->
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0 d-flex align-items-center">
                            <i class="bi bi-camera-video me-2"></i>
                            Select Viewpoint
                        </h6>
                    </div>
                    <div class="card-body">
                        <viewpoint-selector
                                lot-id="{{ lot_id }}"
                                current-viewpoint="{{ (default_viewpoint.id if default_viewpoint else '') }}">
                        </viewpoint-selector>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Viewpoint Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary btn-sm" id="add-viewpoint-btn">
                                <i class="bi bi-plus"></i> Add Viewpoint
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" id="manage-viewpoints-btn">
                                <i class="bi bi-gear"></i> Manage
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert-container"></div>

        <div class="row g-4">
            <!-- Parking Visualization Column -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-body p-0">
                        <!-- Parking Visualization Component -->
                        <parking-visualization
                            id="parking-visualization"
                            lot-id="{{ lot.id }}"
                            viewpoint-id="{{ default_viewpoint.id if default_viewpoint else '' }}"
                            show-controls="true"
                            show-minimap="true"
                            show-stats="false"
                            style="height: 600px;">
                        </parking-visualization>
                    </div>
                    <!-- Occupancy meter -->
                    <div class="occupancy-meter">
                        <div class="progress">
                            <div id="occupancy-progress" class="progress-bar" role="progressbar"
                                 style="width: {{ "%.1f"|format(grouped_stats.occupancy_rate) }}%;"></div>
                        </div>
                        <div class="progress-label">{{ "%.1f"|format(grouped_stats.occupancy_rate) }}% Occupied</div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="col-lg-4">
                <!-- Stats Section -->
                <div class="occupancy-stats mb-4">
                    <div class="stats-container">
                        <div class="stat-box free">
                            <div class="stat-icon">
                                <i class="bi bi-p-square"></i>
                            </div>
                            <div class="stat-content">
                                <h2 id="free-count">{{ grouped_stats.available_groups }}</h2>
                                <p>Available Spots</p>
                            </div>
                        </div>

                        <div class="stat-box occupied">
                            <div class="stat-icon">
                                <i class="bi bi-car-front"></i>
                            </div>
                            <div class="stat-content">
                                <h2 id="occupied-count">{{ grouped_stats.occupied_groups }}</h2>
                                <p>Occupied Spots</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Event Log -->
                <div class="card event-log-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Event Log</h5>
                        <div class="d-flex gap-2 align-items-center">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="show-system-events" title="Show technical/system events">
                                <label class="form-check-label" for="show-system-events" style="font-size: 0.875rem;">
                                    System Events
                                </label>
                            </div>
                            <button class="btn btn-sm btn-outline-secondary" id="clear-log-btn">
                                Clear Log
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="event-log-table">
                            <thead>
                            <tr>
                                <th>Time</th>
                                <th>Details</th>
                                <th>Change</th>
                                <th>Source</th>
                            </tr>
                            </thead>
                            <tbody id="event-log-body">
                            <!-- Event rows will be added here dynamically -->
                            </tbody>
                        </table>
                    </div>
                    <div class="card-footer">
                        <div id="events-pagination" class="d-flex justify-content-between">
                            <!-- Pagination controls will be added here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.4/socket.io.min.js"></script>
    <script>
        // Pass required data from Flask to JavaScript
        const LOT_ID = '{{ lot.id }}';  // Fix: use lot.id instead of lot_id
        window.LOT_ID = LOT_ID;  // Make it globally available
        // Pass lot data to the global scope
        window.lot = {
            busy_slots: {{ lot.get_busy_slot_count() }},
            free_slots: {{ lot.get_free_slot_count() }}
        };

        // Initialize Socket.IO directly with namespace
        window.socketConnection = io('/dashboard');
    </script>
    <script src="/static/js/zoom_manager.js"></script>
    <script src="/static/js/viewpoint-selector.js"></script>
    <script src="/static/js/components/parking-slot.js"></script>
    <script src="/static/js/components/parking-visualization.js"></script>
    <script src="/static/js/register_components.js"></script>
    <script src="/static/js/lot_dashboard.js"></script>
    <!-- REMOVED detection engine script -->
{% endblock %}
