import os
from pathlib import Path
from werkzeug.datastructures import FileStorage
from typing import Optional

from src.utils import logger

LOGGER = logger.setup_logger(__name__)


class ImageHandler:
    """Simplified class for handling image saving and updating for PoC."""
    
    ALLOWED_EXTENSIONS = {'jpg', 'jpeg', 'png'}
    
    def __init__(self, storage_dir="static/uploads"):
        """
        Initialize the image handler.
        
        Args:
            storage_dir: Absolute path to the directory to store images (e.g., /abs/path/to/static/uploads)
        """
        self.storage_dir = storage_dir
        if not os.path.exists(self.storage_dir):
            os.makedirs(self.storage_dir, exist_ok=True)
            LOGGER.info(f"Created storage directory: {self.storage_dir}")

    @classmethod
    def allowed_file(cls, filename: str) -> bool:
        """Check if the filename has an allowed extension."""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in cls.ALLOWED_EXTENSIONS

    def save_image(self, image_bytes: bytes, lot_id: str, original_filename: str) -> str:
        """
        Save an image with a filename based on lot_id and original extension.
        
        Args:
            image_bytes: Image content in bytes.
            lot_id: ID of the parking lot.
            original_filename: The original name of the uploaded file to derive the extension.
            
        Returns:
            Relative path to saved image from the base static directory (e.g., 'uploads/lot_id.jpg').
        """
        if not isinstance(image_bytes, bytes):
            raise ValueError("Image must be in bytes format")

        file_extension = Path(original_filename).suffix.lower()
        # Corrected line: ensure dot is part of the check or use strip correctly
        if not file_extension or file_extension.lstrip('.') not in self.ALLOWED_EXTENSIONS:
            LOGGER.warning(f"Original filename '{original_filename}' has an unsupported or missing extension. Defaulting to .jpg.")
            file_extension = ".jpg" # PoC: default to .jpg

        simple_filename = f"{lot_id}{file_extension}"
        absolute_save_path = os.path.join(self.storage_dir, simple_filename)
        
        try:
            with open(absolute_save_path, 'wb') as f:
                f.write(image_bytes)
            LOGGER.info(f"Saved image to {absolute_save_path}")
        except IOError as e:
            LOGGER.error(f"Failed to save image {absolute_save_path}: {e}")
            raise

        upload_folder_name = os.path.basename(self.storage_dir) # e.g., 'uploads'
        relative_path = os.path.join(upload_folder_name, simple_filename).replace("\\", "/")
        return relative_path
        
    @staticmethod
    def update_image(file_storage: FileStorage, lot_id: str, storage_dir_abs_path: str, old_relative_image_path: Optional[str]) -> str:
        """
        Update an image: save the new one, delete the old one. Filename based on lot_id.

        Args:
            file_storage: The new image file from the request.
            lot_id: ID of the parking lot.
            storage_dir_abs_path: Absolute path to the directory where images are stored.
            old_relative_image_path: The existing relative path (e.g., 'uploads/lot_id_old.png') or None.

        Returns:
            The new relative path to the saved image (e.g., 'uploads/lot_id.jpg').
        """
        if not ImageHandler.allowed_file(file_storage.filename):
            raise ValueError(f"Invalid file type for update: {file_storage.filename}. Allowed: {ImageHandler.ALLOWED_EXTENSIONS}")

        if not os.path.exists(storage_dir_abs_path):
            os.makedirs(storage_dir_abs_path, exist_ok=True)
            LOGGER.info(f"Created storage directory (from static update_image): {storage_dir_abs_path}")

        new_file_extension = Path(file_storage.filename).suffix.lower()
        new_simple_filename = f"{lot_id}{new_file_extension}"
        absolute_new_save_path = os.path.join(storage_dir_abs_path, new_simple_filename)

        try:
            file_storage.save(absolute_new_save_path)
            LOGGER.info(f"Saved new image to {absolute_new_save_path}")
        except Exception as e:
            LOGGER.error(f"Error saving updated image {absolute_new_save_path}: {e}")
            raise

        if old_relative_image_path:
            old_filename_only = os.path.basename(old_relative_image_path)
            absolute_old_path_to_delete = os.path.join(storage_dir_abs_path, old_filename_only)
            
            if absolute_old_path_to_delete != absolute_new_save_path and os.path.exists(absolute_old_path_to_delete):
                try:
                    os.remove(absolute_old_path_to_delete)
                    LOGGER.info(f"Successfully deleted old image: {absolute_old_path_to_delete}")
                except OSError as e:
                    LOGGER.error(f"Error deleting old image {absolute_old_path_to_delete}: {e}")
        
        upload_folder_name = os.path.basename(storage_dir_abs_path)
        new_relative_path = os.path.join(upload_folder_name, new_simple_filename).replace("\\", "/")
        return new_relative_path

    def create_placeholder_image(self, lot_id: str, filename: str) -> str:
        """
        Create a placeholder image for stream-based parking lots.

        Args:
            lot_id: ID of the parking lot
            filename: Filename for the placeholder

        Returns:
            Relative path to the placeholder image
        """
        try:
            from PIL import Image, ImageDraw, ImageFont

            # Create a simple placeholder image
            width, height = 800, 600
            image = Image.new('RGB', (width, height), color='#f0f0f0')
            draw = ImageDraw.Draw(image)

            # Try to use a default font, fallback to basic if not available
            try:
                font = ImageFont.truetype("arial.ttf", 40)
                small_font = ImageFont.truetype("arial.ttf", 20)
            except (OSError, IOError):
                try:
                    font = ImageFont.load_default()
                    small_font = ImageFont.load_default()
                except:
                    font = None
                    small_font = None

            # Draw placeholder text
            text = "Video Stream"
            subtext = "Waiting for first frame..."

            if font:
                # Calculate text position for centering
                bbox = draw.textbbox((0, 0), text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                text_x = (width - text_width) // 2
                text_y = (height - text_height) // 2 - 20

                draw.text((text_x, text_y), text, fill='#666666', font=font)

                if small_font:
                    bbox2 = draw.textbbox((0, 0), subtext, font=small_font)
                    subtext_width = bbox2[2] - bbox2[0]
                    subtext_x = (width - subtext_width) // 2
                    subtext_y = text_y + text_height + 10

                    draw.text((subtext_x, subtext_y), subtext, fill='#999999', font=small_font)

            # Save the placeholder image
            file_extension = Path(filename).suffix.lower() or '.jpg'
            simple_filename = f"{lot_id}_placeholder{file_extension}"
            absolute_save_path = os.path.join(self.storage_dir, simple_filename)

            # Convert to RGB if saving as JPEG
            if file_extension.lower() in ['.jpg', '.jpeg']:
                if image.mode != 'RGB':
                    image = image.convert('RGB')
                image.save(absolute_save_path, 'JPEG', quality=85)
            else:
                image.save(absolute_save_path, 'PNG')

            LOGGER.info(f"Created placeholder image: {absolute_save_path}")

            # Return relative path
            upload_folder_name = os.path.basename(self.storage_dir)
            relative_path = os.path.join(upload_folder_name, simple_filename).replace("\\", "/")
            return relative_path

        except ImportError:
            LOGGER.warning("PIL not available, creating text placeholder")
            # Fallback: create a simple text file as placeholder
            simple_filename = f"{lot_id}_placeholder.txt"
            absolute_save_path = os.path.join(self.storage_dir, simple_filename)

            with open(absolute_save_path, 'w') as f:
                f.write("Video Stream Placeholder\nWaiting for first frame...")

            upload_folder_name = os.path.basename(self.storage_dir)
            relative_path = os.path.join(upload_folder_name, simple_filename).replace("\\", "/")
            return relative_path

        except Exception as e:
            LOGGER.error(f"Error creating placeholder image: {str(e)}")
            # Return a default path that won't break the system
            return "uploads/placeholder.jpg"

    @staticmethod
    def update_image_from_bytes(image_bytes: bytes, current_image_path: str, storage_dir: str) -> str:
        """
        Update an image from raw bytes, typically from a video stream frame.

        Args:
            image_bytes: Raw image data as bytes
            current_image_path: Current relative path of the image
            storage_dir: Absolute path to storage directory

        Returns:
            Relative path to the updated image
        """
        try:
            # Extract filename from current path
            if current_image_path:
                filename = os.path.basename(current_image_path)
                # Remove the uploads/ prefix if present
                if filename.startswith('uploads/'):
                    filename = filename[8:]
            else:
                filename = "stream_capture.jpg"

            # Ensure we have a valid filename
            if not filename or filename == "placeholder.jpg":
                filename = "stream_capture.jpg"

            # Write the new image
            absolute_save_path = os.path.join(storage_dir, filename)

            # Ensure directory exists
            os.makedirs(os.path.dirname(absolute_save_path), exist_ok=True)

            with open(absolute_save_path, 'wb') as f:
                f.write(image_bytes)

            LOGGER.info(f"Updated image from stream: {absolute_save_path}")

            # Return relative path
            upload_folder_name = os.path.basename(storage_dir)
            relative_path = os.path.join(upload_folder_name, filename).replace("\\", "/")
            return relative_path

        except Exception as e:
            LOGGER.error(f"Error updating image from bytes: {str(e)}")
            # Return the current path to avoid breaking the system
            return current_image_path or "uploads/placeholder.jpg"
