/* Styles for zoom functionality */

.zoom-wrapper {
    position: relative;
    width: 100%;
    height: auto; /* Let content determine height */
    overflow: hidden;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f8f9fa;
}

/* REMOVED .zoom-container styles */

/* Image container styles */
.img-container {
    position: relative;
    width: 100%;
    height: 0; /* Height is controlled by padding-bottom */
    padding-bottom: 56.25%; /* Default aspect ratio, will be updated by JS */
    overflow: hidden; /* Ensure image doesn't overflow */
    background: #eee; /* Optional: background while image loads */
}

/* Image and container styles */
.img-container img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: block;
    max-width: none;
    object-fit: contain; /* Ensure image fits within container */
    z-index: 1;
}

/* Ensure overlays stay on top of the image */
#parking-slots-overlay {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    pointer-events: none;
    z-index: 2;
}

.parking-slot {
    position: absolute !important;
    /* top/left/width/height are handled by clip-path container */
    opacity: 0.7;
    z-index: 3; /* Ensure slots are above image but below labels/points */
    pointer-events: all;
    transform-origin: 0 0;
    margin: 0 !important;
    padding: 0 !important;
    will-change: transform;
    /* background/border defined elsewhere */
}

/* Ensure images fill their containers properly */
.zoom-container img,
.img-container img,
#parking-image {
    position: relative;
    width: 100%;
    height: auto;
    display: block;
    max-width: none;
    z-index: 1;
}

/* Zoom controls */
#zoom-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem; /* Adjust padding */
    background-color: rgba(255, 255, 255, 0.9); /* Slightly more opaque */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative; /* Keep relative positioning */
    z-index: 100;
    border-bottom: 1px solid #eee; /* Add subtle border */
}

#zoom-controls button {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

#zoom-level {
    min-width: 50px;
    text-align: center;
    font-weight: bold;
    font-size: 0.875rem;
    color: #6c757d;
}

/* Critical fix: ensure drawing elements use percentage-based positioning */
.zone-polygon, .zone-label, .vertex-point, .drawing-point, .overlay-line {
    pointer-events: auto;
    position: absolute;
    /* Force consistent transform origin */
    transform-origin: 0 0 !important;
    backface-visibility: hidden;
    will-change: transform;
    box-sizing: border-box;
    z-index: 3;
}

/* Force vertex and drawing points to center correctly */
.drawing-point, .vertex-point {
    transform: translate(-50%, -50%) !important;
    transform-origin: center center !important;
}

/* Mini-map styles */
.minimap-container {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 200px;
    height: auto;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ccc;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: opacity 0.3s ease;
}

.minimap-container:hover {
    opacity: 1;
}

.minimap-container img {
    width: 100%;
    height: auto;
    display: block;
}

.minimap-viewport {
    position: absolute;
    border: 2px solid #007bff;
    background: rgba(0, 123, 255, 0.1);
    pointer-events: none;
    box-sizing: border-box;
}

/* Add overlay outside of viewport */
.minimap-container::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(173, 216, 230, 0.3);
    pointer-events: none;
    z-index: -1;
}

/* Hide minimap based on toggle state */
.minimap-hidden {
    display: none;
}

/* Labels for both zones and slots */
.zone-label,
.label {
    position: absolute;
    background: rgba(255, 255, 255, 0.8);
    padding: 4px 8px;
    border-radius: 3px;
    font-weight: bold;
    z-index: 4;
    transform: translate(-50%, -50%);
    pointer-events: none;
    font-size: 13px;
    color: #000;
    white-space: nowrap;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    min-width: 20px;
    height: auto;
    line-height: normal;
    border: 1px solid rgba(0, 0, 0, 0.2);
}

/* State styles for slots */
.parking-slot.free {
    border-color: #198754;
    background-color: rgba(25, 135, 84, 0.2);
}

.parking-slot.occupied {
    border-color: #dc3545;
    background-color: rgba(220, 53, 69, 0.2);
}

/* Animations */
@keyframes slot-status-change {
    0% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.3); opacity: 0.9; }
    100% { transform: scale(1); opacity: 0.7; }
}

.parking-slot.status-changed {
    animation: slot-status-change 1000ms cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
    z-index: 5;
    pointer-events: none;
    box-shadow: 0 0 25px currentColor;
}
