#!/usr/bin/env python3
import sys
import os
sys.path.append('app')

from src.utils.event_sourced_storage import EventSourcedStorage
from src.utils.sqlalchemy_event_store import SQLAlchemyEventStore

def main():
    try:
        # Initialize storage
        event_store = SQLAlchemyEventStore()
        storage = EventSourcedStorage(event_store)

        # Get all parking lots
        lots = storage.get_all_parking_lots()
        print(f'Found {len(lots)} parking lots')

        for lot in lots:
            print(f'\nLot: {lot.name} (ID: {lot.id})')
            
            # Get all events for this lot
            events = storage._get_events_for_lot(lot.id)
            
            # Filter occupancy events
            occupancy_events = [e for e in events if 'Occupancy' in e.event_type]
            print(f'  Total Occupancy Events: {len(occupancy_events)}')
            
            # Show recent occupancy events
            print(f'  Recent Occupancy Events (last 10):')
            for event in occupancy_events[-10:]:
                slot_id = event.payload.get('slot_id', 'unknown')
                is_occupied = event.payload.get('is_occupied', 'unknown')
                timestamp = event.payload.get('detection_timestamp', 'no timestamp')
                
                # Find slot name
                slot = lot.get_slot(slot_id)
                slot_name = slot.slot_name if slot else f'slot_id:{slot_id}'
                
                print(f'    {event.timestamp}: {slot_name} -> {"occupied" if is_occupied else "free"} (detection: {timestamp})')
            
            # Check current slot states
            print(f'  Current Slot States:')
            for slot in lot.slots:
                print(f'    {slot.slot_name} (ID: {slot.id}): {"occupied" if slot.is_occupied else "free"}')
            
            # Calculate occupancy stats
            total_slots = len(lot.slots)
            occupied_slots = sum(1 for slot in lot.slots if slot.is_occupied)
            occupancy_rate = lot.get_occupancy_rate()
            
            print(f'  Occupancy Summary:')
            print(f'    Total slots: {total_slots}')
            print(f'    Occupied slots: {occupied_slots}')
            print(f'    Occupancy rate: {occupancy_rate:.2%}')

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
