import os
import sys
from logging.config import fileConfig
import urllib # Import urllib here

from sqlalchemy import engine_from_config
from sqlalchemy import pool

from alembic import context

# Add app root to sys.path
# This assumes env.py is in alembic/ directory relative to app root
app_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, app_root)

# Import your models' Base metadata
from src.models.orm import Base # noqa E402

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = Base.metadata # Use the imported Base metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:-
# my_important_option = config.get_main_option("my_important_option")
# ... etc.

def get_database_url():
    """Determines the database URL based on environment variables."""
    azure_conn_str = os.environ.get('AZURE_SQL_CONNECTION_STRING')
    if azure_conn_str:
        # Use Azure SQL (MSSQL)
        quoted_connection_string = urllib.parse.quote_plus(azure_conn_str)
        return f"mssql+pyodbc:///?odbc_connect={quoted_connection_string}"
    else:
        # Use SQLite
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
        db_dir = os.path.join(project_root, 'data')
        db_path = os.path.join(db_dir, 'parking.db')
        # Ensure the directory exists
        os.makedirs(db_dir, exist_ok=True)
        return f"sqlite:///{db_path}"

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode."""
    sqlalchemy_url = get_database_url()
    context.configure(
        url=sqlalchemy_url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        # Add compare_type=True for SQLite type comparison support
        compare_type=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    # Use engine_from_config with the dynamic URL
    # Corrected: Use config_ini_section
    configuration = config.get_section(config.config_ini_section)
    configuration['sqlalchemy.url'] = get_database_url() # Set the URL dynamically
    connectable = engine_from_config(
        configuration, # Use the modified configuration
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, 
            target_metadata=target_metadata,
            render_as_batch=True # Keep batch mode enabled
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
