"""
Unit tests for the detection_service.py module
"""
import unittest
from unittest.mock import MagicMock, patch

from app.src.models.detection import DetectionResult
from app.src.services.detection_manager import DetectionManager
from app.src.services.detection_service import DetectionService
from app.src.services.image_handler import ImageHandler
from app.src.services.occupancy_manager import OccupancyManager
from app.src.services.event_service import event_service


class TestDetectionService(unittest.TestCase):
    """Test case for DetectionService class"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create mocks for dependencies
        self.mock_detection_manager = MagicMock(spec=DetectionManager)
        self.mock_detection_manager.run_batch_detection = MagicMock()
        self.mock_image_handler = MagicMock(spec=ImageHandler)
        self.mock_occupancy_manager = MagicMock(spec=OccupancyManager)
        
        # Patch the event_service
        self.event_service_patcher = patch('app.src.services.detection_service.event_service')
        self.mock_event_service = self.event_service_patcher.start()
        
        # Create detection service with mocked dependencies
        self.service = DetectionService(
            detection_manager=self.mock_detection_manager,
            image_handler=self.mock_image_handler,
            occupancy_manager=self.mock_occupancy_manager
        )
        
        # Test data
        self.lot_id = "test-lot-123"
        
    def tearDown(self):
        """Tear down test fixtures"""
        self.event_service_patcher.stop()
    
    def test_successful_detection(self):
        """Test successful detection and occupancy update"""
        # Arrange
        # Create some mock detection results
        mock_results = [
            DetectionResult(slot_id="slot-1", slot_name="slot-name-1", is_occupied=True, confidence=0.9),
            DetectionResult(slot_id="slot-2", slot_name="slot-name-2", is_occupied=False, confidence=0.8),
            DetectionResult(slot_id="slot-3", slot_name="slot-name-3", is_occupied=True, confidence=0.95)
        ]
        
        # Configure the detection manager to return our mock results
        self.mock_detection_manager.run_batch_detection.return_value = {"viewpoint1": mock_results}
        
        # Configure the occupancy manager to return successful updates
        self.mock_occupancy_manager.update_from_detection_results.return_value = {
            "slot-1": True,
            "slot-2": True,
            "slot-3": False  # Simulate that slot-3 didn't change
        }
        
        # Act
        result = self.service.run_detection_for_lot(self.lot_id)
        
        # Assert
        # Check the detection manager was called correctly
        self.mock_detection_manager.run_batch_detection.assert_called_once_with(self.lot_id)
        
        # Check the occupancy manager was called correctly
        self.mock_occupancy_manager.update_from_detection_results.assert_called_once_with(
            self.lot_id, mock_results
        )
        
        # Check that events were emitted
        self.mock_event_service.emit_detection_status.assert_any_call(
            self.lot_id, "processing"
        )
        self.mock_event_service.emit_detection_status.assert_any_call(
            self.lot_id, 
            "completed", 
            updates={
                "total_slots": 3,
                "occupied_slots": 2,
                "free_slots": 1,
                "changed_slots": 2
            }
        )
        
        # Check the result contains the expected data
        self.assertTrue(result["success"])
        self.assertEqual(result["total_slots"], 3)
        self.assertEqual(result["occupied_slots"], 2)
        self.assertEqual(result["free_slots"], 1)
        self.assertEqual(result["changed_slots"], 2)
        self.assertIn("detection_time", result)
    
    def test_error_during_detection(self):
        """Test handling of errors during detection"""
        # Arrange
        # Configure the detection manager to raise an exception
        error_message = "Test detection error"
        self.mock_detection_manager.run_batch_detection.side_effect = Exception(error_message)
        
        # Act
        result = self.service.run_detection_for_lot(self.lot_id)
        
        # Assert
        # Check the detection manager was called
        self.mock_detection_manager.run_batch_detection.assert_called_once_with(self.lot_id)
        
        # Check that occupancy manager was not called
        self.mock_occupancy_manager.update_from_detection_results.assert_not_called()
        
        # Check that events were emitted
        self.mock_event_service.emit_detection_status.assert_any_call(
            self.lot_id, "processing"
        )
        self.mock_event_service.emit_detection_status.assert_any_call(
            self.lot_id, "error", error=error_message
        )
        
        # Check the result contains error information
        self.assertFalse(result["success"])
        self.assertEqual(result["error"], error_message)
        self.assertIn("detection_time", result)
    
    def test_no_detection_results(self):
        """Test handling of no detection results"""
        # Arrange
        # Configure the detection manager to return empty results
        self.mock_detection_manager.run_batch_detection.return_value = {}
        
        # Act
        result = self.service.run_detection_for_lot(self.lot_id)
        
        # Assert
        # Check the detection manager was called
        self.mock_detection_manager.run_batch_detection.assert_called_once_with(self.lot_id)
        
        # Check that occupancy manager was not called
        self.mock_occupancy_manager.update_from_detection_results.assert_not_called()
        
        # Check that events were emitted
        self.mock_event_service.emit_detection_status.assert_any_call(
            self.lot_id, "processing"
        )
        self.mock_event_service.emit_detection_status.assert_any_call(
            self.lot_id, "error", error="No detection results returned from batch processing"
        )
        
        # Check the result contains error information
        self.assertFalse(result["success"])
        self.assertEqual(result["error"], "No detection results returned from batch processing")
        self.assertIn("detection_time", result)


if __name__ == '__main__':
    unittest.main()