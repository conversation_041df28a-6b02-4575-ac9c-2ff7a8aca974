/**
 * Utility functions for polygon operations
 */
class PolygonUtils {
    /**
     * Calculate the center point of a polygon
     * @param {Array} points - Array of points with x,y coordinates
     * @returns {Object} Center point with x,y coordinates
     */
    static calculateCenter(points) {
        const centerX = points.reduce((sum, p) => sum + p.x, 0) / points.length;
        const centerY = points.reduce((sum, p) => sum + p.y, 0) / points.length;
        return {x: centerX, y: centerY};
    }

    /**
     * Update polygon shape CSS based on points
     * @param {HTMLElement} polygonElement - The polygon DOM element
     * @param {Array} points - Array of points with x,y coordinates
     * @param {DOMRect} imageRect - The image bounding rectangle
     */
    static updatePolygonShape(polygonElement, points, imageRect) {
        // Convert points to clip-path polygon format
        const clipPath = `polygon(${points.map(p => `${p.x}px ${p.y}px`).join(', ')})`;
        polygonElement.style.clipPath = clipPath;
        polygonElement.style.webkitClipPath = clipPath;

        // Set position and size to cover the entire image
        polygonElement.style.left = '0';
        polygonElement.style.top = '0';
        polygonElement.style.width = `${imageRect.width}px`;
        polygonElement.style.height = `${imageRect.height}px`;
    }

    /**
     * Create a line element between two points
     * @param {Object} p1 - Starting point with x,y coordinates
     * @param {Object} p2 - Ending point with x,y coordinates
     * @returns {HTMLElement} - The created line element
     */
    static createLineElement(p1, p2) {
        const line = document.createElement('div');
        line.className = 'overlay-line';

        // Calculate line length and position
        const length = Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
        const angle = Math.atan2(p2.y - p1.y, p2.x - p1.x) * 180 / Math.PI;

        // Set line styles
        line.style.width = `${length}px`;
        line.style.left = `${p1.x}px`;
        line.style.top = `${p1.y}px`;
        line.style.transform = `rotate(${angle}deg)`;
        line.style.transformOrigin = '0 0';

        return line;
    }

    /**
     * Create a polygon element with the given points
     * @param {Array} points - Array of points with x,y coordinates
     * @param {string} slotId - Optional ID for the slot
     * @returns {HTMLElement} The created polygon element
     */
    static createPolygon(points, slotId = null) {
        const polygon = document.createElement('div');
        polygon.className = 'zone-polygon';

        if (slotId) {
            polygon.dataset.slotId = slotId;
        }

        const imageRect = document.getElementById('parking-image').getBoundingClientRect();
        this.updatePolygonShape(polygon, points, imageRect);

        return polygon;
    }

    /**
     * Check if a point is inside a polygon using ray casting algorithm
     * @param {Object} point - The point to check with x,y coordinates
     * @param {Array} polygon - Array of points forming the polygon
     * @returns {boolean} True if the point is inside the polygon
     */
    static isPointInPolygon(point, polygon) {
        if (polygon.length < 3) return false;

        let inside = false;
        for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
            const xi = polygon[i].x, yi = polygon[i].y;
            const xj = polygon[j].x, yj = polygon[j].y;

            const intersect = ((yi > point.y) !== (yj > point.y))
                && (point.x < (xj - xi) * (point.y - yi) / (yj - yi) + xi);

            if (intersect) inside = !inside;
        }

        return inside;
    }

    /**
     * Calculate the area of a polygon
     * @param {Array} points - Array of points with x,y coordinates
     * @returns {number} The area of the polygon
     */
    static calculateArea(points) {
        if (points.length < 3) return 0;

        let area = 0;
        for (let i = 0, j = points.length - 1; i < points.length; j = i++) {
            area += (points[j].x + points[i].x) * (points[j].y - points[i].y);
        }

        return Math.abs(area / 2);
    }

    /**
     * Convert pixel coordinates to percentage coordinates relative to image
     * @param {Array} points - Array of points with pixel coordinates
     * @param {number} imageWidth - Width of the image
     * @param {number} imageHeight - Height of the image
     * @returns {Array} Array of points with percentage coordinates
     */
    static pixelsToPercentage(points, imageWidth, imageHeight) {
        return points.map(point => ({
            x: (point.x / imageWidth) * 100,
            y: (point.y / imageHeight) * 100
        }));
    }

    /**
     * Convert percentage coordinates to pixel coordinates
     * @param {Array} points - Array of points with percentage coordinates
     * @param {number} imageWidth - Width of the image
     * @param {number} imageHeight - Height of the image
     * @returns {Array} Array of points with pixel coordinates
     */
    static percentageToPixels(points, imageWidth, imageHeight) {
        return points.map(point => ({
            x: (point.x * imageWidth) / 100,
            y: (point.y * imageHeight) / 100
        }));
    }

    /**
     * Create a label element for a polygon
     * @param {string} text - The label text
     * @param {Array} points - Array of points with x,y coordinates
     * @param {string} zoneId - ID for the zone/slot
     * @returns {HTMLElement} The created label element
     */
    static createLabel(text, points, zoneId) {
        const center = this.calculateCenter(points);
        const label = document.createElement('div');

        label.className = 'zone-label';
        label.textContent = text;
        label.dataset.zoneId = zoneId;
        label.style.left = `${center.x}px`;
        label.style.top = `${center.y}px`;

        // Store percentage position for responsive repositioning
        const image = document.getElementById('parking-image');
        const centerPercent = {
            x: (center.x / image.offsetWidth) * 100,
            y: (center.y / image.offsetHeight) * 100
        };

        label.dataset.centerXPercent = centerPercent.x;
        label.dataset.centerYPercent = centerPercent.y;

        return label;
    }

    /**
     * Check if two polygons intersect
     * @param {Array} poly1 - First polygon (array of points)
     * @param {Array} poly2 - Second polygon (array of points)
     * @returns {boolean} True if polygons intersect
     */
    static doPolygonsIntersect(poly1, poly2) {
        // Check if any point of poly1 is inside poly2
        for (const point of poly1) {
            if (this.isPointInPolygon(point, poly2)) {
                return true;
            }
        }

        // Check if any point of poly2 is inside poly1
        for (const point of poly2) {
            if (this.isPointInPolygon(point, poly1)) {
                return true;
            }
        }

        // Check if any edges intersect
        for (let i = 0; i < poly1.length; i++) {
            const p1 = poly1[i];
            const p2 = poly1[(i + 1) % poly1.length];

            for (let j = 0; j < poly2.length; j++) {
                const p3 = poly2[j];
                const p4 = poly2[(j + 1) % poly2.length];

                if (this.doLineSegmentsIntersect(p1, p2, p3, p4)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Check if two line segments intersect
     * @param {Object} p1 - Start point of first line
     * @param {Object} p2 - End point of first line
     * @param {Object} p3 - Start point of second line
     * @param {Object} p4 - End point of second line
     * @returns {boolean} True if the line segments intersect
     */
    static doLineSegmentsIntersect(p1, p2, p3, p4) {
        const d1 = this.direction(p3, p4, p1);
        const d2 = this.direction(p3, p4, p2);
        const d3 = this.direction(p1, p2, p3);
        const d4 = this.direction(p1, p2, p4);

        // Check if line segments intersect
        if (((d1 > 0 && d2 < 0) || (d1 < 0 && d2 > 0)) &&
            ((d3 > 0 && d4 < 0) || (d3 < 0 && d4 > 0))) {
            return true;
        }

        // Check for colinearity and overlap
        if (d1 === 0 && this.isOnSegment(p3, p4, p1)) return true;
        if (d2 === 0 && this.isOnSegment(p3, p4, p2)) return true;
        if (d3 === 0 && this.isOnSegment(p1, p2, p3)) return true;
        if (d4 === 0 && this.isOnSegment(p1, p2, p4)) return true;

        return false;
    }

    /**
     * Calculate the direction of three points (cross product)
     * @param {Object} a - First point
     * @param {Object} b - Second point
     * @param {Object} c - Third point
     * @returns {number} Direction value (positive, negative, or zero)
     */
    static direction(a, b, c) {
        return (b.x - a.x) * (c.y - a.y) - (c.x - a.x) * (b.y - a.y);
    }

    /**
     * Check if point c is on segment ab
     * @param {Object} a - Start point of segment
     * @param {Object} b - End point of segment
     * @param {Object} c - Point to check
     * @returns {boolean} True if c is on segment ab
     */
    static isOnSegment(a, b, c) {
        return (c.x <= Math.max(a.x, b.x) && c.x >= Math.min(a.x, b.x) &&
                c.y <= Math.max(a.y, b.y) && c.y >= Math.min(a.y, b.y));
    }
}
