import os
from datetime import datetime
from sqlalchemy import create_engine, func
from sqlalchemy.orm import sessionmaker, Session, joinedload
from contextlib import contextmanager
from typing import List, Optional
import urllib

# Assuming models are in the parent 'models' directory relative to 'utils'
from src.models.parking import ParkingLot, ParkingSlot
from src.models.geometry import Polygon, Point
from src.models.detection import DetectionEngineConfig
from src.models.events import (
    BaseEvent, ParkingLotCreated, ParkingLotDeleted, ParkingLotRenamed,
    ParkingLotImageUpdated, ParkingSlotAdded, ParkingSlotRemoved,
    ParkingSlotZoneUpdated, ParkingSlotThresholdUpdated,
    Parking<PERSON>lotOccupancyChanged, DetectionConfigCreated,
    DetectionConfigUpdated, DetectionConfigDeleted
)
from src.models.orm import (
    Base, ParkingLotORM, ParkingSlotORM, DetectionZoneORM, PolygonPointORM,
    ParkingEventORM, DetectionEngineConfigORM
)
from src.utils.logger import setup_logger
from src.utils.mapper import Mapper

logger = setup_logger(__name__)

class AzureSQLStorage:
    """SQLAlchemy-based repository implementing event sourcing principles."""
    def __init__(self, singleton=False):
        # --- Get connection string from environment variable ---
        connection_string = os.environ.get('AZURE_SQL_CONNECTION_STRING')

        if not connection_string:
            raise ValueError("Missing required Azure SQL environment variable: AZURE_SQL_CONNECTION_STRING")

        # Basic parsing to hide sensitive info for logging
        log_conn_string = connection_string
        try:
            parts = {p.split('=', 1)[0].lower(): p.split('=', 1)[1] for p in connection_string.split(';') if '=' in p}
            if 'pwd' in parts: parts['pwd'] = '***'
            log_conn_string = ';'.join([f"{k}={v}" for k, v in parts.items()])
        except Exception:
            log_conn_string = "Could not parse connection string for safe logging"

        logger.info(f"Configuring Azure SQL connection using Connection String: {log_conn_string}")

        # --- Construct database URL for SQLAlchemy using odbc_connect ---
        quoted_connection_string = urllib.parse.quote_plus(connection_string)
        db_url = f"mssql+pyodbc:///?odbc_connect={quoted_connection_string}"

        try:
            # Use the db_url directly with create_engine
            self.engine = create_engine(db_url)
            self.SessionLocal = sessionmaker(bind=self.engine)

            # Test connection
            with self.engine.connect() as connection:
                logger.info("Successfully connected to Azure SQL database using connection string.")

            Base.metadata.create_all(self.engine) # Create tables if they don't exist
        except Exception as e:
            logger.error(f"Failed to initialize Azure SQL engine or database schema: {e}")
            raise

        self._singleton = singleton
        if singleton:
            AzureSQLStorage._instance = self
        logger.info(f"AzureSQLStorage initialized using Connection String.")

    @classmethod
    def instance(cls):
        if not hasattr(cls, '_instance'):
            cls._instance = AzureSQLStorage(singleton=True)
        return cls._instance

    @contextmanager
    def uow(self):
        """Provides a transactional scope around a series of operations."""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Session rollback due to error: {e}")
            raise
        finally:
            session.close()

    def _get_next_sequence_id(self, session: Session, lot_id: str) -> int:
        """Gets the next sequence ID for a given lot_id."""
        max_sequence_id = session.query(func.max(ParkingEventORM.sequence_id))\
            .filter(ParkingEventORM.lot_id == lot_id)\
            .scalar()
        return (max_sequence_id or 0) + 1

    def add_event(self, event: BaseEvent) -> bool:
        """Adds a new domain event to the event store."""
        logger.debug(f"Adding event for lot {event.lot_id}: {event.event_type}")
        with self.uow() as session:
            try:
                sequence_id = self._get_next_sequence_id(session, event.lot_id)
                event.sequence_id = sequence_id # Assign sequence ID back to event object

                event_orm = ParkingEventORM(
                    lot_id=event.lot_id,
                    sequence_id=sequence_id,
                    event_type=event.event_type,
                    payload=event.payload,
                    timestamp=event.timestamp
                )
                session.add(event_orm)
                logger.info(f"Event added: {event.event_type} (Seq: {sequence_id}) for lot {event.lot_id}")
                return True
            except Exception as e:
                logger.error(f"Failed to add event {event.event_type} for lot {event.lot_id}: {e}")
                return False

    def get_events_for_lot(self, lot_id: str) -> List[BaseEvent]:
        """Retrieves all events for a specific lot, ordered by sequence."""
        logger.debug(f"Fetching all events for lot {lot_id}")
        with self.uow() as session:
            events_orm = session.query(ParkingEventORM)\
                .filter_by(lot_id=lot_id)\
                .order_by(ParkingEventORM.sequence_id.asc())\
                .all()
            logger.info(f"Fetched {len(events_orm)} events for lot {lot_id}")
            # TODO: Need a robust way to map ORM back to specific BaseEvent subclasses (Mapper update)
            return [Mapper.orm_to_dataclass_event(e) for e in events_orm] # Needs Mapper update

    # --- State Reconstruction ---
    def get_parking_lot(self, lot_id: str) -> Optional[ParkingLot]:
        """Reconstructs the state of a parking lot by replaying its events."""
        logger.debug(f"Reconstructing parking lot state for id={lot_id}")
        events = self.get_events_for_lot(lot_id)
        if not events:
            logger.info(f"No events found for parking lot: {lot_id}. Assuming it doesn't exist.")
            return None

        lot_state: Optional[ParkingLot] = None
        for event in events:
            if isinstance(event, ParkingLotCreated):
                lot_state = ParkingLot(
                    id=event.lot_id,
                    name=event.payload.get('name', 'Unnamed Lot'),
                    image_path=event.payload.get('image_path'),
                    slots=[],
                    detection_config=None
                )
            elif lot_state is None:
                logger.error(f"Cannot process event {event.event_type} (Seq: {event.sequence_id}) for lot {lot_id} before creation.")
                continue
            elif isinstance(event, ParkingLotDeleted):
                logger.info(f"Parking lot {lot_id} was deleted at sequence {event.sequence_id}.")
                return None
            else:
                # TODO: Implement apply method on ParkingLot dataclass
                # lot_state.apply(event)
                # Placeholder for applying other events:
                if isinstance(event, ParkingLotRenamed):
                    lot_state.name = event.payload['new_name']
                elif isinstance(event, ParkingLotImageUpdated):
                    lot_state.image_path = event.payload['new_image_path']
                # ... apply other event types ...

        if lot_state:
             logger.info(f"Parking lot state reconstructed for: {lot_id}")
        else:
             logger.warning(f"Parking lot state reconstruction failed for: {lot_id} (likely missing ParkingLotCreated event)")

        return lot_state

    def get_all_parking_lots(self) -> List[ParkingLot]:
        """Reconstructs all currently existing parking lots."""
        logger.debug("Fetching all parking lot IDs to reconstruct state")
        lot_ids = []
        with self.uow() as session:
            distinct_lot_ids = session.query(ParkingEventORM.lot_id).distinct().all()
            lot_ids = [row[0] for row in distinct_lot_ids]
            logger.info(f"Found {len(lot_ids)} distinct lot IDs in event store.")

        all_lots = []
        for lot_id in lot_ids:
            lot = self.get_parking_lot(lot_id)
            if lot:
                all_lots.append(lot)

        logger.info(f"Reconstructed {len(all_lots)} currently existing parking lots.")
        return all_lots

    # --- Command Handlers (Generate Events) ---
    def create_parking_lot(self, lot_id: str, name: str, image_path: Optional[str]) -> bool:
        """Handles the command to create a parking lot by adding an event."""
        logger.info(f"Handling command to create parking lot: {lot_id}")
        with self.uow() as session:
             existing_event = session.query(ParkingEventORM.id).filter_by(lot_id=lot_id).first()
             if existing_event:
                 logger.warning(f"Parking lot {lot_id} already has events. Creation command ignored.")
                 return False

        event = ParkingLotCreated(
            lot_id=lot_id,
            payload={
                "name": name,
                "image_path": image_path
            }
        )
        return self.add_event(event)

    def update_parking_lot(self, lot_id: str, lot_data: ParkingLot) -> bool:
        logger.error("Direct update_parking_lot is incompatible with event sourcing. Use specific commands/events.")
        raise NotImplementedError("Use specific commands (e.g., rename, add_slot) instead of direct update.")

    def delete_parking_lot(self, lot_id: str) -> bool:
        """Handles the command to delete a parking lot by adding an event."""
        logger.info(f"Handling command to delete parking lot: {lot_id}")
        current_state = self.get_parking_lot(lot_id)
        if not current_state:
             logger.warning(f"Parking lot {lot_id} not found or already deleted. Delete command ignored.")
             return False

        event = ParkingLotDeleted(lot_id=lot_id, payload={})
        return self.add_event(event)

    # --- Specific State Change Method (Example) ---
    def update_slot_status(self, lot_id: str, slot_id: str, is_occupied: bool, detection_timestamp: Optional[datetime] = None) -> bool:
        """Handles the command to update a slot's occupancy by adding an event."""
        logger.info(f"Handling command to update slot {slot_id} in lot {lot_id} to {'occupied' if is_occupied else 'free'}")
        # Optional: Check if lot/slot exists
        # ...

        event = ParkingSlotOccupancyChanged(
            lot_id=lot_id,
            payload={
                "slot_id": slot_id,
                "is_occupied": is_occupied,
                "detection_timestamp": detection_timestamp.isoformat() if detection_timestamp else None
            }
        )
        return self.add_event(event)

    # --- Deprecated/Removed Methods ---
    # Remove or comment out methods that directly modify state other than adding events.
    # def get_parking_events(self, lot_id: str, limit: int = 100) -> List[ParkingEvent]:
    #     # This method might still be useful for displaying raw event logs, but should use BaseEvent
    #     # and potentially pagination on sequence_id.
    #     pass

    # def get_detection_config(self, lot_id: str) -> Optional[DetectionEngineConfig]:
    #     # State should be derived from events via get_parking_lot
    #     pass

    # def update_detection_config(self, lot_id: str, config: DetectionEngineConfig) -> bool:
    #     # Should be handled by specific events like DetectionConfigUpdated
    #     pass

