"""Initial migration with sequence_id

Revision ID: 7ff23235e259
Revises: 
Create Date: 2025-07-11 14:29:07.834756

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7ff23235e259'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('detection_engine_config',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('engine_type', sa.String(), nullable=True),
    sa.Column('is_enabled', sa.<PERSON>an(), nullable=True),
    sa.Column('update_interval', sa.Integer(), nullable=True),
    sa.Column('parameters', sa.JSO<PERSON>(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('parking_lot',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('image_path', sa.String(length=1024), nullable=True),
    sa.Column('detection_config_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['detection_config_id'], ['detection_engine_config.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('parking_event',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('sequence_id', sa.Integer(), nullable=False),
    sa.Column('lot_id', sa.String(length=36), nullable=False),
    sa.Column('event_type', sa.String(length=50), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('payload', sa.JSON(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['lot_id'], ['parking_lot.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('lot_id', 'sequence_id', name='_lot_seq_uc')
    )
    op.create_table('parking_slot',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('slot_name', sa.String(length=255), nullable=False),
    sa.Column('lot_id', sa.String(length=36), nullable=False),
    sa.Column('is_occupied', sa.Boolean(), nullable=True),
    sa.Column('overlap_threshold', sa.Float(), nullable=True),
    sa.ForeignKeyConstraint(['lot_id'], ['parking_lot.id'], ),
    sa.PrimaryKeyConstraint('id', 'lot_id'),
    sa.UniqueConstraint('id', 'lot_id', name='_lot_slot_uc')
    )
    op.create_table('detection_zone',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('slot_id', sa.String(length=36), nullable=True),
    sa.Column('lot_id', sa.String(length=36), nullable=True),
    sa.ForeignKeyConstraint(['slot_id', 'lot_id'], ['parking_slot.id', 'parking_slot.lot_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('polygon_point',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('zone_id', sa.Integer(), nullable=True),
    sa.Column('x', sa.Float(), nullable=False),
    sa.Column('y', sa.Float(), nullable=False),
    sa.Column('order', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['zone_id'], ['detection_zone.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('polygon_point')
    op.drop_table('detection_zone')
    op.drop_table('parking_slot')
    op.drop_table('parking_event')
    op.drop_table('parking_lot')
    op.drop_table('detection_engine_config')
    # ### end Alembic commands ###
