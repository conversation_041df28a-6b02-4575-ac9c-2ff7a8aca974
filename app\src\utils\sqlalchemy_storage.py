from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session, joinedload
from contextlib import contextmanager
from typing import List, Optional, Tuple
from src.models.parking import Parking<PERSON>ot, ParkingSlot
from src.models.geometry import Polygon, Point
from src.models.detection import DetectionEngineConfig
from src.models.events import ParkingEvent
from src.models.orm import (
    Base, ParkingLotORM, ParkingSlotORM, DetectionZoneORM, PolygonPointORM,
    ParkingEventORM, DetectionEngineConfigORM
)
import os
from src.utils.logger import setup_logger
from src.utils.mapper import Mapper

logger = setup_logger(__name__)

class SQLAlchemyStorage:
    """SQLAlchemy-based repository for normalized parking lot data."""
    def __init__(self, db_path=None, singleton=False):
        if db_path is None:
            db_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'parking.db')
        
        # Special handling for in-memory databases
        if db_path == ":memory:":
            db_url = "sqlite:///:memory:"
        else:
            # Ensure directory exists for file-based databases
            if not os.path.exists(os.path.dirname(db_path)):
                os.makedirs(os.path.dirname(db_path))
            db_url = f"sqlite:///{db_path}"
            
        self.engine = create_engine(db_url, connect_args={"check_same_thread": False})
        self.SessionLocal = sessionmaker(bind=self.engine)
        Base.metadata.create_all(self.engine)
        self._singleton = singleton
        if singleton:
            SQLAlchemyStorage._instance = self
        logger.info(f"SQLAlchemyStorage initialized with DB: {db_path}")

    @classmethod
    def instance(cls):
        """Get or create the singleton instance."""
        if not hasattr(cls, '_instance') or cls._instance is None:
            cls._instance = cls(singleton=True)
        return cls._instance
            
    @classmethod
    def reset_instance(cls):
        """Reset the singleton instance (for testing purposes)."""
        if hasattr(cls, '_instance'):
            del cls._instance

    @contextmanager
    def uow(self):
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Session rollback due to error: {e}")
            raise
        finally:
            session.close()

    # --- CRUD operations ---
    def get_parking_lot(self, lot_id: str) -> Optional[ParkingLot]:
        logger.debug(f"Fetching parking lot with id={lot_id}")
        with self.uow() as session:
            orm = session.query(ParkingLotORM).options(
                joinedload(ParkingLotORM.slots).joinedload(ParkingSlotORM.detection_zone).joinedload(DetectionZoneORM.points),
                joinedload(ParkingLotORM.detection_config),
                joinedload(ParkingLotORM.events)
            ).filter_by(id=lot_id).first()
            
            if orm is None:
                logger.debug(f"Parking lot not found: {lot_id}")
                return None

            logger.debug(f"Parking lot fetched: {lot_id}")
            return Mapper.orm_to_dataclass_lot(orm)

    def get_all_parking_lots(self) -> List[ParkingLot]:
        logger.debug("Fetching all parking lots")
        with self.uow() as session:
            lots = session.query(ParkingLotORM).options(
                joinedload(ParkingLotORM.slots).joinedload(ParkingSlotORM.detection_zone).joinedload(DetectionZoneORM.points),
                joinedload(ParkingLotORM.detection_config),
                joinedload(ParkingLotORM.events)
            ).all()
            logger.info(f"Fetched {len(lots)} parking lots")
            return [Mapper.orm_to_dataclass_lot(lot) for lot in lots]

    def create_parking_lot(self, lot: ParkingLot) -> bool:
        logger.info(f"Creating parking lot: {lot.id}")
        with self.uow() as session:
            existing_lot = session.query(ParkingLotORM).filter_by(id=lot.id).first()
            if existing_lot:
                logger.warning(f"Parking lot with id {lot.id} already exists. Creation skipped.")
                return False

            orm = ParkingLotORM(
                id=lot.id,
                name=lot.name,
                image_path=lot.image_path
            )
            session.add(orm)
            session.flush()
            for slot in lot.slots:
                slot_orm = ParkingSlotORM(
                    id=slot.id,
                    slot_name=slot.slot_name,
                    lot_id=orm.id,
                    is_occupied=slot.is_occupied,
                    overlap_threshold=slot.overlap_threshold
                )
                session.add(slot_orm)
                if slot.detection_zone:
                    zone_orm = DetectionZoneORM(slot=slot_orm)
                    session.add(zone_orm)
                    session.flush()
                    for idx, pt in enumerate(slot.detection_zone.points):
                        pt_orm = PolygonPointORM(zone_id=zone_orm.id, x=pt.x, y=pt.y, order=idx)
                        session.add(pt_orm)

            for event in lot.events:
                 event_orm = ParkingEventORM(
                     lot_id=orm.id,
                     event_type=event.event_type,
                     description=event.description,
                     payload=event.payload or {},
                     timestamp=event.timestamp
                 )
                 session.add(event_orm)

            if lot.detection_config:
                config_orm = Mapper.dataclass_to_orm_config(lot.detection_config, lot_id=orm.id)
                session.add(config_orm)

            logger.info(f"Parking lot created: {lot.id}")
            return True
        return False

    def update_parking_lot(self, lot_id: str, lot: ParkingLot) -> bool:
        logger.info(f"Updating parking lot: {lot_id}")
        with self.uow() as session:
            orm = session.query(ParkingLotORM).filter_by(id=lot_id).first()

            if not orm:
                logger.warning(f"Parking lot not found for update: {lot_id}")
                return False

            orm.name = lot.name
            orm.image_path = lot.image_path

            orm.slots.clear()
            session.flush()

            for slot_data in lot.slots:
                slot_orm = ParkingSlotORM(
                    id=slot_data.id,
                    lot_id=orm.id,
                    is_occupied=slot_data.is_occupied,
                    overlap_threshold=slot_data.overlap_threshold
                )
                session.add(slot_orm)
                if slot_data.detection_zone:
                    zone_orm = DetectionZoneORM(slot=slot_orm)
                    session.add(zone_orm)
                    session.flush()
                    for idx, pt in enumerate(slot_data.detection_zone.points):
                        pt_orm = PolygonPointORM(zone_id=zone_orm.id, x=pt.x, y=pt.y, order=idx)
                        session.add(pt_orm)

            if orm.detection_config:
                 session.delete(orm.detection_config)
                 orm.detection_config = None
                 session.flush()

            if lot.detection_config:
                 new_config_orm = Mapper.dataclass_to_orm_config(lot.detection_config, lot_id=orm.id)
                 session.add(new_config_orm)

            orm.events.clear()
            session.flush()
            for event_data in lot.events:
                event_orm = ParkingEventORM(
                    lot_id=orm.id,
                    event_type=event_data.event_type,
                    description=event_data.description,
                    payload=event_data.payload or {},
                    timestamp=event_data.timestamp
                )
                session.add(event_orm)

            logger.info(f"Parking lot updated: {lot_id}")
            return True
        return False

    def delete_parking_lot(self, lot_id: str) -> bool:
        logger.info(f"Deleting parking lot: {lot_id}")
        with self.uow() as session:
            orm = session.query(ParkingLotORM).filter_by(id=lot_id).first()
            if orm:
                session.delete(orm)
                logger.info(f"Parking lot deleted: {lot_id}")
                return True
            logger.warning(f"Parking lot not found for delete: {lot_id}")
            return False
        return False

    # --- Event specific methods ---
    def add_parking_event(self, event: ParkingEvent) -> bool:
        logger.debug(f"Adding parking event for lot {event.lot_id}: {event.event_type}")
        with self.uow() as session:
            lot_exists = session.query(ParkingLotORM.id).filter_by(id=event.lot_id).first() is not None
            if not lot_exists:
                 logger.error(f"Cannot add event, parking lot {event.lot_id} not found.")
                 return False

            event_orm = ParkingEventORM(
                lot_id=event.lot_id,
                event_type=event.event_type,
                description=event.description,
                payload=event.payload or {},
                timestamp=event.timestamp
            )
            session.add(event_orm)
            logger.info(f"Parking event added: {event.event_type} for lot {event.lot_id}")
            return True
        return False

    # --- Config specific methods ---
    def get_detection_config(self, lot_id: str) -> Optional[DetectionEngineConfig]:
        logger.debug(f"Fetching detection config for lot {lot_id}")
        with self.uow() as session:
            config_orm = session.query(DetectionEngineConfigORM).filter_by(lot_id=lot_id).first()
            if config_orm:
                logger.info(f"Detection config found for lot {lot_id}")
                return Mapper.orm_to_dataclass_config(config_orm)
            else:
                logger.info(f"No detection config found for lot {lot_id}")
                return None

    def update_detection_config(self, lot_id: str, config: DetectionEngineConfig) -> bool:
        logger.info(f"Updating detection config for lot {lot_id}")
        with self.uow() as session:
            orm = session.query(DetectionEngineConfigORM).filter_by(lot_id=lot_id).first()
            if orm:
                orm.engine_type = config.engine_type
                orm.is_enabled = config.is_enabled
                orm.update_interval = config.update_interval
                orm.parameters = config.parameters or {}
            else:
                lot_exists = session.query(ParkingLotORM.id).filter_by(id=lot_id).first() is not None
                if not lot_exists:
                    logger.error(f"Cannot update/create config, parking lot {lot_id} not found.")
                    return False
                orm = Mapper.dataclass_to_orm_config(config, lot_id=lot_id)
                session.add(orm)
            logger.info(f"Detection config updated for lot {lot_id}")
            return True
        return False

    # --- Event retrieval ---
    def get_parking_events(self, lot_id: str, page: int = 1, per_page: int = 50) -> Tuple[List[ParkingEvent], int]:
        logger.debug(f"Fetching events for lot {lot_id}, page={page}, per_page={per_page}")
        with self.uow() as session:
            query = session.query(ParkingEventORM).filter_by(lot_id=lot_id)

            total = query.count()

            events_orm = query.order_by(ParkingEventORM.timestamp.desc())\
                              .offset((page - 1) * per_page)\
                              .limit(per_page)\
                              .all()

            logger.info(f"Fetched {len(events_orm)} events (total: {total}) for lot {lot_id}")
            events_dataclass = [Mapper.orm_to_dataclass_event(e) for e in events_orm]
            return events_dataclass, total
