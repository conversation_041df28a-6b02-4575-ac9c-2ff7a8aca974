"""
Unit tests for the DetectionManager class.
"""
import unittest
from unittest.mock import MagicMock, patch, call, ANY
import threading
import time

from app.src.models.detection import DetectionEngineConfig, DetectionResult
from app.src.models.parking import ParkingLot, ParkingSlot, Polygon, Point
from app.src.services.detection_manager import DetectionManager
from app.src.services.mock_detection_engine import MockDetectionEngine
from app.src.services.yolo_detection_engine import YOLODetectionEngine
from app.src.config.settings import get_settings


class TestDetectionManager(unittest.TestCase):
    """Test suite for the DetectionManager."""

    @classmethod
    def setUpClass(cls):
        """Set up test configuration using settings."""
        # Configure settings for testing
        settings = get_settings()
        settings.model_path = 'dummy_model.pt'
        settings.yolo_model_path = 'dummy_model.pt'

    @classmethod
    def tearDownClass(cls):
        """Clean up after tests."""
        pass


    def setUp(self):
        """Set up test fixtures."""
        # Reset DetectionManager internal state before each test
        DetectionManager._engines = {}
        DetectionManager._detection_threads = {}
        DetectionManager._stop_events = {}

        # Save original engine types
        self.original_engine_types = DetectionManager._engine_types.copy()

        # Mock dependencies
        self.storage_patcher = patch('app.src.services.detection_manager.StorageService')
        self.mock_storage = self.storage_patcher.start()

        # Patch the engine classes within the detection_manager module's scope
        self.mock_yolo_patcher = patch('app.src.services.detection_manager.YOLODetectionEngine')
        self.mock_yolo_class = self.mock_yolo_patcher.start()
        self.mock_yolo_instance = self.mock_yolo_class.return_value
        self.mock_yolo_instance.is_available.return_value = True
        self.mock_yolo_instance.get_classes.return_value = ['car', 'truck']
        # Ensure slot_name is included in the mocked DetectionResult
        self.mock_yolo_instance.detect.return_value = [DetectionResult(slot_id='slot-1', slot_name='slot-name-1', is_occupied=True, confidence=0.9)]
        # Mock the configure method on the instance
        self.mock_yolo_instance.configure = MagicMock(return_value=True)

        self.mock_mock_engine_patcher = patch('app.src.services.detection_manager.MockDetectionEngine')
        self.mock_mock_engine_class = self.mock_mock_engine_patcher.start()
        self.mock_mock_engine_instance = self.mock_mock_engine_class.return_value
        self.mock_mock_engine_instance.is_available.return_value = True
        self.mock_mock_engine_instance.get_classes.return_value = ['car', 'bike']
        # Ensure slot_name is included in the mocked DetectionResult
        self.mock_mock_engine_instance.detect.return_value = [DetectionResult(slot_id='slot-1', slot_name='slot-name-1', is_occupied=False, confidence=0.8)]
        # Mock the configure method on the instance
        self.mock_mock_engine_instance.configure = MagicMock(return_value=True)

        # Update the engine types dictionary with the mocked classes
        DetectionManager._engine_types = {
            "mock": self.mock_mock_engine_class,
            "yolo": self.mock_yolo_class
        }

        # Instead of trying to mock the engine classes, we'll use the real ones
        # and just verify the behavior of the DetectionManager class
        # This is a simpler approach that avoids issues with isinstance() checks

        self.thread_patcher = patch('app.src.services.detection_manager.threading.Thread')
        self.mock_thread_class = self.thread_patcher.start()
        self.mock_thread_instance = self.mock_thread_class.return_value
        # Configure is_alive to return False for test_detection_loop
        self.mock_thread_instance.is_alive.return_value = False

        self.event_patcher = patch('app.src.services.detection_manager.threading.Event')
        self.mock_event_class = self.event_patcher.start()
        self.mock_event_instance = self.mock_event_class.return_value

        self.occupancy_patcher = patch('app.src.services.detection_manager.OccupancyManager')
        self.mock_occupancy_manager = self.occupancy_patcher.start()
        self.mock_occupancy_manager.update_from_detection_results.return_value = {'slot-1': True}

        # Patch the handle_slot_status_update function in routes.events
        # This is imported inside the run_single_detection method
        self.handle_slot_update_patcher = patch('app.src.routers.events.handle_slot_status_update')
        self.mock_handle_slot_update = self.handle_slot_update_patcher.start()

        # Sample data
        self.lot_id = "test-lot-1"
        # Add slot_name to ParkingSlot instantiation
        self.slot1 = ParkingSlot(id='slot-1', slot_name='S1', detection_zone=Polygon(points=[Point(x=0, y=0), Point(x=10, y=0), Point(x=10, y=10), Point(x=0, y=10)]), is_occupied=False)
        self.parking_lot = ParkingLot(id=self.lot_id, name="Test Lot", slots=[self.slot1])
        self.yolo_config = DetectionEngineConfig(engine_type='yolo', is_enabled=True, update_interval=30, parameters={'model_path': 'yolo.pt'})
        self.mock_config = DetectionEngineConfig(engine_type='mock', is_enabled=True, update_interval=60, parameters={'seed': 42})

        self.mock_storage.get_parking_lot.return_value = self.parking_lot


    def tearDown(self):
        """Tear down test fixtures."""
        self.storage_patcher.stop()
        self.mock_yolo_patcher.stop()
        self.mock_mock_engine_patcher.stop()
        self.thread_patcher.stop()
        self.event_patcher.stop()
        self.occupancy_patcher.stop()
        self.handle_slot_update_patcher.stop()

        # Restore original engine types
        DetectionManager._engine_types = self.original_engine_types

        # Ensure all threads are stopped if any test fails mid-way
        for lot_id in list(DetectionManager._stop_events.keys()):
            DetectionManager.stop_detection(lot_id)

    def test_get_engine_instance_existing(self):
        """Test getting an already instantiated engine."""
        DetectionManager._engines[self.lot_id] = self.mock_yolo_instance
        engine = DetectionManager.get_engine_instance(self.lot_id)
        self.assertIs(engine, self.mock_yolo_instance)
        self.mock_storage.get_parking_lot.assert_not_called()

    def test_get_engine_instance_create_from_config_dict(self):
        """Test creating a new engine instance from a dict config."""
        self.parking_lot.detection_config = self.yolo_config
        self.mock_storage.get_parking_lot.return_value = self.parking_lot
        engine = DetectionManager.get_engine_instance(self.lot_id)
        self.assertIsInstance(engine, MagicMock) # It's the mocked YOLODetectionEngine instance
        self.mock_yolo_class.assert_called_once()
        self.assertIn(self.lot_id, DetectionManager._engines)
        self.assertIs(DetectionManager._engines[self.lot_id], engine)

    def test_get_engine_instance_create_from_config_object(self):
        """Test creating a new engine instance from a config object."""
        self.parking_lot.detection_config = self.yolo_config # Use object
        self.mock_storage.get_parking_lot.return_value = self.parking_lot
        engine = DetectionManager.get_engine_instance(self.lot_id)
        self.assertIsInstance(engine, MagicMock)
        self.mock_yolo_class.assert_called_once()
        self.assertIn(self.lot_id, DetectionManager._engines)

    def test_get_engine_instance_no_lot(self):
        """Test getting engine when lot doesn't exist."""
        self.mock_storage.get_parking_lot.return_value = None
        engine = DetectionManager.get_engine_instance("nonexistent-lot")
        self.assertIsNone(engine)

    def test_get_engine_instance_no_config(self):
        """Test getting engine when lot has no config."""
        self.parking_lot.detection_config = None
        self.mock_storage.get_parking_lot.return_value = self.parking_lot
        engine = DetectionManager.get_engine_instance(self.lot_id)
        self.assertIsNone(engine)

    def test_get_engine_instance_unknown_type(self):
        """Test getting engine with an unknown type in config."""
        self.parking_lot.detection_config = DetectionEngineConfig(engine_type='unknown', is_enabled=True)
        self.mock_storage.get_parking_lot.return_value = self.parking_lot
        engine = DetectionManager.get_engine_instance(self.lot_id)
        self.assertIsNone(engine)

    def test_get_classes_success(self):
        """Test getting classes from an available engine."""
        DetectionManager._engines[self.lot_id] = self.mock_yolo_instance
        classes = DetectionManager.get_classes(self.lot_id)
        self.assertEqual(classes, ['car', 'truck'])
        self.mock_yolo_instance.get_classes.assert_called_once()

    def test_get_classes_no_engine(self):
        """Test getting classes when no engine instance exists."""
        self.parking_lot.detection_config = None # Ensure no engine is created
        self.mock_storage.get_parking_lot.return_value = self.parking_lot
        classes = DetectionManager.get_classes(self.lot_id)
        self.assertEqual(classes, [])

    def test_get_available_engine_types(self):
        """Test getting the list of available engine types."""
        types = DetectionManager.get_available_engine_types()
        self.assertIn("mock", types)
        self.assertIn("yolo", types)

    def test_get_engine_config_success_dict(self):
        """Test getting engine config when it's a dict."""
        # This test was originally for config as dict, but type hints require DetectionEngineConfig object.
        # We'll adapt it to test with the object directly.
        self.parking_lot.detection_config = self.yolo_config 
        self.mock_storage.get_parking_lot.return_value = self.parking_lot
        config = DetectionManager.get_engine_config(self.lot_id)
        self.assertIsNotNone(config) # Ensure config is not None
        if config: # Added for type safety, though assertIsNotNone should guarantee it
            self.assertIsInstance(config, DetectionEngineConfig)
            self.assertEqual(config.engine_type, 'yolo')
            self.assertEqual(config.parameters['model_path'], 'yolo.pt')

    def test_get_engine_config_success_object(self):
        """Test getting engine config when it's an object."""
        self.parking_lot.detection_config = self.yolo_config
        self.mock_storage.get_parking_lot.return_value = self.parking_lot
        config = DetectionManager.get_engine_config(self.lot_id)
        self.assertIs(config, self.yolo_config)

    def test_get_engine_config_no_lot(self):
        """Test getting config when lot doesn't exist."""
        self.mock_storage.get_parking_lot.return_value = None
        config = DetectionManager.get_engine_config("nonexistent-lot")
        self.assertIsNone(config)

    def test_get_engine_config_no_config(self):
        """Test getting config when lot has no config."""
        self.parking_lot.detection_config = None
        self.mock_storage.get_parking_lot.return_value = self.parking_lot
        config = DetectionManager.get_engine_config(self.lot_id)
        self.assertIsNone(config)

    def test_get_default_config_yolo_available(self):
        """Test getting default config when YOLO is available."""
        self.mock_yolo_instance.is_available.return_value = True
        config = DetectionManager.get_default_config(self.lot_id)
        self.assertEqual(config.engine_type, 'yolo')
        self.mock_yolo_class.assert_called_once() # Checks if YOLO was attempted

    def test_get_default_config_yolo_unavailable(self):
        """Test getting default config falls back to mock if YOLO fails."""
        self.mock_yolo_class.side_effect = Exception("YOLO load failed")
        config = DetectionManager.get_default_config(self.lot_id)
        self.assertEqual(config.engine_type, 'mock')
        self.assertEqual(config.parameters['random_seed'], 42)

    def test_configure_engine_new_yolo(self):
        """Test configuring a new YOLO engine."""
        self.mock_storage.create_detection_config.return_value = True
        success = DetectionManager.configure_engine(self.lot_id, self.yolo_config)
        self.assertTrue(success)
        self.mock_storage.create_detection_config.assert_called_once_with(
            lot_id=self.lot_id,
            engine_type='yolo',
            is_enabled=True,
            update_interval=30,
            parameters={'model_path': 'yolo.pt'}
        )
        self.assertIn(self.lot_id, DetectionManager._engines)
        self.mock_yolo_class.assert_called_once()
        self.mock_yolo_instance.configure.assert_called_once_with({'model_path': 'yolo.pt'})

    def test_configure_engine_update_to_mock(self):
        """Test reconfiguring from YOLO to Mock engine."""
        # Setup initial YOLO engine
        DetectionManager._engines[self.lot_id] = self.mock_yolo_instance
        self.mock_storage.create_detection_config.return_value = True

        success = DetectionManager.configure_engine(self.lot_id, self.mock_config)
        self.assertTrue(success)
        self.mock_storage.create_detection_config.assert_called_with(
            lot_id=self.lot_id,
            engine_type='mock',
            is_enabled=True,
            update_interval=60,
            parameters={'seed': 42}
        )
        self.assertIn(self.lot_id, DetectionManager._engines)
        # Check that the *new* engine instance was created and configured
        self.mock_mock_engine_class.assert_called_once()
        new_mock_engine_instance = DetectionManager._engines[self.lot_id]
        self.assertIs(new_mock_engine_instance, self.mock_mock_engine_instance) # Ensure it's the mocked instance
        self.mock_mock_engine_instance.configure.assert_called_once_with({'seed': 42})
        self.mock_yolo_class.assert_not_called() # No new YOLO engine

    def test_configure_engine_no_lot(self):
        """Test configuring engine when lot doesn't exist."""
        self.mock_storage.get_parking_lot.return_value = None
        success = DetectionManager.configure_engine("nonexistent-lot", self.yolo_config)
        self.assertFalse(success)
        self.mock_storage.create_detection_config.assert_not_called()

    def test_configure_engine_unknown_type(self):
        """Test configuring engine with unknown type."""
        unknown_config = DetectionEngineConfig(engine_type='unknown', is_enabled=True)
        success = DetectionManager.configure_engine(self.lot_id, unknown_config)
        self.assertFalse(success)
        self.mock_storage.create_detection_config.assert_not_called()

    def test_configure_engine_storage_failure(self):
        """Test configuring engine when storage update fails."""
        self.mock_storage.create_detection_config.return_value = False
        success = DetectionManager.configure_engine(self.lot_id, self.yolo_config)
        self.assertFalse(success)
        self.mock_storage.create_detection_config.assert_called_once()

    def test_configure_engine_stops_existing_thread(self):
        """Test that configuring an engine stops an existing detection thread."""
        # Simulate running thread
        DetectionManager._stop_events[self.lot_id] = self.mock_event_instance
        DetectionManager._detection_threads[self.lot_id] = self.mock_thread_instance
        # self.mock_thread_instance.is_alive is already a MagicMock from setup, 
        # its return_value can be set directly.
        self.mock_thread_instance.is_alive.return_value = True 
        self.mock_storage.create_detection_config.return_value = True

        DetectionManager.configure_engine(self.lot_id, self.yolo_config)

        self.mock_event_instance.set.assert_called_once()
        self.mock_thread_instance.join.assert_called_once_with(timeout=2.0)
        self.assertNotIn(self.lot_id, DetectionManager._detection_threads)
        self.assertNotIn(self.lot_id, DetectionManager._stop_events)

    def test_start_detection_success(self):
        """Test starting detection successfully."""
        self.parking_lot.detection_config = self.yolo_config
        self.mock_storage.get_parking_lot.return_value = self.parking_lot

        success = DetectionManager.start_detection(self.lot_id)

        self.assertTrue(success)
        self.mock_event_class.assert_called_once()
        self.mock_thread_class.assert_called_once_with(
            target=DetectionManager._detection_loop,
            args=(self.lot_id, self.mock_event_instance),
            daemon=True
        )
        self.mock_thread_instance.start.assert_called_once()
        self.assertIn(self.lot_id, DetectionManager._detection_threads)
        self.assertIn(self.lot_id, DetectionManager._stop_events)

    def test_start_detection_no_lot(self):
        """Test starting detection when lot doesn't exist."""
        self.mock_storage.get_parking_lot.return_value = None
        success = DetectionManager.start_detection(self.lot_id)
        self.assertFalse(success)
        self.mock_thread_class.assert_not_called()

    def test_start_detection_no_config(self):
        """Test starting detection when lot has no config."""
        self.parking_lot.detection_config = None
        self.mock_storage.get_parking_lot.return_value = self.parking_lot
        success = DetectionManager.start_detection(self.lot_id)
        self.assertFalse(success)
        self.mock_thread_class.assert_not_called()

    def test_start_detection_not_enabled(self):
        """Test starting detection when config is not enabled."""
        self.yolo_config.is_enabled = False
        self.parking_lot.detection_config = self.yolo_config
        self.mock_storage.get_parking_lot.return_value = self.parking_lot
        success = DetectionManager.start_detection(self.lot_id)
        self.assertFalse(success)
        self.mock_thread_class.assert_not_called()

    def test_start_detection_stops_existing(self):
        """Test starting detection stops an existing thread first."""
        # Simulate running thread
        mock_existing_stop_event = MagicMock()
        mock_existing_thread = MagicMock()
        mock_existing_thread.is_alive.return_value = True # Correctly set return_value on the MagicMock

        DetectionManager._stop_events[self.lot_id] = mock_existing_stop_event
        DetectionManager._detection_threads[self.lot_id] = mock_existing_thread
        
        self.parking_lot.detection_config = self.yolo_config
        self.mock_storage.get_parking_lot.return_value = self.parking_lot

        with patch.object(DetectionManager, 'stop_detection', wraps=DetectionManager.stop_detection) as mock_stop:
            DetectionManager.start_detection(self.lot_id)
            mock_stop.assert_called_once_with(self.lot_id)

        # New thread and event mocks are created by the setUp method's patchers
        # self.mock_thread_class is the mock for threading.Thread
        # self.mock_event_class is the mock for threading.Event
        self.mock_event_class.assert_called_once() # New event created for the new thread
        self.mock_thread_class.assert_called_once() # New thread started
        # self.mock_thread_instance is the instance returned by self.mock_thread_class()
        self.mock_thread_instance.start.assert_called_once()

    def test_stop_detection_success(self):
        """Test stopping a running detection thread."""
        # Simulate running thread
        DetectionManager._stop_events[self.lot_id] = self.mock_event_instance
        DetectionManager._detection_threads[self.lot_id] = self.mock_thread_instance
        self.mock_thread_instance.is_alive.return_value = True

        success = DetectionManager.stop_detection(self.lot_id)

        self.assertTrue(success)
        self.mock_event_instance.set.assert_called_once()
        self.mock_thread_instance.join.assert_called_once_with(timeout=2.0)
        self.assertNotIn(self.lot_id, DetectionManager._detection_threads)
        self.assertNotIn(self.lot_id, DetectionManager._stop_events)

    def test_stop_detection_not_running(self):
        """Test stopping detection when no thread is running."""
        success = DetectionManager.stop_detection(self.lot_id)
        self.assertFalse(success)
        self.mock_event_instance.set.assert_not_called()
        self.mock_thread_instance.join.assert_not_called()

    def test_run_single_detection_yolo_success(self):
        """Test running a single detection with YOLO engine."""
        self.parking_lot.detection_config = self.yolo_config
        # Use the slot_name from the ParkingSlot object for consistency in the event
        initial_slot = ParkingSlot(id='slot-1', slot_name='S1', detection_zone=self.slot1.detection_zone, is_occupied=False)
        updated_slot = ParkingSlot(id='slot-1', slot_name='S1', detection_zone=self.slot1.detection_zone, is_occupied=True)
        
        initial_lot_obj = ParkingLot(id=self.lot_id, name="Test Lot", slots=[initial_slot])
        updated_lot_obj = ParkingLot(id=self.lot_id, name="Test Lot", slots=[updated_slot])
        
        self.mock_storage.get_parking_lot.side_effect = [initial_lot_obj, updated_lot_obj]

        DetectionManager._engines[self.lot_id] = self.mock_yolo_instance
        # Ensure slot_name in DetectionResult matches the slot_name of the ParkingSlot for the event check
        self.mock_yolo_instance.detect.return_value = [DetectionResult(slot_id='slot-1', slot_name=initial_slot.slot_name, is_occupied=True, confidence=0.9)]

        updates = DetectionManager.run_single_detection(self.lot_id)

        self.mock_yolo_instance.detect.assert_called_once_with(self.lot_id)
        self.mock_occupancy_manager.update_from_detection_results.assert_called_once_with(
            self.lot_id, [DetectionResult(slot_id='slot-1', slot_name=initial_slot.slot_name, is_occupied=True, confidence=0.9)]
        )
        self.assertEqual(updates, {'slot-1': True})
        # The event should be called with the slot_name from the ParkingSlot object
        self.mock_handle_slot_update.assert_called_once_with(self.lot_id, initial_slot.slot_name, True)

    def test_run_single_detection_creates_engine_from_config(self):
        """Test that run_single_detection creates engine if not present, using config."""
        # Changed from self.mock_config.dict() to self.mock_config
        self.parking_lot.detection_config = self.mock_config
        self.mock_storage.get_parking_lot.return_value = self.parking_lot
        # Engine not pre-loaded in _engines

        DetectionManager.run_single_detection(self.lot_id)

        self.mock_mock_engine_class.assert_called_once()
        self.mock_mock_engine_instance.configure.assert_called_once_with({'seed': 42})
        self.mock_mock_engine_instance.detect.assert_called_once_with(self.lot_id)
        self.mock_occupancy_manager.update_from_detection_results.assert_called_once()

    def test_run_single_detection_creates_engine_default_yolo(self):
        """Test that run_single_detection creates default YOLO engine if no config."""
        self.parking_lot.detection_config = None
        self.mock_storage.get_parking_lot.return_value = self.parking_lot
        # Engine not pre-loaded in _engines

        DetectionManager.run_single_detection(self.lot_id)

        self.mock_yolo_class.assert_called_once() # Default YOLO engine created
        self.mock_yolo_instance.configure.assert_not_called() # No params to configure
        self.mock_yolo_instance.detect.assert_called_once_with(self.lot_id)
        self.mock_occupancy_manager.update_from_detection_results.assert_called_once()

    def test_run_single_detection_no_lot(self):
        """Test running detection when lot doesn't exist."""
        self.mock_storage.get_parking_lot.return_value = None
        updates = DetectionManager.run_single_detection("nonexistent-lot")
        self.assertEqual(updates, {})
        self.mock_yolo_instance.detect.assert_not_called()
        self.mock_mock_engine_instance.detect.assert_not_called()

    def test_run_single_detection_engine_unavailable(self):
        """Test running detection when the engine is unavailable."""
        self.parking_lot.detection_config = self.yolo_config
        self.mock_storage.get_parking_lot.return_value = self.parking_lot
        DetectionManager._engines[self.lot_id] = self.mock_yolo_instance
        self.mock_yolo_instance.is_available.return_value = False # Make engine unavailable

        updates = DetectionManager.run_single_detection(self.lot_id)

        self.assertEqual(updates, {})
        self.mock_yolo_instance.detect.assert_not_called()
        self.mock_occupancy_manager.update_from_detection_results.assert_not_called()

    def test_run_single_detection_no_results(self):
        """Test running detection when engine returns no results."""
        self.parking_lot.detection_config = self.yolo_config
        self.mock_storage.get_parking_lot.return_value = self.parking_lot
        DetectionManager._engines[self.lot_id] = self.mock_yolo_instance
        self.mock_yolo_instance.detect.return_value = [] # No results

        updates = DetectionManager.run_single_detection(self.lot_id)

        self.assertEqual(updates, {})
        self.mock_yolo_instance.detect.assert_called_once_with(self.lot_id)
        self.mock_occupancy_manager.update_from_detection_results.assert_not_called() # Not called if no results

    def test_run_single_detection_no_state_change(self):
        """Test running detection when slot state doesn't change."""
        self.parking_lot.detection_config = self.yolo_config
        # Add slot_name to ParkingSlot
        initial_slot = ParkingSlot(id='slot-1', slot_name='S1', detection_zone=self.slot1.detection_zone, is_occupied=True)
        initial_lot_obj = ParkingLot(id=self.lot_id, name="Test Lot", slots=[initial_slot])
        
        self.mock_storage.get_parking_lot.side_effect = [initial_lot_obj, initial_lot_obj]

        DetectionManager._engines[self.lot_id] = self.mock_yolo_instance
        # Add slot_name to DetectionResult
        self.mock_yolo_instance.detect.return_value = [DetectionResult(slot_id='slot-1', slot_name='slot-name-1', is_occupied=True, confidence=0.9)]

        DetectionManager.run_single_detection(self.lot_id)

        self.mock_yolo_instance.detect.assert_called_once_with(self.lot_id)
        self.mock_occupancy_manager.update_from_detection_results.assert_called_once()
        self.mock_handle_slot_update.assert_not_called()

    def test_detection_loop(self):
        """Test the _detection_loop logic."""
        # Create a simple implementation of _detection_loop that we can test directly
        # without threading complications

        # Setup
        stop_event = MagicMock()
        # Configure stop_event.is_set() to return False once, then True to exit the loop
        stop_event.is_set.side_effect = [False, True]

        # Configure wait to track calls
        stop_event.wait = MagicMock()

        # Configure get_parking_lot to return a lot with our config
        self.parking_lot.detection_config = self.yolo_config  # Interval 30s
        self.mock_storage.get_parking_lot.return_value = self.parking_lot

        # Create a spy for run_batch_detection
        with patch.object(DetectionManager, 'run_batch_detection', wraps=DetectionManager.run_batch_detection) as spy_run_detection:
            # Call the method directly
            DetectionManager._detection_loop(self.lot_id, stop_event)

            # Assertions
            spy_run_detection.assert_called_once_with(self.lot_id)
            # The timeout should be the update_interval from the config (30 seconds for YOLO config)
            stop_event.wait.assert_called_once_with(timeout=30)


if __name__ == '__main__':
    unittest.main()
