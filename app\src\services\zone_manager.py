from typing import List, Dict, Any, Optional
from src.models.parking import ParkingLot, ParkingSlot
from src.models.geometry import Point, Polygon
from src.utils.storage import StorageService
from src.utils import logger

LOGGER = logger.setup_logger(__name__)


class ZoneManager:
    """Manages parking slot zones for parking lots."""

    @staticmethod
    def occupy_slot(lot_id: str, slot_id: str) -> bool:
        """
        Mark a parking slot as occupied using event sourcing.

        Args:
            lot_id: ID of the parking lot
            slot_id: ID of the slot to mark as occupied

        Returns:
            True if the operation was successful, False otherwise
        """
        LOGGER.debug(f"Attempting to mark slot {slot_id} as occupied in lot {lot_id}")
        # Verify the slot exists before updating
        parking_lot = StorageService.get_parking_lot(lot_id)
        if not parking_lot:
            LOGGER.warning(f"Cannot occupy slot: Parking lot {lot_id} not found")
            return False

        current_slot = parking_lot.get_slot(slot_id)
        if not current_slot:
            LOGGER.warning(f"Cannot occupy slot: Slot {slot_id} not found in lot {lot_id}")
            return False

        # If already occupied, no change needed
        if current_slot.is_occupied:
            LOGGER.debug(f"Slot {slot_id} in lot {lot_id} is already occupied, no change needed")
            return True

        # Use command method to emit an event instead of direct modification
        result = StorageService.update_slot_occupancy(lot_id, slot_id, True)
        if result:
            LOGGER.info(f"Successfully marked slot {slot_id} as occupied in lot {lot_id}")
        else:
            LOGGER.warning(f"Failed to mark slot {slot_id} as occupied in lot {lot_id}")
        return result

    @staticmethod
    def vacate_slot(lot_id: str, slot_id: str) -> bool:
        """
        Mark a parking slot as free using event sourcing.

        Args:
            lot_id: ID of the parking lot
            slot_id: ID of the slot to mark as free

        Returns:
            True if the operation was successful, False otherwise
        """
        LOGGER.debug(f"Attempting to mark slot {slot_id} as free in lot {lot_id}")
        # Verify the slot exists before updating
        parking_lot = StorageService.get_parking_lot(lot_id)
        if not parking_lot:
            LOGGER.warning(f"Cannot vacate slot: Parking lot {lot_id} not found")
            return False

        current_slot = parking_lot.get_slot(slot_id)
        if not current_slot:
            LOGGER.warning(f"Cannot vacate slot: Slot {slot_id} not found in lot {lot_id}")
            return False

        # If already free, no change needed
        if not current_slot.is_occupied:
            LOGGER.debug(f"Slot {slot_id} in lot {lot_id} is already free, no change needed")
            return True

        # Use command method to emit an event instead of direct modification
        result = StorageService.update_slot_occupancy(lot_id, slot_id, False)
        if result:
            LOGGER.info(f"Successfully marked slot {slot_id} as free in lot {lot_id}")
        else:
            LOGGER.warning(f"Failed to mark slot {slot_id} as free in lot {lot_id}")
        return result

    @staticmethod
    def create_zone(lot_id: str, slot_id: str, points: List[Dict[str, float]],
                   overlap_threshold: float = 0.3, viewpoint_id: Optional[str] = None) -> Optional[str]:
        """
        Create a new parking slot with detection zone using event sourcing.

        Args:
            lot_id: ID of the parking lot
            slot_id: ID of the new slot
            points: List of coordinate points for the detection zone
            overlap_threshold: Overlap threshold for the new slot
            viewpoint_id: Optional ID of the viewpoint this slot belongs to

        Returns:
            The ID of the created slot if successful, None otherwise
        """
        LOGGER.debug(f"Creating new parking slot {slot_id} in lot {lot_id} with {len(points)} points, viewpoint_id: {viewpoint_id}")
        # Use command method to emit an event instead of direct modification
        result_slot_id = StorageService.add_parking_slot(lot_id=lot_id, slot_name=slot_id, zone_points=points,
                                                  overlap_threshold=overlap_threshold, viewpoint_id=viewpoint_id)

        if result_slot_id:
            LOGGER.info(f"Successfully created parking slot {result_slot_id} in lot {lot_id}, viewpoint_id: {viewpoint_id}")
        else:
            LOGGER.warning(f"Failed to create parking slot {slot_id} in lot {lot_id}")

        return result_slot_id

    @staticmethod
    def create_zones(lot_id: str, slots: List[Dict[str, Any]]) -> Dict[str, bool]:
        """
        Create multiple parking slots with detection zones using event sourcing.

        Args:
            lot_id: ID of the parking lot
            slots: List of slot data including points and optional overlap_threshold

        Returns:
            Dictionary mapping slot IDs to success/failure status
        """
        LOGGER.info(f"Creating {len(slots)} parking slots in lot {lot_id}")
        results = {}

        for slot_data in slots:
            # Extract points and optional overlap threshold and viewpoint_id
            slot_id = slot_data.get("id", None)
            if not slot_id:
                LOGGER.warning("Missing slot ID in slot data")
                raise ValueError("Slot ID is required for each slot data.")
            points = slot_data.get("points", [])
            overlap_threshold = slot_data.get("overlap_threshold", 0.3)
            viewpoint_id = slot_data.get("viewpoint_id")

            LOGGER.debug(f"Creating slot {slot_id} with {len(points)} points, overlap threshold {overlap_threshold}, viewpoint_id: {viewpoint_id}")
            # Use command method to emit an event instead of direct modification
            created_slot_id = StorageService.add_parking_slot(lot_id=lot_id, slot_name=slot_id, zone_points=points,
                                                      overlap_threshold=overlap_threshold, viewpoint_id=viewpoint_id)

            # Track result for this slot
            success = created_slot_id is not None
            results[slot_id] = success

            if success:
                LOGGER.debug(f"Successfully created slot {slot_id} in lot {lot_id}")
            else:
                LOGGER.warning(f"Failed to create slot {slot_id} in lot {lot_id}")

        successful_count = sum(1 for success in results.values() if success)
        LOGGER.info(f"Created {successful_count} out of {len(slots)} slots in lot {lot_id}")
        return results

    @staticmethod
    def update_zone(lot_id: str, slot_id: str, points: List[Dict[str, float]], 
                    overlap_threshold: Optional[float] = None) -> bool:
        """
        Update an existing parking slot's detection zone using event sourcing.

        Args:
            lot_id: ID of the parking lot
            slot_id: ID of the slot to update
            points: List of coordinate points for the new detection zone
            overlap_threshold: Optional new overlap threshold

        Returns:
            True if the update was successful, False otherwise
        """
        LOGGER.debug(f"Updating zone for slot {slot_id} in lot {lot_id} with {len(points)} points")

        # First update the detection zone
        zone_updated = StorageService.update_slot_zone(lot_id, slot_id, points)

        if not zone_updated:
            LOGGER.warning(f"Failed to update zone for slot {slot_id} in lot {lot_id}")
            return False

        LOGGER.debug(f"Successfully updated zone for slot {slot_id} in lot {lot_id}")

        # If overlap threshold was provided, update it separately
        if overlap_threshold is not None:
            LOGGER.debug(f"Updating overlap threshold for slot {slot_id} to {overlap_threshold}")
            threshold_updated = StorageService.update_slot_threshold(lot_id, slot_id, overlap_threshold)

            if threshold_updated:
                LOGGER.info(f"Successfully updated slot {slot_id} in lot {lot_id} with new zone and threshold")
            else:
                LOGGER.warning(f"Failed to update overlap threshold for slot {slot_id} in lot {lot_id}")
            return threshold_updated

        LOGGER.info(f"Successfully updated zone for slot {slot_id} in lot {lot_id}")
        return zone_updated

    @staticmethod
    def delete_zone(lot_id: str, slot_id: str) -> bool:
        """
        Delete a parking slot using event sourcing.

        Args:
            lot_id: ID of the parking lot
            slot_id: ID of the slot to delete

        Returns:
            True if the deletion was successful, False otherwise
        """
        LOGGER.debug(f"Attempting to delete slot {slot_id} from lot {lot_id}")
        result = StorageService.remove_parking_slot(lot_id, slot_id)

        if result:
            LOGGER.info(f"Successfully deleted slot {slot_id} from lot {lot_id}")
        else:
            LOGGER.warning(f"Failed to delete slot {slot_id} from lot {lot_id}")

        return result

    # Legacy methods for backward compatibility during transition

    @staticmethod
    def occupy_slot_legacy(parking_lot: ParkingLot, slot_id: str) -> bool:
        """Legacy method that directly updates the parking lot object."""
        LOGGER.debug(f"[Legacy] Attempting to mark slot {slot_id} as occupied in lot {parking_lot.id}")

        current_slot = parking_lot.get_slot(slot_id)
        if not current_slot:
            LOGGER.warning(f"[Legacy] Cannot occupy slot: Slot {slot_id} not found in lot {parking_lot.id}")
            return False

        if current_slot.is_occupied:
            LOGGER.debug(f"[Legacy] Slot {slot_id} in lot {parking_lot.id} is already occupied, no change needed")
            return True

        # Direct state modification
        parking_lot.set_slot_occupied(slot_id, True)
        LOGGER.debug(f"[Legacy] Direct state modification: marked slot {slot_id} as occupied")

        # Update via event sourcing as well to keep states in sync
        event_result = StorageService.update_slot_occupancy(parking_lot.id, slot_id, True)
        if event_result:
            LOGGER.info(f"[Legacy] Successfully marked slot {slot_id} as occupied in lot {parking_lot.id}")
        else:
            LOGGER.warning(f"[Legacy] Event sourcing update failed for slot {slot_id} in lot {parking_lot.id}")

        return True

    @staticmethod
    def vacate_slot_legacy(parking_lot: ParkingLot, slot_id: str) -> bool:
        """Legacy method that directly updates the parking lot object."""
        LOGGER.debug(f"[Legacy] Attempting to mark slot {slot_id} as free in lot {parking_lot.id}")

        current_slot = parking_lot.get_slot(slot_id)
        if not current_slot:
            LOGGER.warning(f"[Legacy] Cannot vacate slot: Slot {slot_id} not found in lot {parking_lot.id}")
            return False

        if not current_slot.is_occupied:
            LOGGER.debug(f"[Legacy] Slot {slot_id} in lot {parking_lot.id} is already free, no change needed")
            return True

        # Direct state modification
        parking_lot.set_slot_occupied(slot_id, False)
        LOGGER.debug(f"[Legacy] Direct state modification: marked slot {slot_id} as free")

        # Update via event sourcing as well to keep states in sync
        event_result = StorageService.update_slot_occupancy(parking_lot.id, slot_id, False)
        if event_result:
            LOGGER.info(f"[Legacy] Successfully marked slot {slot_id} as free in lot {parking_lot.id}")
        else:
            LOGGER.warning(f"[Legacy] Event sourcing update failed for slot {slot_id} in lot {parking_lot.id}")

        return True

    @staticmethod
    def create_zone_legacy(parking_lot: ParkingLot, slot_id: str, points: List[Dict[str, float]], 
                         overlap_threshold: float = 0.3) -> ParkingSlot:
        """Legacy method that directly updates the parking lot object."""
        LOGGER.debug(f"[Legacy] Creating new parking slot {slot_id} in lot {parking_lot.id} with {len(points)} points")

        # Convert the raw points to a Polygon
        polygon_points = [Point(x=pt["x"], y=pt["y"]) for pt in points]
        polygon = Polygon(points=polygon_points)
        LOGGER.debug(f"[Legacy] Created polygon with {len(polygon_points)} points")

        # Create the new parking slot with specified overlap threshold
        slot = ParkingSlot(id=slot_id, detection_zone=polygon, overlap_threshold=overlap_threshold)
        LOGGER.debug(f"[Legacy] Created parking slot object with ID {slot_id} and overlap threshold {overlap_threshold}")

        # Direct state modification
        parking_lot.add_slot(slot)
        LOGGER.debug(f"[Legacy] Direct state modification: added slot {slot_id} to lot {parking_lot.id}")

        # Update via event sourcing as well to keep states in sync
        event_result = StorageService.add_parking_slot(lot_id=parking_lot.id, slot_name="", zone_points=points,
                                        overlap_threshold=overlap_threshold)
        if event_result:
            LOGGER.info(f"[Legacy] Successfully created parking slot {slot_id} in lot {parking_lot.id}")
        else:
            LOGGER.warning(f"[Legacy] Event sourcing update failed for new slot {slot_id} in lot {parking_lot.id}")

        return slot

    @staticmethod
    def update_zone_legacy(parking_lot: ParkingLot, slot_id: str, points: List[Dict[str, float]], 
                          overlap_threshold: Optional[float] = None) -> bool:
        """Legacy method that directly updates the parking lot object."""
        LOGGER.debug(f"[Legacy] Updating zone for slot {slot_id} in lot {parking_lot.id} with {len(points)} points")

        # Convert the raw points to a Polygon
        polygon_points = [Point(x=pt["x"], y=pt["y"]) for pt in points]
        polygon = Polygon(points=polygon_points)
        LOGGER.debug(f"[Legacy] Created polygon with {len(polygon_points)} points")

        slot = parking_lot.get_slot(slot_id)
        if not slot:
            LOGGER.warning(f"[Legacy] Cannot update zone: Slot {slot_id} not found in lot {parking_lot.id}")
            return False

        # Direct state modification
        slot.detection_zone = polygon
        LOGGER.debug(f"[Legacy] Direct state modification: updated zone for slot {slot_id}")

        # Update overlap threshold if provided
        if overlap_threshold is not None:
            slot.overlap_threshold = overlap_threshold
            LOGGER.debug(f"[Legacy] Direct state modification: updated overlap threshold to {overlap_threshold}")

        # Update via event sourcing as well to keep states in sync
        zone_updated = StorageService.update_slot_zone(parking_lot.id, slot_id, points)
        if not zone_updated:
            LOGGER.warning(f"[Legacy] Event sourcing zone update failed for slot {slot_id} in lot {parking_lot.id}")

        threshold_updated = True
        if overlap_threshold is not None:
            threshold_updated = StorageService.update_slot_threshold(parking_lot.id, slot_id, overlap_threshold)
            if not threshold_updated:
                LOGGER.warning(f"[Legacy] Event sourcing threshold update failed for slot {slot_id} in lot {parking_lot.id}")

        if zone_updated and threshold_updated:
            LOGGER.info(f"[Legacy] Successfully updated slot {slot_id} in lot {parking_lot.id}")

        return True

    @staticmethod
    def delete_zone_legacy(parking_lot: ParkingLot, slot_id: str) -> bool:
        """Legacy method that directly updates the parking lot object."""
        LOGGER.debug(f"[Legacy] Attempting to delete slot {slot_id} from lot {parking_lot.id}")

        # Direct state modification
        result = parking_lot.delete_slot(slot_id)

        if result:
            LOGGER.debug(f"[Legacy] Direct state modification: deleted slot {slot_id} from lot {parking_lot.id}")
            # Update via event sourcing as well to keep states in sync
            event_result = StorageService.remove_parking_slot(parking_lot.id, slot_id)
            if event_result:
                LOGGER.info(f"[Legacy] Successfully deleted slot {slot_id} from lot {parking_lot.id}")
            else:
                LOGGER.warning(f"[Legacy] Event sourcing delete failed for slot {slot_id} in lot {parking_lot.id}")
        else:
            LOGGER.warning(f"[Legacy] Failed to delete slot {slot_id} from lot {parking_lot.id}: slot not found")

        return result
