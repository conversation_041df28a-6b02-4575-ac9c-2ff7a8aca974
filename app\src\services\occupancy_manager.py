from datetime import datetime
from src.models.parking import ParkingLot
from src.models.detection import DetectionResult
from src.models.status import SlotStatus  # Added import for SlotStatus
from typing import List, Optional, Dict
import time

from src.utils import logger
from src.utils.storage import StorageService
from src.services.event_service import event_service  # Updated to use the singleton instance

LOGGER = logger.setup_logger(__name__)


class OccupancyManager:
    """Manages parking slot occupancy status."""

    @staticmethod
    def update_slot_status(lot_id: str, slot_id: str, is_occupied: bool) -> bool:
        """
        Update the occupancy status of a specific parking slot using event sourcing.

        Args:
            lot_id: ID of the parking lot containing the slot
            slot_id: ID of the slot to update
            is_occupied: New occupancy status

        Returns:
            True if the update was successful, False otherwise
        """
        LOGGER.debug("Updating slot %s occupancy to %s", slot_id, is_occupied)
        
        # Use the Storage facade's command method instead of directly modifying state
        return StorageService.update_slot_occupancy(
            lot_id=lot_id,
            slot_id=slot_id,
            is_occupied=is_occupied,
            detection_timestamp=datetime.utcnow()
        )

    @staticmethod
    def update_from_detection_results(
            lot_id: str,
            results: List[DetectionResult]
    ) -> Dict[str, bool]:
        """
        Update multiple slot statuses from detection results using event sourcing.

        Args:
            lot_id: ID of the parking lot to update
            results: List of detection results

        Returns:
            Dictionary mapping slot_names to success/failure of update
        """
        LOGGER.info("🔄 OCCUPANCY UPDATE: Processing %d detection results for lot %s", len(results), lot_id)
        update_results = {}
        detection_timestamp = datetime.utcnow()

        # First get the current state to check if updates are needed
        parking_lot = StorageService.get_parking_lot(lot_id)
        if not parking_lot:
            LOGGER.warning("Cannot update occupancy: Parking lot %s not found", lot_id)
            return update_results

        # Log all detection results for debugging
        for result in results:
            LOGGER.info("🔍 DETECTION RESULT: Slot '%s' (ID: %s) -> %s (confidence: %.2f)",
                       result.slot_name, result.slot_id, "OCCUPIED" if result.is_occupied else "FREE", result.confidence)
            
        for result in results:
            slot = parking_lot.get_slot(result.slot_id)
            if not slot:
                LOGGER.warning("Slot %s (ID: %s) not found in lot %s", result.slot_name, result.slot_id, lot_id)
                update_results[result.slot_name] = False
                continue

            # Only update if the status has changed
            if slot.is_occupied != result.is_occupied:
                LOGGER.debug(
                    "🔄 SLOT UPDATE: Updating slot '%s' (ID: %s) from %s to %s",
                    slot.slot_name,
                    slot.id,
                    slot.is_occupied,
                    result.is_occupied
                )

                # CRITICAL FIX: Use slot.id (UUID) instead of result.slot_name for storage
                success = StorageService.update_slot_occupancy(
                    lot_id=lot_id,
                    slot_id=slot.id,  # ✅ Fixed: Use actual slot ID instead of slot_name
                    is_occupied=result.is_occupied,
                    detection_timestamp=detection_timestamp
                )

                if success:
                    LOGGER.info("✅ Successfully updated slot '%s' to %s", slot.slot_name, "occupied" if result.is_occupied else "free")
                else:
                    LOGGER.error("❌ Failed to update slot '%s' occupancy", slot.slot_name)

                update_results[result.slot_name] = success
            else:
                # No change needed
                LOGGER.debug("🔄 SLOT UNCHANGED: Slot '%s' already %s", slot.slot_name, "occupied" if slot.is_occupied else "free")
                update_results[result.slot_name] = True
                
        return update_results
        
    # Legacy methods for backward compatibility
    
    @staticmethod
    def update_slot_status_legacy(parking_lot: ParkingLot, slot_id: str, is_occupied: bool) -> bool:
        """
        Legacy method that directly updates the parking lot object.
        
        This is kept for backward compatibility during transition to event sourcing.
        New code should use update_slot_status(lot_id, slot_id, is_occupied) instead.
        """
        LOGGER.warning("Using legacy direct state modification method")
        slot = parking_lot.get_slot(slot_id)
        if not slot:
            return False

        # Direct state modification
        parking_lot.set_slot_occupied(slot_id, is_occupied)
        
        # Also update via event sourcing to keep states in sync during transition
        StorageService.update_slot_occupancy(
            lot_id=parking_lot.id,
            slot_id=slot_id,
            is_occupied=is_occupied
        )
        
        return True

    @staticmethod
    def update_from_detection_results_legacy(
            parking_lot: ParkingLot,
            results: List[DetectionResult]
    ) -> int:
        """
        Legacy method that directly updates the parking lot object from detection results.
        
        This is kept for backward compatibility during transition to event sourcing.
        New code should use update_from_detection_results(lot_id, results) instead.
        """
        LOGGER.warning("Using legacy direct state modification method")
        updates_count = 0

        for result in results:
            slot = parking_lot.get_slot(result.slot_id)
            if slot and slot.is_occupied != result.is_occupied:
                LOGGER.debug(
                    "🔄 LEGACY UPDATE: Updating slot '%s' (ID: %s) from %s to %s",
                    slot.slot_name,
                    slot.id,
                    slot.is_occupied,
                    result.is_occupied
                )
                # Direct state modification
                parking_lot.set_slot_occupied(slot.id, result.is_occupied)

                # Also update via event sourcing to keep states in sync during transition
                StorageService.update_slot_occupancy(
                    lot_id=parking_lot.id,
                    slot_id=slot.id,  # ✅ This was already correct
                    is_occupied=result.is_occupied
                )

                updates_count += 1

        return updates_count

def emit_slot_status_update(slot_id: str, lot_id: str, status: SlotStatus, event_time: datetime = None):
    """
    Emit a slot status update event
    
    Args:
        slot_id: The ID of the parking slot
        lot_id: The ID of the parking lot
        status: The new status of the slot
        event_time: The time of the event (defaults to current time)
    """
    if event_time is None:
        event_time = datetime.now()
        
    event_service.emit_status_update(
        slot_id=slot_id,
        lot_id=lot_id,
        status=status,
        timestamp=event_time
    )
