"""
FastAPI router for viewpoints endpoints
Migrated from routes/viewpoints.py - maintains 100% compatibility
"""

from fastapi import APIRouter, HTTPException, Request, Depends, File, UploadFile, Form
from fastapi.responses import JSONResponse
from typing import Dict, Any, List, Optional
import uuid

# Import business logic (unchanged from Flask)
from src.utils.storage import StorageService
from src.utils.logger import setup_logger
from src.services.image_handler import ImageHandler
from src.dependencies import get_config, get_socketio

# Import Pydantic schemas
from src.schemas import (
    CreateViewpointRequest, UpdateViewpointRequest, ViewpointsResponse,
    ViewpointListItemResponse, ViewpointResponse, ImageUploadResponse
)

# Initialize logger
LOGGER = setup_logger(__name__)

# Create router with prefix to match Flask blueprint
router = APIRouter(prefix="/api/lots/{lot_id}/viewpoints", tags=["viewpoints"])

@router.get("/")
async def get_viewpoints(lot_id: str) -> ViewpointsResponse:
    """Get all viewpoints for a parking lot."""
    LOGGER.info(f"API request: Get viewpoints for lot {lot_id}")

    try:
        lot = StorageService.get_parking_lot(lot_id)
        if not lot:
            LOGGER.warning(f"Parking lot with ID {lot_id} not found when getting viewpoints")
            raise HTTPException(status_code=404, detail="Parking lot not found")

        viewpoints_data = []
        for viewpoint in lot.viewpoints:
            # Calculate zone count by counting slots that belong to this viewpoint
            zone_count = len([slot for slot in lot.slots if slot.viewpoint_id == viewpoint.id])

            viewpoints_data.append(ViewpointListItemResponse(
                id=viewpoint.id,
                name=viewpoint.name,
                image_path=viewpoint.image_path,
                camera_position=viewpoint.camera_position,
                is_active=viewpoint.is_active,
                sort_order=viewpoint.sort_order,
                zone_count=zone_count,
                is_default=viewpoint.id == lot.default_viewpoint_id,
                stream_url=viewpoint.stream_url,
                capture_fps=viewpoint.capture_fps,
                is_stream_active=viewpoint.is_stream_active,
                stream_status=viewpoint.stream_status,
                stream_error_message=viewpoint.stream_error_message,
                last_frame_time=viewpoint.last_frame_time
            ))

        # Sort by sort_order
        viewpoints_data.sort(key=lambda x: x.sort_order)

        LOGGER.debug(f"Retrieved {len(viewpoints_data)} viewpoints for lot {lot_id}")
        return ViewpointsResponse(
            viewpoints=viewpoints_data,
            default_viewpoint_id=lot.default_viewpoint_id,
            total_count=len(viewpoints_data)
        )

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error getting viewpoints for lot {lot_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/", status_code=201)
async def create_viewpoint(
    lot_id: str,
    name: str = Form(...),
    file: Optional[UploadFile] = File(None),
    camera_position: Optional[str] = Form(None),
    is_active: bool = Form(True),
    sort_order: int = Form(0),
    config: dict = Depends(get_config),
    socketio = Depends(get_socketio)
) -> ViewpointResponse:
    """Create a new viewpoint for a parking lot."""
    LOGGER.info(f"API request: Create viewpoint '{name}' for lot {lot_id}")

    lot = StorageService.get_parking_lot(lot_id)
    if not lot:
        LOGGER.warning(f"Parking lot with ID {lot_id} not found when creating viewpoint")
        raise HTTPException(status_code=404, detail="Parking lot not found")

    try:
        # Generate unique viewpoint ID
        viewpoint_id = str(uuid.uuid4())

        # Handle image upload if file is provided
        relative_path = None
        if file and file.filename:
            # Validate file
            if not ImageHandler.allowed_file(file.filename):
                raise HTTPException(status_code=400, detail="Invalid file type. Only JPG and PNG are supported")

            # Save the image
            image_handler = ImageHandler(storage_dir=config['UPLOAD_FOLDER'])
            image_bytes = await file.read()
            relative_path = image_handler.save_image(image_bytes, f"viewpoint_{viewpoint_id}", file.filename)
        else:
            # Create a placeholder image for viewpoints without uploaded images
            from src.services.image_handler import ImageHandler
            image_handler = ImageHandler(storage_dir=config['UPLOAD_FOLDER'])
            relative_path = image_handler.create_placeholder_image(viewpoint_id, "viewpoint_placeholder.jpg")

        # Parse camera position if provided
        camera_pos = None
        if camera_position:
            import json
            try:
                camera_pos = json.loads(camera_position)
            except json.JSONDecodeError:
                LOGGER.warning(f"Invalid camera position JSON for viewpoint {name}")

        # Create viewpoint using event sourcing
        created_viewpoint_id = StorageService.add_viewpoint(
            lot_id=lot_id,
            name=name,
            image_path=relative_path,
            camera_position=camera_pos,
            is_active=is_active,
            sort_order=sort_order
        )

        if not created_viewpoint_id:
            LOGGER.warning(f"Failed to create viewpoint {name} for lot {lot_id}")
            raise HTTPException(status_code=500, detail="Failed to create viewpoint")

        # Get the created viewpoint
        updated_lot = StorageService.get_parking_lot(lot_id)
        created_viewpoint = next((v for v in updated_lot.viewpoints if v.id == created_viewpoint_id), None)

        if not created_viewpoint:
            raise HTTPException(status_code=500, detail="Failed to retrieve created viewpoint")

        LOGGER.debug(f"Created viewpoint {name} with ID {created_viewpoint_id}")

        # Convert to response model
        from src.schemas import ViewpointZoneResponse
        zones = [ViewpointZoneResponse(slot_id=z.slot_id, slot_name=z.slot_name)
                for z in created_viewpoint.zones]

        return ViewpointResponse(
            id=created_viewpoint.id,
            name=created_viewpoint.name,
            image_path=created_viewpoint.image_path,
            camera_position=created_viewpoint.camera_position,
            is_active=created_viewpoint.is_active,
            sort_order=created_viewpoint.sort_order,
            zones=zones
        )

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error creating viewpoint for lot {lot_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating viewpoint: {str(e)}")


@router.post("/stream", status_code=201)
async def create_stream_viewpoint(
    lot_id: str,
    name: str = Form(...),
    stream_url: str = Form(...),
    capture_fps: float = Form(1.0),
    camera_position: Optional[str] = Form(None),
    is_active: bool = Form(True),
    sort_order: int = Form(0),
    config: dict = Depends(get_config),
    socketio = Depends(get_socketio)
) -> ViewpointResponse:
    """Create a new stream viewpoint for a parking lot."""
    LOGGER.info(f"API request: Create stream viewpoint '{name}' for lot {lot_id} with URL {stream_url}")

    lot = StorageService.get_parking_lot(lot_id)
    if not lot:
        LOGGER.warning(f"Parking lot with ID {lot_id} not found when creating stream viewpoint")
        raise HTTPException(status_code=404, detail="Parking lot not found")

    try:
        # Validate stream URL
        if not stream_url or not stream_url.strip():
            raise HTTPException(status_code=400, detail="Stream URL is required")

        # Generate unique viewpoint ID
        viewpoint_id = str(uuid.uuid4())

        # Create placeholder image for the stream viewpoint
        from src.services.image_handler import ImageHandler
        image_handler = ImageHandler(storage_dir=config['UPLOAD_FOLDER'])
        placeholder_path = image_handler.create_placeholder_image(viewpoint_id, "stream_placeholder.jpg")

        # Parse camera position if provided
        camera_pos = None
        if camera_position:
            import json
            try:
                camera_pos = json.loads(camera_position)
            except json.JSONDecodeError:
                LOGGER.warning(f"Invalid camera position JSON for stream viewpoint {name}")

        # Create viewpoint using event sourcing
        created_viewpoint_id = StorageService.add_viewpoint(
            lot_id=lot_id,
            name=name,
            image_path=placeholder_path,
            camera_position=camera_pos,
            is_active=is_active,
            sort_order=sort_order
        )

        if not created_viewpoint_id:
            LOGGER.warning(f"Failed to create stream viewpoint {name} for lot {lot_id}")
            raise HTTPException(status_code=500, detail="Failed to create viewpoint")

        # Configure stream for the viewpoint
        success = StorageService.configure_viewpoint_stream(
            lot_id=lot_id,
            viewpoint_id=created_viewpoint_id,
            stream_url=stream_url,
            capture_fps=capture_fps
        )

        if not success:
            LOGGER.warning(f"Failed to configure stream for viewpoint {created_viewpoint_id}")
            raise HTTPException(status_code=500, detail="Failed to configure stream")

        # Activate stream
        StorageService.activate_viewpoint_stream(lot_id, created_viewpoint_id)

        # Start stream capture (cache is now updated automatically by the methods above)
        from src.services.stream_capture_service import StreamCaptureService
        capture_started = StreamCaptureService.start_stream_capture(lot_id, created_viewpoint_id)

        if not capture_started:
            LOGGER.warning(f"Failed to start stream capture for viewpoint {created_viewpoint_id}")
            # Don't fail the creation, just log the warning

        # Get the created viewpoint
        updated_lot = StorageService.get_parking_lot(lot_id)
        created_viewpoint = next((v for v in updated_lot.viewpoints if v.id == created_viewpoint_id), None)

        if not created_viewpoint:
            raise HTTPException(status_code=500, detail="Failed to retrieve created viewpoint")

        LOGGER.info(f"Stream viewpoint '{name}' created successfully with ID {created_viewpoint_id}")

        # Emit WebSocket event for real-time updates
        await socketio.emit('viewpoint_created', {
            'lot_id': lot_id,
            'viewpoint': {
                'id': created_viewpoint.id,
                'name': created_viewpoint.name,
                'image_path': created_viewpoint.image_path,
                'stream_url': created_viewpoint.stream_url,
                'capture_fps': created_viewpoint.capture_fps,
                'is_stream_active': created_viewpoint.is_stream_active
            }
        }, namespace='/dashboard')

        return ViewpointResponse(
            id=created_viewpoint.id,
            name=created_viewpoint.name,
            image_path=created_viewpoint.image_path,
            camera_position=created_viewpoint.camera_position,
            is_active=created_viewpoint.is_active,
            sort_order=created_viewpoint.sort_order,
            zones=[],
            stream_url=created_viewpoint.stream_url,
            capture_fps=created_viewpoint.capture_fps,
            is_stream_active=created_viewpoint.is_stream_active,
            stream_status=created_viewpoint.stream_status,
            stream_error_message=created_viewpoint.stream_error_message,
            last_frame_time=created_viewpoint.last_frame_time
        )

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error creating stream viewpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating stream viewpoint: {str(e)}")

@router.put("/{viewpoint_id}")
async def update_viewpoint(
    lot_id: str,
    viewpoint_id: str,
    viewpoint_update: UpdateViewpointRequest
) -> ViewpointResponse:
    """Update a viewpoint."""
    LOGGER.info(f"API request: Update viewpoint {viewpoint_id} in lot {lot_id}")

    lot = StorageService.get_parking_lot(lot_id)
    if not lot:
        LOGGER.warning(f"Parking lot with ID {lot_id} not found when updating viewpoint")
        raise HTTPException(status_code=404, detail="Parking lot not found")

    # Find the viewpoint
    viewpoint = next((v for v in lot.viewpoints if v.id == viewpoint_id), None)
    if not viewpoint:
        LOGGER.warning(f"Viewpoint with ID {viewpoint_id} not found in lot {lot_id}")
        raise HTTPException(status_code=404, detail="Viewpoint not found")

    try:
        # Update viewpoint using event sourcing
        update_data = {}
        if viewpoint_update.name is not None:
            update_data['name'] = viewpoint_update.name
        if viewpoint_update.camera_position is not None:
            update_data['camera_position'] = viewpoint_update.camera_position
        if viewpoint_update.is_active is not None:
            update_data['is_active'] = viewpoint_update.is_active
        if viewpoint_update.sort_order is not None:
            update_data['sort_order'] = viewpoint_update.sort_order

        success = StorageService.update_viewpoint(lot_id, viewpoint_id, **update_data)
        if not success:
            LOGGER.warning(f"Failed to update viewpoint {viewpoint_id} in lot {lot_id}")
            raise HTTPException(status_code=500, detail="Failed to update viewpoint")

        # Get updated viewpoint
        updated_lot = StorageService.get_parking_lot(lot_id)
        updated_viewpoint = next((v for v in updated_lot.viewpoints if v.id == viewpoint_id), None)

        if not updated_viewpoint:
            raise HTTPException(status_code=500, detail="Failed to retrieve updated viewpoint")

        LOGGER.debug(f"Updated viewpoint {viewpoint_id} in lot {lot_id}")

        # Convert to response model
        from src.schemas import ViewpointZoneResponse
        zones = [ViewpointZoneResponse(slot_id=z.slot_id, slot_name=z.slot_name)
                for z in updated_viewpoint.zones]

        return ViewpointResponse(
            id=updated_viewpoint.id,
            name=updated_viewpoint.name,
            image_path=updated_viewpoint.image_path,
            camera_position=updated_viewpoint.camera_position,
            is_active=updated_viewpoint.is_active,
            sort_order=updated_viewpoint.sort_order,
            zones=zones
        )

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error updating viewpoint {viewpoint_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error updating viewpoint: {str(e)}")

@router.delete("/{viewpoint_id}")
async def delete_viewpoint(lot_id: str, viewpoint_id: str) -> Dict[str, Any]:
    """Delete a viewpoint."""
    LOGGER.info(f"API request: Delete viewpoint {viewpoint_id} from lot {lot_id}")

    lot = StorageService.get_parking_lot(lot_id)
    if not lot:
        LOGGER.warning(f"Parking lot with ID {lot_id} not found when deleting viewpoint")
        raise HTTPException(status_code=404, detail="Parking lot not found")

    # Find the viewpoint in the cached lot
    viewpoint = next((v for v in lot.viewpoints if v.id == viewpoint_id), None)
    if not viewpoint:
        # Viewpoint not found in cache - this could be a cache consistency issue
        # Force cache refresh and try again
        LOGGER.warning(f"Viewpoint {viewpoint_id} not found in cached lot {lot_id}, refreshing cache")
        StorageService._invalidate_cache(lot_id)
        lot = StorageService.get_parking_lot(lot_id)

        if lot:
            viewpoint = next((v for v in lot.viewpoints if v.id == viewpoint_id), None)

        if not viewpoint:
            LOGGER.warning(f"Viewpoint with ID {viewpoint_id} not found in lot {lot_id} even after cache refresh")
            raise HTTPException(status_code=404, detail="Viewpoint not found")

    try:
        # Delete viewpoint using event sourcing
        success = StorageService.remove_viewpoint(lot_id, viewpoint_id)
        if not success:
            LOGGER.warning(f"Failed to delete viewpoint {viewpoint_id} from lot {lot_id}")
            raise HTTPException(status_code=500, detail="Failed to delete viewpoint")

        LOGGER.debug(f"Deleted viewpoint {viewpoint_id} from lot {lot_id}")
        return {"message": "Viewpoint deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error deleting viewpoint {viewpoint_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting viewpoint: {str(e)}")

@router.post("/{viewpoint_id}/set-default")
async def set_default_viewpoint(lot_id: str, viewpoint_id: str) -> Dict[str, Any]:
    """Set a viewpoint as the default for a parking lot."""
    LOGGER.info(f"API request: Set viewpoint {viewpoint_id} as default for lot {lot_id}")

    lot = StorageService.get_parking_lot(lot_id)
    if not lot:
        LOGGER.warning(f"Parking lot with ID {lot_id} not found when setting default viewpoint")
        raise HTTPException(status_code=404, detail="Parking lot not found")

    # Find the viewpoint
    viewpoint = next((v for v in lot.viewpoints if v.id == viewpoint_id), None)
    if not viewpoint:
        LOGGER.warning(f"Viewpoint with ID {viewpoint_id} not found in lot {lot_id}")
        raise HTTPException(status_code=404, detail="Viewpoint not found")

    try:
        # Set default viewpoint using event sourcing
        success = StorageService.set_default_viewpoint(lot_id, viewpoint_id)
        if not success:
            LOGGER.warning(f"Failed to set viewpoint {viewpoint_id} as default for lot {lot_id}")
            raise HTTPException(status_code=500, detail="Failed to set default viewpoint")

        LOGGER.debug(f"Set viewpoint {viewpoint_id} as default for lot {lot_id}")
        return {"message": "Default viewpoint set successfully"}

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error setting default viewpoint {viewpoint_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error setting default viewpoint: {str(e)}")
