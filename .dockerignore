# Git
.git/
.gitignore

# Docker
Dockerfile
.dockerignore

# IDE / Editor config
.idea/
.spyderproject
.spyproject
.ropeproject

# Python cache/bytecode
__pycache__/
*.py[cod]
*$py.class
.mypy_cache/
.pytest_cache/
.cache

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
__pypackages__/

# Build / Distribution artifacts
build/
dist/
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
*.manifest
*.spec
target/

# Test / Coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
*.cover
*.py,cover
.hypothesis/
cover/
nosetests.xml
coverage.xml

# Logs / Databases / Uploads / Data
*.log
*.db
*.sqlite3
*.sqlite3-journal
static/uploads/
data/ # Ignore the whole data dir, it's created empty in Dockerfile

# OS generated files
*.DS_Store
*~
# Thumbs.db

# Notebook checkpoints
.ipynb_checkpoints

# Scrapy
.scrapy

# Sphinx docs build
docs/_build/

# Other tools/configs
.pdm.toml
.pdm-python
.pdm-build/
celerybeat-schedule
celerybeat.pid
*.sage.py
cython_debug/
.pyre/
.pytype/

# Project specific ignores
azure/
features/
frontend/ # Assuming frontend built elsewhere or not needed
node_modules/
static/dist/ # Assuming frontend build output
parking_lots*.json # Backup files
photo_uploader.py
stream_uploader.py
requirements.dev.txt
requirements.txt # We copy requirements.docker.txt as requirements.txt

# Don't ignore model files explicitly copied in Dockerfile
# *.pt
# *.onnx
