from datetime import datetime
from typing import Dict, Any, Type, Optional

from src.models.parking import Parking<PERSON>ot, ParkingSlot
from src.models.geometry import Polygon, Point
from src.models.detection import DetectionEngineConfig
from src.models.events import (
    BaseEvent, ParkingEvent,
    ParkingLotCreated, ParkingLotDeleted, ParkingLotRenamed,
    ParkingLotImageUpdated, ParkingSlotAdded, ParkingSlotRemoved,
    Parking<PERSON>lotZoneUpdated, ParkingSlotThresholdUpdated,
    ParkingSlotOccupancyChanged, DetectionConfigCreated,
    DetectionConfigUpdated, DetectionConfigDeleted,
    ViewpointAdded, ViewpointRemoved, ViewpointUpdated,
    ViewpointStreamConfigured, ViewpointStreamActivated, ViewpointStreamDeactivated,
    ViewpointStreamStatusChanged,
    ViewpointZoneAdded, ViewpointZoneUpdated, ViewpointZoneRemoved,
    DefaultViewpointSet
)
from src.models.orm import (
    ParkingLotORM, ParkingSlotORM, DetectionZoneORM, PolygonPointORM,
    ParkingEventORM, DetectionEngineConfigORM
)
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

class Mapper:
    """Handles mapping between ORM models and data classes."""

    # Event type to event class mapping with both camelCase and snake_case variants
    _EVENT_TYPE_MAP: Dict[str, Type[BaseEvent]] = {
        # Snake case variants (from original implementation)
        'parking_lot_created': ParkingLotCreated,
        'parking_lot_deleted': ParkingLotDeleted,
        'parking_lot_renamed': ParkingLotRenamed,
        'parking_lot_image_updated': ParkingLotImageUpdated,
        'parking_slot_added': ParkingSlotAdded,
        'parking_slot_removed': ParkingSlotRemoved,
        'parking_slot_zone_updated': ParkingSlotZoneUpdated,
        'parking_slot_threshold_updated': ParkingSlotThresholdUpdated,
        'parking_slot_occupancy_changed': ParkingSlotOccupancyChanged,
        'detection_config_created': DetectionConfigCreated,
        'detection_config_updated': DetectionConfigUpdated,
        'detection_config_deleted': DetectionConfigDeleted,
        'viewpoint_added': ViewpointAdded,
        'viewpoint_removed': ViewpointRemoved,
        'viewpoint_updated': ViewpointUpdated,
        'viewpoint_stream_configured': ViewpointStreamConfigured,
        'viewpoint_stream_activated': ViewpointStreamActivated,
        'viewpoint_stream_deactivated': ViewpointStreamDeactivated,
        'viewpoint_stream_status_changed': ViewpointStreamStatusChanged,
        'viewpoint_zone_added': ViewpointZoneAdded,
        'viewpoint_zone_updated': ViewpointZoneUpdated,
        'viewpoint_zone_removed': ViewpointZoneRemoved,
        'default_viewpoint_set': DefaultViewpointSet,
        
        # CamelCase variants (as used in modern implementation)
        'parkinglotcreated': ParkingLotCreated,
        'parkinglotdeleted': ParkingLotDeleted,
        'parkinglotrenamed': ParkingLotRenamed,
        'parkinglotimageupdated': ParkingLotImageUpdated,
        'parkingslotadded': ParkingSlotAdded,
        'parkingslotremoved': ParkingSlotRemoved,
        'parkinglotzoneupdated': ParkingSlotZoneUpdated,
        'parkingslotthresholdupdated': ParkingSlotThresholdUpdated,
        'parkingslotoccupancychanged': ParkingSlotOccupancyChanged,
        'detectionconfigcreated': DetectionConfigCreated,
        'detectionconfigupdated': DetectionConfigUpdated,
        'detectionconfigdeleted': DetectionConfigDeleted,
        'viewpointadded': ViewpointAdded,
        'viewpointremoved': ViewpointRemoved,
        'viewpointupdated': ViewpointUpdated,
        'viewpointstreamconfigured': ViewpointStreamConfigured,
        'viewpointstreamactivated': ViewpointStreamActivated,
        'viewpointstreamdeactivated': ViewpointStreamDeactivated,
        'viewpointstreamstatuschanged': ViewpointStreamStatusChanged,
        'viewpointzoneadded': ViewpointZoneAdded,
        'viewpointzoneupdated': ViewpointZoneUpdated,
        'viewpointzoneremoved': ViewpointZoneRemoved,
        'defaultviewpointset': DefaultViewpointSet,
        
        # Add exact class name matches
        'ParkingLotCreated': ParkingLotCreated,
        'ParkingLotDeleted': ParkingLotDeleted,
        'ParkingLotRenamed': ParkingLotRenamed,
        'ParkingLotImageUpdated': ParkingLotImageUpdated,
        'ParkingSlotAdded': ParkingSlotAdded,
        'ParkingSlotRemoved': ParkingSlotRemoved,
        'ParkingSlotZoneUpdated': ParkingSlotZoneUpdated,
        'ParkingSlotThresholdUpdated': ParkingSlotThresholdUpdated,
        'ParkingSlotOccupancyChanged': ParkingSlotOccupancyChanged,
        'DetectionConfigCreated': DetectionConfigCreated,
        'DetectionConfigUpdated': DetectionConfigUpdated,
        'DetectionConfigDeleted': DetectionConfigDeleted,
        'ViewpointAdded': ViewpointAdded,
        'ViewpointRemoved': ViewpointRemoved,
        'ViewpointUpdated': ViewpointUpdated,
        'ViewpointStreamConfigured': ViewpointStreamConfigured,
        'ViewpointStreamActivated': ViewpointStreamActivated,
        'ViewpointStreamDeactivated': ViewpointStreamDeactivated,
        'ViewpointStreamStatusChanged': ViewpointStreamStatusChanged,
        'ViewpointZoneAdded': ViewpointZoneAdded,
        'ViewpointZoneUpdated': ViewpointZoneUpdated,
        'ViewpointZoneRemoved': ViewpointZoneRemoved,
        'DefaultViewpointSet': DefaultViewpointSet,
        
        # Legacy generic event type
        'parking_event': ParkingEvent,
        'parkingevent': ParkingEvent,
        'ParkingEvent': ParkingEvent
    }
    
    @staticmethod
    def orm_to_dataclass_lot(orm: ParkingLotORM) -> ParkingLot:
        """
        Converts a ParkingLotORM to a ParkingLot domain object.
        NOTE: This is primarily for backward compatibility with direct ORM access.
        In event sourcing, lots are reconstructed from events, not directly from ORM.
        """
        slots = [Mapper.orm_to_dataclass_slot(s) for s in orm.slots]
        events = [Mapper.orm_to_dataclass_event(e) for e in orm.events]
        config = Mapper.orm_to_dataclass_config(orm.detection_config) if orm.detection_config else None
        return ParkingLot(
            id=orm.id,
            name=orm.name,
            image_path=orm.image_path,
            slots=slots,
            detection_config=config,
            events=events
        )

    @staticmethod
    def orm_to_dataclass_slot(orm: ParkingSlotORM) -> ParkingSlot:
        zone = Mapper.orm_to_dataclass_zone(orm.detection_zone) if orm.detection_zone else None
        return ParkingSlot(
            id=orm.id,
            slot_name=orm.slot_name,
            detection_zone=zone,
            is_occupied=orm.is_occupied,
            overlap_threshold=orm.overlap_threshold
        )

    @staticmethod
    def orm_to_dataclass_zone(orm: DetectionZoneORM) -> Polygon:
        # Ensure points are sorted by their order attribute
        points = [Point(x=p.x, y=p.y) for p in sorted(orm.points, key=lambda pt: pt.order)]
        return Polygon(points=points)

    @staticmethod
    def orm_to_dataclass_event(orm: ParkingEventORM) -> BaseEvent:
        """
        Converts a ParkingEventORM to the appropriate BaseEvent subclass instance
        based on the event_type field.
        
        Args:
            orm: The ParkingEventORM to convert
            
        Returns:
            BaseEvent: An instance of the appropriate BaseEvent subclass
            
        Raises:
            ValueError: If the event type is not recognized
        """
        # Create a normalized version of the event type for lookup
        # Remove spaces, underscores, and convert to lowercase
        normalized_event_type = orm.event_type.lower().replace('_', '').replace(' ', '')

        # First try exact match (case-sensitive)
        event_class = Mapper._EVENT_TYPE_MAP.get(orm.event_type)
        
        # If no exact match, try normalized lowercase version
        if not event_class:
            event_class = Mapper._EVENT_TYPE_MAP.get(normalized_event_type)

        if not event_class:
            logger.warning(f"Unknown event type: {orm.event_type}, falling back to ParkingEvent")
            event_class = ParkingEvent
        
        # Handle legacy ParkingEvent (which has different constructor params)
        if event_class == ParkingEvent:
            return ParkingEvent(
                lot_id=orm.lot_id,
                event_type=orm.event_type,
                description=orm.description,
                payload=orm.payload or {},
                timestamp=orm.timestamp
            )
        
        # Construct the appropriate event class
        try:
            # Assign sequence_id if it exists in ORM
            sequence_id = getattr(orm, 'sequence_id', None)
            
            # Create event instance
            event = event_class(
                lot_id=orm.lot_id,
                payload=orm.payload or {},
                # Allow timestamp from ORM or create new if missing
                timestamp=orm.timestamp if orm.timestamp else datetime.utcnow(),
            )
            
            # Set sequence_id if available (might be None in legacy events)
            if sequence_id is not None:
                event.sequence_id = sequence_id
                
            return event
            
        except Exception as e:
            logger.error(f"Error creating event object of type {event_class.__name__}: {e}", exc_info=True)
            # Fallback to generic ParkingEvent if specific class instantiation fails
            return ParkingEvent(
                lot_id=orm.lot_id,
                event_type=orm.event_type,
                description=orm.description or "",
                payload=orm.payload or {},
                timestamp=orm.timestamp
            )
    
    @staticmethod
    def dataclass_to_orm_event(event: BaseEvent) -> ParkingEventORM:
        """
        Converts a BaseEvent to a ParkingEventORM.
        
        Args:
            event: The domain event to convert
            
        Returns:
            ParkingEventORM: The ORM representation of the event
        """
        return ParkingEventORM(
            lot_id=event.lot_id,
            event_type=event.event_type,
            # sequence_id is assigned by the event store
            description=None,  # No longer using description, info is in payload
            payload=event.payload,
            timestamp=event.timestamp
        )

    @staticmethod
    def orm_to_dataclass_config(orm: DetectionEngineConfigORM) -> DetectionEngineConfig:
        return DetectionEngineConfig(
            engine_type=orm.engine_type,
            is_enabled=orm.is_enabled,
            update_interval=orm.update_interval,
            parameters=orm.parameters or {}
        )

    @staticmethod
    def dataclass_to_orm_config(config: DetectionEngineConfig, lot_id: str) -> DetectionEngineConfigORM:
        """
        Converts a DetectionEngineConfig dataclass to a DetectionEngineConfigORM.
        
        Args:
            config: The detection engine configuration object
            lot_id: The ID of the parking lot this config belongs to
            
        Returns:
            DetectionEngineConfigORM: The ORM representation of the config
        """
        return DetectionEngineConfigORM(
            lot_id=lot_id,
            engine_type=config.engine_type,
            is_enabled=config.is_enabled,
            update_interval=config.update_interval,
            parameters=config.parameters or {}
        )

    # Add dataclass_to_orm methods if needed for create/update operations,
    # although current implementation builds ORM objects directly in storage methods.
