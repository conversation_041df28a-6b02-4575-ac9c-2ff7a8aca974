/**
 * Class Autocomplete Component
 * Provides autocomplete functionality for selecting model classes
 */
class ClassAutocomplete {
    constructor(options = {}) {
        this.options = {
            inputId: 'class_input',
            dropdownId: 'classes_list',
            selectedContainerId: 'selected_classes',
            hiddenInputId: 'selected_classes_input',
            lotIdSelector: '[data-lot-id]',
            apiEndpoint: '/api/lots/{lot_id}/detection-classes',
            ...options
        };
        
        this.classes = [];
        this.selectedClasses = [];
        this.initialized = false;
    }
    
    async init() {
        // Get DOM elements
        this.input = document.getElementById(this.options.inputId);
        this.dropdown = document.getElementById(this.options.dropdownId);
        this.selectedContainer = document.getElementById(this.options.selectedContainerId);
        this.hiddenInput = document.getElementById(this.options.hiddenInputId);
        
        if (!this.input || !this.dropdown || !this.selectedContainer || !this.hiddenInput) {
            console.error('Class autocomplete: Required elements not found');
            return;
        }
        
        // Fetch classes
        await this.fetchClasses();
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Initialize from checkbox selections
        this.initFromCheckboxes();
        
        this.initialized = true;
    }
    
    async fetchClasses() {
        try {
            const lotIdEl = document.querySelector(this.options.lotIdSelector);
            if (!lotIdEl || !lotIdEl.dataset.lotId) {
                throw new Error('Lot ID not found');
            }
            
            const lotId = lotIdEl.dataset.lotId;
            const url = this.options.apiEndpoint.replace('{lot_id}', lotId);
            
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error('Failed to fetch classes');
            }
            
            const data = await response.json();
            this.classes = data.classes || [];
            
            // Populate dropdown
            this.populateDropdown();
        } catch (error) {
            console.error('Error fetching classes:', error);
            this.classes = [];
        }
    }
    
    populateDropdown() {
        // Clear existing options
        this.dropdown.innerHTML = '';
        
        if (this.classes.length === 0) {
            const emptyOption = document.createElement('div');
            emptyOption.className = 'class-option';
            emptyOption.textContent = 'No classes available';
            this.dropdown.appendChild(emptyOption);
            return;
        }
        
        // Create options for each class
        this.classes.forEach((className, index) => {
            const option = document.createElement('div');
            option.className = 'class-option';
            option.textContent = `${className} (${index})`;
            option.dataset.className = className;
            option.dataset.classId = index;
            this.dropdown.appendChild(option);
            
            // Add click event
            option.addEventListener('click', () => {
                this.addClass(parseInt(option.dataset.classId), option.dataset.className);
                this.input.value = '';
                this.dropdown.style.display = 'none';
            });
        });
    }
    
    setupEventListeners() {
        // Input change/focus events
        this.input.addEventListener('input', () => {
            const value = this.input.value.toLowerCase();
            if (value.length === 0) {
                this.dropdown.style.display = 'none';
                return;
            }
            
            let hasMatches = false;
            this.dropdown.querySelectorAll('.class-option').forEach(option => {
                const text = option.textContent.toLowerCase();
                if (text.includes(value)) {
                    option.style.display = 'block';
                    hasMatches = true;
                } else {
                    option.style.display = 'none';
                }
            });
            
            this.dropdown.style.display = hasMatches ? 'block' : 'none';
        });
        
        this.input.addEventListener('focus', () => {
            if (this.input.value.length > 0) {
                this.dropdown.style.display = 'block';
            }
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', (event) => {
            if (!this.dropdown.contains(event.target) && event.target !== this.input) {
                this.dropdown.style.display = 'none';
            }
        });
        
        // No checkbox syncing needed since we removed hardcoded checkboxes
    }
    
    initFromCheckboxes() {
        // Initialize from existing configuration if available
        try {
            const hiddenInput = document.getElementById(this.options.hiddenInputId);
            if (hiddenInput && hiddenInput.value) {
                const existingClasses = JSON.parse(hiddenInput.value);
                if (Array.isArray(existingClasses)) {
                    existingClasses.forEach(classId => {
                        // Find class name from available classes
                        const className = this.classes[classId] || `Class ${classId}`;
                        this.addClass(classId, className, false);
                    });
                }
            }
        } catch (error) {
            console.warn('Could not initialize from existing configuration:', error);
        }
    }
    
    addClass(id, name, updateCheckbox = true) {
        // Check if already in selection
        if (this.selectedClasses.some(c => c.id === id)) {
            return;
        }
        
        this.selectedClasses.push({ id, name });
        this.updateHiddenInput();
        
        // Create tag element
        const tag = document.createElement('div');
        tag.className = 'selected-class-tag';
        tag.innerHTML = `
            <span>${name} (${id})</span>
            <button type="button" class="remove-class-btn" data-class-id="${id}">&times;</button>
        `;
        this.selectedContainer.appendChild(tag);
        
        // Add event listener to remove button
        tag.querySelector('.remove-class-btn').addEventListener('click', () => {
            this.removeClass(id);
        });
        
        // No checkbox updating needed since we removed hardcoded checkboxes
    }
    
    removeClass(id) {
        this.selectedClasses = this.selectedClasses.filter(c => c.id !== id);
        this.updateHiddenInput();
        
        // Remove tag
        const tag = this.selectedContainer.querySelector(`.remove-class-btn[data-class-id="${id}"]`);
        if (tag) {
            tag.closest('.selected-class-tag').remove();
        }
        
        // No checkbox updating needed since we removed hardcoded checkboxes
    }
    
    updateHiddenInput() {
        this.hiddenInput.value = JSON.stringify(this.selectedClasses.map(c => c.id));
    }
}

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', () => {
    const classAutocomplete = new ClassAutocomplete();
    classAutocomplete.init();
});
