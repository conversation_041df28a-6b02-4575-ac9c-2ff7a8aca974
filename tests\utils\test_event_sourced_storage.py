"""
Unit tests for the EventSourcedStorage class.
"""
import uuid
import pytest
from datetime import datetime
from typing import List, Dict, Any

from app.src.models.events import (
    BaseEvent, ParkingLotCreated, ParkingLotDeleted, Parking<PERSON>otRenamed,
    Parking<PERSON>otImageUpdated, Parking<PERSON><PERSON>Added, Parking<PERSON>lotRemoved, Parking<PERSON>lot<PERSON>oneUpdated
)
from app.src.models.orm import ParkingEventORM
from app.src.models.parking import ParkingLot, ParkingSlot
from app.src.models.geometry import Polygon, Point
from app.src.utils.event_store_interface import EventStore  # Corrected import
from app.src.utils.event_sourced_storage import EventSourcedStorage
from app.src.utils.mapper import Mapper


class MockEventStore(EventStore):  # Corrected inheritance
    """Mock implementation of the EventStore interface for testing."""

    def __init__(self):
        self.events: Dict[str, List[ParkingEventORM]] = {}

    def append_event(self, event_orm: ParkingEventORM) -> bool:
        """Append an event to the store."""
        # Arrange (within mock)
        if event_orm.lot_id not in self.events:
            self.events[event_orm.lot_id] = []

        # Act (within mock) - Assign sequence ID
        event_orm.sequence_id = len(self.events[event_orm.lot_id]) + 1
        self.events[event_orm.lot_id].append(event_orm)

        # Assert (within mock) - Return success
        return True

    def get_raw_events_for_aggregate(self, aggregate_id: str) -> List[ParkingEventORM]:
        """Get all events for an aggregate."""
        # Arrange/Act (within mock)
        orm_events = self.events.get(aggregate_id, [])
        # Assert (within mock) - Return ORM events
        return orm_events

    def get_all_aggregate_ids(self) -> List[str]:
        """Get all aggregate IDs."""
        # Arrange/Act/Assert (within mock)
        return list(self.events.keys())

    def clear(self):
        """Clear all events - helper method for tests."""
        self.events.clear()


@pytest.fixture
def mock_event_store() -> MockEventStore:  # Add type hint
    """Fixture providing a mock event store."""
    # Arrange
    store = MockEventStore()
    yield store
    # Cleanup (part of Arrange)
    store.clear()


@pytest.fixture
def event_sourced_storage(mock_event_store: MockEventStore) -> EventSourcedStorage:  # Add type hints
    """Fixture providing an EventSourcedStorage with a mock event store."""
    # Arrange
    return EventSourcedStorage(mock_event_store)


class TestEventSourcedStorage:
    """Tests for the EventSourcedStorage class."""

    def test_get_nonexistent_parking_lot(self, event_sourced_storage: EventSourcedStorage):
        """Test retrieving a parking lot that doesn't exist."""
        # Arrange
        non_existent_id = str(uuid.uuid4())

        # Act
        result = event_sourced_storage.get_parking_lot(non_existent_id)

        # Assert
        assert result is None

    def test_create_parking_lot(self, event_sourced_storage: EventSourcedStorage, mock_event_store: MockEventStore):
        """Test creating a parking lot."""
        # Arrange
        lot_name = "Test Parking Lot"
        image_path = "/path/to/image.jpg"

        # Act
        lot_id = event_sourced_storage.create_parking_lot(lot_name, image_path)

        # Assert: Verify lot_id, event persistence, and retrieved lot state
        assert lot_id is not None
        assert isinstance(lot_id, str)
        assert lot_id in mock_event_store.events
        assert len(mock_event_store.events[lot_id]) == 1

        event_orm = mock_event_store.events[lot_id][0]
        assert event_orm.event_type == "ParkingLotCreated"
        assert event_orm.lot_id == lot_id
        assert event_orm.payload.get("name") == lot_name
        assert event_orm.payload.get("image_path") == image_path

        retrieved_lot = event_sourced_storage.get_parking_lot(lot_id)
        assert retrieved_lot is not None
        assert retrieved_lot.id == lot_id
        assert retrieved_lot.name == lot_name
        assert retrieved_lot.image_path == image_path

    def test_rename_parking_lot(self, event_sourced_storage: EventSourcedStorage):
        """Test renaming a parking lot."""
        # Arrange: Create a parking lot first
        original_name = "Original Name"
        lot_id = event_sourced_storage.create_parking_lot(original_name)
        assert lot_id is not None
        new_name = "New Name"

        # Act: Rename the parking lot
        renamed = event_sourced_storage.rename_parking_lot(lot_id, new_name)

        # Assert: Verify rename result and updated lot state
        assert renamed is True
        retrieved_lot = event_sourced_storage.get_parking_lot(lot_id)
        assert retrieved_lot is not None
        assert retrieved_lot.name == new_name

    def test_delete_parking_lot(self, event_sourced_storage: EventSourcedStorage):
        """Test deleting a parking lot."""
        # Arrange: Create a parking lot first
        lot_id = event_sourced_storage.create_parking_lot("Parking Lot To Delete")
        assert lot_id is not None

        # Act: Delete the parking lot
        deleted = event_sourced_storage.delete_parking_lot(lot_id)

        # Assert: Verify delete result and that lot is no longer retrievable
        assert deleted is True
        retrieved_lot = event_sourced_storage.get_parking_lot(lot_id)
        assert retrieved_lot is None

    def test_add_parking_slot(self, event_sourced_storage: EventSourcedStorage):
        """Test adding a parking slot to a lot."""
        # Arrange: Create a parking lot first
        lot_id = event_sourced_storage.create_parking_lot("Parking Lot With Slots")
        assert lot_id is not None
        polygon = Polygon(points=[
            Point(x=10, y=10), Point(x=20, y=10), Point(x=20, y=20), Point(x=10, y=20)
        ])
        zone_points = [p.to_dict() for p in polygon.points]
        overlap_threshold = 0.4

        # Act: Add a parking slot
        slot_id = event_sourced_storage.add_parking_slot(lot_id, "Test Slot", zone_points, overlap_threshold)

        # Assert: Verify slot_id and updated lot state
        assert slot_id is not None
        assert isinstance(slot_id, str)

        retrieved_lot = event_sourced_storage.get_parking_lot(lot_id)
        assert retrieved_lot is not None
        assert len(retrieved_lot.slots) == 1
        added_slot = retrieved_lot.slots[0]
        assert added_slot.id == slot_id
        assert added_slot.overlap_threshold == overlap_threshold
        assert added_slot.detection_zone is not None
        assert len(added_slot.detection_zone.points) == len(zone_points)
        assert added_slot.detection_zone.points[0].x == zone_points[0]['x']

    def test_update_slot_zone(self, event_sourced_storage: EventSourcedStorage):
        """Test updating a parking slot's zone."""
        # Arrange: Create a lot and add a slot
        lot_id = event_sourced_storage.create_parking_lot("Parking Lot For Zone Update")
        assert lot_id is not None
        original_polygon = Polygon(points=[
            Point(x=10, y=10), Point(x=20, y=10), Point(x=20, y=20), Point(x=10, y=20)
        ])
        original_zone_points = [p.to_dict() for p in original_polygon.points]
        slot_id = event_sourced_storage.add_parking_slot(lot_id, "Test Slot", original_zone_points, 0.3)
        assert slot_id is not None

        new_polygon = Polygon(points=[
            Point(x=15, y=15), Point(x=25, y=15), Point(x=25, y=25), Point(x=15, y=25)
        ])
        new_zone_points = [p.to_dict() for p in new_polygon.points]

        # Act: Update the slot's zone points
        updated = event_sourced_storage.update_slot_zone(lot_id, slot_id, new_zone_points)

        # Assert: Verify update result and updated slot state
        assert updated is True
        retrieved_lot = event_sourced_storage.get_parking_lot(lot_id)
        assert retrieved_lot is not None
        slot = next((s for s in retrieved_lot.slots if s.id == slot_id), None)
        assert slot is not None
        assert slot.detection_zone is not None
        assert len(slot.detection_zone.points) == len(new_zone_points)
        assert slot.detection_zone.points[0].x == new_zone_points[0]['x']

    def test_remove_parking_slot(self, event_sourced_storage: EventSourcedStorage):
        """Test removing a parking slot from a lot."""  # Arrange: Create a lot and add a slot
        lot_id = event_sourced_storage.create_parking_lot("Parking Lot For Slot Removal")
        assert lot_id is not None
        polygon = Polygon(points=[
            Point(x=10, y=10), Point(x=20, y=10), Point(x=20, y=20), Point(x=10, y=20)
        ])
        zone_points = [p.to_dict() for p in polygon.points]
        slot_id = event_sourced_storage.add_parking_slot(lot_id, "Test Slot", zone_points, 0.3)
        assert slot_id is not None

        # Act: Remove the slot
        removed = event_sourced_storage.remove_parking_slot(lot_id, slot_id)

        # Assert: Verify removal result and updated lot state
        assert removed is True
        retrieved_lot = event_sourced_storage.get_parking_lot(lot_id)
        assert retrieved_lot is not None
        assert len(retrieved_lot.slots) == 0

    def test_update_parking_lot_image(self, event_sourced_storage: EventSourcedStorage):
        """Test updating a parking lot's image."""
        # Arrange: Create a lot with an image
        original_image = "/path/to/original.jpg"
        lot_id = event_sourced_storage.create_parking_lot("Parking Lot With Image", original_image)
        assert lot_id is not None
        new_image = "/path/to/new.jpg"

        # Act: Update the image
        updated = event_sourced_storage.update_parking_lot_image(lot_id, new_image)

        # Assert: Verify update result and updated lot state
        assert updated is True
        retrieved_lot = event_sourced_storage.get_parking_lot(lot_id)
        assert retrieved_lot is not None
        assert retrieved_lot.image_path == new_image

    def test_reconstruct_full_parking_lot(self, event_sourced_storage: EventSourcedStorage):
        """Test reconstructing a parking lot with multiple events."""
        # Arrange: Perform a series of operations (create, add slots, rename, remove slot, update image, update zones)
        lot_id = event_sourced_storage.create_parking_lot("Complex Lot", "/path/to/image.jpg")
        assert lot_id is not None

        slot_ids = []
        for i in range(3):
            polygon = Polygon(points=[
                Point(x=i * 10, y=i * 10), Point(x=i * 10 + 10, y=i * 10),
                Point(x=i * 10 + 10, y=i * 10 + 10), Point(x=i * 10, y=i * 10 + 10)
            ])
            zone_points = [p.to_dict() for p in polygon.points]
            slot_id = event_sourced_storage.add_parking_slot(lot_id, f"Test Slot {i}", zone_points, 0.3 + i * 0.1)
            assert slot_id is not None
            slot_ids.append(slot_id)

        event_sourced_storage.rename_parking_lot(lot_id, "Renamed Complex Lot")
        event_sourced_storage.remove_parking_slot(lot_id, slot_ids[1])
        event_sourced_storage.update_parking_lot_image(lot_id, "/path/to/updated.jpg")

        new_polygon_0 = Polygon(points=[Point(x=5, y=5), Point(x=15, y=5), Point(x=15, y=15), Point(x=5, y=15)])
        new_zone_points_0 = [p.to_dict() for p in new_polygon_0.points]
        event_sourced_storage.update_slot_zone(lot_id, slot_ids[0], new_zone_points_0)

        new_polygon_2 = Polygon(points=[Point(x=25, y=25), Point(x=35, y=25), Point(x=35, y=35), Point(x=25, y=35)])
        new_zone_points_2 = [p.to_dict() for p in new_polygon_2.points]
        event_sourced_storage.update_slot_zone(lot_id, slot_ids[2], new_zone_points_2)

        # Act: Retrieve the parking lot, triggering reconstruction
        retrieved_lot = event_sourced_storage.get_parking_lot(lot_id)

        # Assert: Verify the final reconstructed state
        assert retrieved_lot is not None
        assert retrieved_lot.name == "Renamed Complex Lot"
        assert retrieved_lot.image_path == "/path/to/updated.jpg"
        assert len(retrieved_lot.slots) == 2

        slot_ids_in_lot = {s.id for s in retrieved_lot.slots}
        assert slot_ids[0] in slot_ids_in_lot
        assert slot_ids[1] not in slot_ids_in_lot
        assert slot_ids[2] in slot_ids_in_lot

        for slot in retrieved_lot.slots:
            if slot.id == slot_ids[0]:
                assert slot.detection_zone.points[0].x == 5
                assert slot.overlap_threshold == 0.3
            elif slot.id == slot_ids[2]:
                assert slot.detection_zone.points[0].x == 25
                assert slot.overlap_threshold == 0.5
