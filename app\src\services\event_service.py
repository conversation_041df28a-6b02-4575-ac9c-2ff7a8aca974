from datetime import datetime
from typing import Dict, List, Optional

from src.models.events import ParkingEvent
from src.models.status import SlotStatus
from src.utils import logger
from src.config.settings import get_settings

LOGGER = logger.setup_logger(__name__)

# For testing purposes
EVENT_CLIENTS = []

class SlotStatusUpdateEvent:
    """Event representing an update to a parking slot's status."""
    
    def __init__(self, slot_id: str, lot_id: str, status: SlotStatus, timestamp: datetime = None):
        self.event_type = "slot_status_update"
        self.slot_id = slot_id
        self.lot_id = lot_id
        self.status = status
        self.timestamp = timestamp or datetime.now()
        
    def to_dict(self) -> dict:
        """Convert the event to a dictionary for serialization."""
        return {
            "event_type": self.event_type,
            "slot_id": self.slot_id,
            "lot_id": self.lot_id,
            "status": self.status.value,
            "timestamp": self.timestamp.isoformat()
        }

class EventService:
    """
    Service for emitting various events through SocketIO and other channels.
    Implemented as a singleton to ensure there's only one instance.
    """
    _instance = None
    
    def __new__(cls):
        """Ensure singleton pattern - only one instance is created."""
        if cls._instance is None:
            cls._instance = super(EventService, cls).__new__(cls)
            cls._instance._initialize()
        return cls._instance
    
    def _initialize(self):
        """Initialize the event service."""
        LOGGER.info("Initializing EventService")
        # Any setup code goes here
        
    @staticmethod
    def emit_status_update(slot_id: str, lot_id: str, status: SlotStatus, timestamp: datetime = None):
        """
        Emit a status update event.
        
        Args:
            slot_id: ID of the parking slot
            lot_id: ID of the parking lot
            status: New status of the slot
            timestamp: When the status change occurred (defaults to now)
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        event = SlotStatusUpdateEvent(
            slot_id=slot_id,
            lot_id=lot_id,
            status=status,
            timestamp=timestamp
        )
        
        # Publish the event to all registered clients
        for client in EVENT_CLIENTS:
            client.publish_event(event)
    
    def emit_parking_event(self, lot_id: str, event: ParkingEvent):
        """
        Emit a parking event through Socket.IO.
        
        Args:
            lot_id: ID of the parking lot
            event: The event to emit
        """
        try:
            socketio = get_settings().socketio
            if socketio:
                LOGGER.info(f"Emitting parking_event for lot {lot_id}: {event.event_type}")
                socketio.emit('parking_event', {
                    'lot_id': lot_id,
                    **event.to_dict()
                }, namespace='/dashboard')
            else:
                LOGGER.error("SocketIO instance not found in settings.")
        except Exception as e:
            LOGGER.exception("Error emitting SocketIO event: %s", 'parking_event')

    def emit_slot_status_update(self, lot_id: str, slot_id: str, is_occupied: bool):
        """
        Emit a slot status update through Socket.IO.
        
        Args:
            lot_id: ID of the parking lot
            slot_id: ID of the slot
            is_occupied: Whether the slot is occupied
        """
        try:
            socketio = get_settings().socketio
            if socketio:
                LOGGER.info(f"Emitting slot_status_updated for lot {lot_id}, slot {slot_id}: {'occupied' if is_occupied else 'free'}")
                socketio.emit('slot_status_updated', {
                    'lot_id': lot_id,
                    'slot_id': slot_id,
                    'is_occupied': is_occupied,
                    'payload': {
                        'slot_id': slot_id,
                        'is_occupied': is_occupied
                    }
                }, namespace='/dashboard')
            else:
                LOGGER.error("SocketIO instance not found in settings.")
        except Exception as e:
            LOGGER.exception("Error emitting SocketIO event: %s", 'slot_status_updated')

    def emit_image_update(self, lot_id: str, image_path: str, viewpoint_id: str = None):
        """
        Emit an image update through Socket.IO.

        Args:
            lot_id: ID of the parking lot
            image_path: Path to the updated image (e.g., 'uploads/lot_id/image.jpg')
            viewpoint_id: Optional ID of the specific viewpoint that was updated
        """
        try:
            socketio = get_settings().socketio
            if socketio:
                # image_path is relative to the static folder, e.g., "uploads/lot_id/image.jpg"
                # Construct the full URI path for new_image_path
                if image_path.startswith('/'):
                    # Defensive: if it somehow already has a leading slash
                    full_image_uri = f"/static{image_path}" if not image_path.startswith("/static/") else image_path
                else:
                    full_image_uri = f"/static/{image_path}"

                event_data = {
                    'lot_id': lot_id,
                    'image_path': image_path,  # Original relative path
                    'new_image_path': full_image_uri,  # New field with full URI path
                    'viewpoint_id': viewpoint_id,  # ID of the updated viewpoint (None for main lot)
                    'is_main_lot_update': viewpoint_id is None,  # Flag to indicate if this is a main lot update
                    'payload': {
                        'image_path': image_path,
                        'new_image_path': full_image_uri,
                        'viewpoint_id': viewpoint_id
                    }
                }

                LOGGER.info(f"Emitting image_updated for lot {lot_id}: {image_path}, viewpoint_id: {viewpoint_id}, new_image_path: {full_image_uri}")

                # Handle async emission from sync context (e.g., background threads)
                import asyncio
                try:
                    # Try to get the current event loop
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # If we're in an async context, schedule the emission
                        asyncio.create_task(socketio.emit('image_updated', event_data, namespace='/dashboard'))
                    else:
                        # If not in async context, run it
                        asyncio.run(socketio.emit('image_updated', event_data, namespace='/dashboard'))
                except RuntimeError:
                    # No event loop in current thread, create a new one
                    asyncio.run(socketio.emit('image_updated', event_data, namespace='/dashboard'))
            else:
                LOGGER.error("SocketIO instance not found in settings.")
        except Exception as e:
            LOGGER.exception("Error emitting SocketIO event: %s", 'image_updated')

    def emit_detection_status(self, lot_id: str, status: str, updates=None, error=None):
        """
        Emit a detection status update through Socket.IO.
        
        Args:
            lot_id: ID of the parking lot
            status: Status of the detection (processing, completed, error)
            updates: Optional updates to include
            error: Optional error message
        """
        try:
            socketio = get_settings().socketio
            if socketio:
                event_data = {
                    'lot_id': lot_id,
                    'status': status,
                    'payload': {
                        'status': status
                    }
                }
                
                if updates is not None:
                    event_data['updates'] = updates
                    event_data['payload']['updates'] = updates
                    
                if error is not None:
                    event_data['error'] = error
                    event_data['payload']['error'] = error
                    
                LOGGER.info(f"Emitting detection_status for lot {lot_id}: {status}")
                socketio.emit('detection_status', event_data, namespace='/dashboard')
            else:
                LOGGER.error("SocketIO instance not found in settings.")
        except Exception as e:
            LOGGER.exception("Error emitting SocketIO event: %s", 'detection_status')

# Singleton instance, accessible as event_service
event_service = EventService()

# For backward compatibility
def emit_parking_event(lot_id: str, event: ParkingEvent):
    """Legacy function for backward compatibility."""
    event_service.emit_parking_event(lot_id, event)

def emit_slot_status_update(lot_id: str, slot_id: str, is_occupied: bool):
    """Legacy function for backward compatibility."""
    event_service.emit_slot_status_update(lot_id, slot_id, is_occupied)

def emit_image_update(lot_id: str, image_path: str, viewpoint_id: str = None):
    """Legacy function for backward compatibility."""
    event_service.emit_image_update(lot_id, image_path, viewpoint_id)

def emit_detection_status(lot_id: str, status: str, updates=None, error=None):
    """Legacy function for backward compatibility."""
    event_service.emit_detection_status(lot_id, status, updates, error)
