{% extends "base_layout.html" %}

{% block title %}Edit Parking Zones - {{ lot.name }}{% endblock %}

{% block extra_css %}
<link href="/static/css/zone_editor.css" rel="stylesheet">
<link href="/static/css/zoom_styles.css" rel="stylesheet">
{% endblock %}

{% block content %}
<h1>Edit Parking Zones - {{ lot.name }}</h1>

<!-- Viewpoint Selection Menu -->
<div class="row mt-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1">Editing Viewpoint</h6>
                        <small class="text-muted">Select which viewpoint's parking slots to edit</small>
                    </div>
                    <viewpoint-selector
                        id="viewpoint-selector"
                        lot-id="{{ lot_id }}"
                        show-actions="false">
                    </viewpoint-selector>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-9">
        <!-- Add zoom controls above the image container -->
        <div id="zoom-controls" class="mb-2">
            <button id="zoom-in-btn" class="btn btn-sm btn-light" title="Zoom In"><i class="bi bi-zoom-in"></i> Zoom In</button>
            <button id="zoom-out-btn" class="btn btn-sm btn-light" title="Zoom Out"><i class="bi bi-zoom-out"></i> Zoom Out</button>
            <button id="zoom-reset-btn" class="btn btn-sm btn-light" title="Reset Zoom"><i class="bi bi-aspect-ratio"></i> Reset</button>
            <span id="zoom-level" class="ms-2">100%</span>

            <!-- Add minimap toggle checkbox -->
            <div class="form-check form-check-inline ms-3">
                <input class="form-check-input" type="checkbox" id="minimap-toggle" checked>
                <label class="form-check-label" for="minimap-toggle">Show Minimap</label>
            </div>
        </div>

        <!-- Zoom wrapper div to properly contain the zoomed image -->
        <div class="zoom-wrapper">
            <!-- Add minimap container for preview when zoomed -->
            <div id="minimap-container" class="minimap-container minimap-hidden">
                {% if default_viewpoint and default_viewpoint.image_path %}
                <img src="/static/{{ default_viewpoint.image_path }}" alt="Minimap">
                {% elif lot.image_path %}
                <img src="/static/{{ lot.image_path }}" alt="Minimap">
                {% else %}
                <div class="no-image-placeholder">
                    <i class="bi bi-camera" style="font-size: 2rem; color: #dee2e6;"></i>
                    <p>No image available</p>
                </div>
                {% endif %}
                <div id="minimap-viewport" class="minimap-viewport"></div>
            </div>

            <div id="image-container">
                {% if default_viewpoint and default_viewpoint.image_path %}
                <img id="parking-image" src="/static/{{ default_viewpoint.image_path }}" alt="Parking Lot">
                {% elif lot.image_path %}
                <img id="parking-image" src="/static/{{ lot.image_path }}" alt="Parking Lot">
                {% else %}
                <div class="no-image-placeholder">
                    <i class="bi bi-camera" style="font-size: 4rem; color: #dee2e6;"></i>
                    <p>No image available for this parking lot</p>
                    <p class="text-muted">Please upload an image or configure a stream viewpoint</p>
                </div>
                {% endif %}
                <!-- Zones will be dynamically added here -->
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Tools</h5>
            </div>
            <div class="card-body">
                <button id="add-zone-btn" class="btn btn-primary mb-3">Add Parking Slot</button>
                <button id="cancel-draw-btn" class="btn btn-secondary mb-3 d-none">Cancel Drawing</button>
                <button id="finish-draw-btn" class="btn btn-success mb-3 d-none">Finish Drawing</button>

                <div id="zone-form" class="d-none">
                    <h6>New Parking Slot</h6>
                    <div class="mb-3">
                        <label for="slot-id" class="form-label">Slot Name</label>
                        <input type="text" class="form-control" id="slot-id" placeholder="e.g., A1" autocomplete="off">
                        <div class="form-text">
                            <small id="slot-mapping-info" class="text-muted"></small>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="overlap-threshold" class="form-label">Coverage Threshold <span id="overlap-value">30%</span></label>
                        <input type="range" class="form-range" id="overlap-threshold" min="0" max="1" step="0.05" value="0.3">
                        <div class="form-text">Required vehicle coverage to mark slot as occupied.</div>
                    </div>
                    <button id="save-zone-btn" class="btn btn-success">Save</button>
                    <button id="cancel-zone-btn" class="btn btn-secondary">Cancel</button>
                </div>

                <div id="edit-zone-controls" class="d-none mb-3">
                    <h6>Edit Parking Slot</h6>
                    <div class="mb-3">
                        <label for="edit-overlap-threshold" class="form-label">Coverage Threshold <span id="edit-overlap-value">30%</span></label>
                        <input type="range" class="form-range" id="edit-overlap-threshold" min="0" max="1" step="0.05" value="0.3">
                        <div class="form-text">Required vehicle coverage to mark slot as occupied.</div>
                    </div>
                    <button id="update-zone-btn" class="btn btn-primary">Save Changes</button>
                    <button id="cancel-edit-btn" class="btn btn-secondary">Cancel</button>
                    <button id="delete-zone-btn" class="btn btn-danger mt-2">Delete Zone</button>
                </div>

                <hr>
                <h6>Parking Slots</h6>
                <ul id="zones-list" class="list-group">
                    <!-- Zone list items will be added dynamically -->
                    {% if lot.slots %}
                        {% for slot in lot.slots %}
                        <li class="list-group-item d-flex justify-content-between align-items-center" data-slot-id="{{ slot.id }}">
                            {{ slot.slot_name or slot.id }}
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-primary edit-zone me-2" title="Edit Zone" data-zone-id="{{ slot.id }}">
                                    <i class="bi bi-pencil-square"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger delete-zone" title="Delete Zone" data-zone-id="{{ slot.id }}">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </li>
                        {% endfor %}
                    {% else %}
                        <li id="no-zones-message" class="list-group-item">No parking slots defined yet</li>
                    {% endif %}
                </ul>
            </div>
        </div>
        <div class="mt-3">
            <a href="/dashboard/{{ lot_id }}" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>
</div>

<!-- Confirmation Modal for Deletion -->
<div class="modal fade" id="confirmation-modal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this parking slot?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirm-delete-btn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Pass required data from Flask to JavaScript
    const LOT_ID = '{{ lot_id }}';
    const DEFAULT_VIEWPOINT_ID = {{ default_viewpoint.id | tojson if default_viewpoint else 'null' | safe }};
</script>
<script src="/static/js/viewpoint-selector.js"></script>
<script src="/static/js/polygon_utils.js"></script>
<script src="/static/js/polygon_drawing.js"></script>
<script src="/static/js/zoom_manager.js"></script>
<script src="/static/js/zone_editor.js"></script>
{% endblock %}
