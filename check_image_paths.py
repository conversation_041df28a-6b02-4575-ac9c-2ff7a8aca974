#!/usr/bin/env python3
import sys
import os
sys.path.append('app')

from src.utils.event_sourced_storage import EventSourcedStorage
from src.utils.sqlalchemy_event_store import SQLAlchemyEventStore

def main():
    try:
        # Initialize storage
        event_store = SQLAlchemyEventStore()
        storage = EventSourcedStorage(event_store)

        # Get all parking lots
        lots = storage.get_all_parking_lots()
        print(f'Found {len(lots)} parking lots')

        for lot in lots:
            print(f'\nLot: {lot.name} (ID: {lot.id})')
            
            # Check viewpoints and their image paths
            active_viewpoints = lot.get_active_viewpoints()
            print(f'  Active viewpoints: {len(active_viewpoints)}')
            
            for viewpoint in active_viewpoints:
                print(f'    Viewpoint: {viewpoint.name} (ID: {viewpoint.id})')
                print(f'      Image path: {viewpoint.image_path}')
                
                # Check if file exists using different path resolutions
                if viewpoint.image_path:
                    # Try relative to current directory
                    path1 = viewpoint.image_path
                    exists1 = os.path.exists(path1)
                    print(f'      Exists (relative): {exists1} - {path1}')
                    
                    # Try relative to app directory
                    path2 = os.path.join('app', viewpoint.image_path)
                    exists2 = os.path.exists(path2)
                    print(f'      Exists (app/): {exists2} - {path2}')
                    
                    # Try absolute path
                    path3 = os.path.abspath(viewpoint.image_path)
                    exists3 = os.path.exists(path3)
                    print(f'      Exists (absolute): {exists3} - {path3}')
                    
                    # Try with app prefix
                    path4 = os.path.abspath(os.path.join('app', viewpoint.image_path))
                    exists4 = os.path.exists(path4)
                    print(f'      Exists (app absolute): {exists4} - {path4}')
                    
                    # Show current working directory
                    print(f'      Current working dir: {os.getcwd()}')
                else:
                    print(f'      No image path set')

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
