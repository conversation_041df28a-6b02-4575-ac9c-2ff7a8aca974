{% extends "base_layout.html" %}

{% block title %}Parking Management Dashboard{% endblock %}

{% block extra_css %}
    <link href="/static/css/shared_dashboard.css" rel="stylesheet">
    <link href="/static/css/dashboard.css" rel="stylesheet">
    <link href="/static/css/fluent_effect.css" rel="stylesheet">
{% endblock %}

{% block content %}
    <h1 class="mb-4">Parking Lots</h1>

    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
        {% for lot in parking_lots %}
            <div class="col">
                <div class="card h-100">
                    {% set display_image = lot.get_display_image_path() %}
                    {% if display_image %}
                        <a href="/dashboard/{{ lot.id }}" class="card-img-container">
                            <img src="/static/{{ display_image }}" class="card-img-top lot-image"
                                 alt="{{ lot.name }}" data-lot-id="{{ lot.id }}">
                            <div id="parking-slots-overlay-{{ lot.id }}" class="parking-slots-overlay"></div>
                            {% if lot.has_active_streams() %}
                                <div class="stream-indicator">
                                    <span class="badge bg-success">LIVE</span>
                                </div>
                            {% endif %}
                        </a>
                    {% endif %}

                    <div class="card-body">
                        <h5 class="card-title">{{ lot.name }}</h5>

                        <div class="stats d-flex justify-content-around mb-3">
                            <div class="stat text-center">
                                <h6>Free Spots</h6>
                                <p><span class="status-badge free">{{ lot.get_free_slot_count() }}</span></p>
                            </div>
                            <div class="stat text-center">
                                <h6>Occupied Spots</h6>
                                <p><span class="status-badge occupied">{{ lot.get_busy_slot_count() }}</span></p>
                            </div>
                        </div>

                        <div class="occupancy-meter mb-3">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar"
                                     style="width: {{ lot.get_occupancy_rate() }}%;"
                                     aria-valuenow="{{ lot.get_occupancy_rate() }}"
                                     aria-valuemin="0"
                                     aria-valuemax="100">
                                </div>
                            </div>
                            <div class="progress-label text-center mt-1">
                                {{ "%.1f"|format(lot.get_occupancy_rate()) }}% Occupied
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <div class="action-buttons">
                                <a href="/dashboard/{{ lot.id }}" class="btn btn-primary">
                                    <i class="bi bi-eye"></i> View Details
                                </a>
                            </div>
                            <small class="text-muted">Last
                                updated: {{ lot.last_updated.strftime('%Y-%m-%d %H:%M:%S') if lot.last_updated else 'Never' }}</small>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}

        <!-- Add New Lot Card -->
        <div class="col">
            <a href="/upload" class="text-decoration-none">
                <div class="card h-100 border-dashed">
                    <div class="card-body d-flex flex-column align-items-center justify-content-center text-center">
                        <i class="bi bi-plus-circle display-4 mb-3 text-primary"></i>
                        <h5 class="card-title mb-3">Add New Parking Lot</h5>
                    </div>
                </div>
            </a>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.4/socket.io.min.js"></script>
    <script src="/static/js/fluent_effect.js"></script>
    <script src="/static/js/all_lots_dashboard.js"></script>
{% endblock %}