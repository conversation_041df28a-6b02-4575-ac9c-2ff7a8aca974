#!/usr/bin/env python3
import sys
import os
sys.path.append('app')

from src.utils.event_sourced_storage import EventSourcedStorage
from src.utils.sqlalchemy_event_store import SQLAlchemyEventStore
from src.services.occupancy_manager import OccupancyManager
from datetime import datetime

def main():
    try:
        # Initialize storage
        event_store = SQLAlchemyEventStore()
        storage = EventSourcedStorage(event_store)

        # Get all parking lots
        lots = storage.get_all_parking_lots()
        print(f'Found {len(lots)} parking lots')

        for lot in lots:
            print(f'\nLot: {lot.name} (ID: {lot.id})')
            
            if lot.slots:
                # Test updating the first slot to occupied
                test_slot = lot.slots[0]
                print(f'Testing occupancy update for slot: {test_slot.slot_name} (ID: {test_slot.id})')
                print(f'Current state: {"occupied" if test_slot.is_occupied else "free"}')
                
                # Try to update the slot to occupied
                print('Attempting to update slot to occupied...')
                success = OccupancyManager.update_slot_status(lot.id, test_slot.id, True)
                print(f'Update result: {success}')
                
                if success:
                    # Refresh the lot state and check if the update worked
                    print('Refreshing lot state...')
                    refreshed_lot = storage.get_parking_lot(lot.id)
                    if refreshed_lot:
                        refreshed_slot = refreshed_lot.get_slot(test_slot.id)
                        if refreshed_slot:
                            print(f'Slot state after update: {"occupied" if refreshed_slot.is_occupied else "free"}')
                            
                            # Check occupancy stats
                            occupied_count = sum(1 for slot in refreshed_lot.slots if slot.is_occupied)
                            print(f'Total occupied slots: {occupied_count}/{len(refreshed_lot.slots)}')
                            print(f'Occupancy rate: {refreshed_lot.get_occupancy_rate():.2%}')
                        else:
                            print('Could not find refreshed slot')
                    else:
                        print('Could not refresh lot state')
                
                # Check if occupancy events were created
                events = storage._get_events_for_lot(lot.id)
                occupancy_events = [e for e in events if 'Occupancy' in e.event_type]
                print(f'Total occupancy events after update: {len(occupancy_events)}')
                
                if occupancy_events:
                    latest_event = occupancy_events[-1]
                    print(f'Latest occupancy event: {latest_event.timestamp} - {latest_event.payload}')

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
