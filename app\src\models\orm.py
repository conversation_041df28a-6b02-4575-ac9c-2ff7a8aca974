from sqlalchemy import Column, String, Integer, Float, <PERSON>olean, Foreign<PERSON>ey, DateTime, Text, JSON, UniqueConstraint, ForeignKeyConstraint
from sqlalchemy.orm import relationship, declarative_base
from datetime import datetime

Base = declarative_base()

class DetectionEngineConfigORM(Base):
    __tablename__ = 'detection_engine_config'
    id = Column(Integer, primary_key=True)
    engine_type = Column(String, default='yolo')
    is_enabled = Column(Boolean, default=False)
    update_interval = Column(Integer, default=60)
    parameters = Column(JSON, default={})

class ParkingLotORM(Base):
    __tablename__ = 'parking_lot'
    id = Column(String(36), primary_key=True)
    name = Column(String(255), nullable=False)
    image_path = Column(String(1024))
    detection_config_id = Column(Integer, ForeignKey('detection_engine_config.id'))
    detection_config = relationship('DetectionEngineConfigORM', backref='parking_lot', uselist=False)
    slots = relationship('ParkingSlotORM', back_populates='lot', cascade='all, delete-orphan')
    events = relationship('ParkingEventORM', back_populates='lot', cascade='all, delete-orphan')

class ParkingSlotORM(Base):
    __tablename__ = 'parking_slot'
    id = Column(String(36), primary_key=True)
    slot_name = Column(String(255), nullable=False)
    lot_id = Column(String(36), ForeignKey('parking_lot.id'), primary_key=True)
    is_occupied = Column(Boolean, default=False)
    overlap_threshold = Column(Float, default=0.3)
    lot = relationship('ParkingLotORM', back_populates='slots')
    detection_zone = relationship('DetectionZoneORM', back_populates='slot', uselist=False, cascade='all, delete-orphan')
    __table_args__ = (UniqueConstraint('id', 'lot_id', name='_lot_slot_uc'),)

class DetectionZoneORM(Base):
    __tablename__ = 'detection_zone'
    id = Column(Integer, primary_key=True)
    slot_id = Column(String(36))
    lot_id = Column(String(36))
    __table_args__ = (
        ForeignKeyConstraint(['slot_id', 'lot_id'], ['parking_slot.id', 'parking_slot.lot_id'], ondelete='CASCADE'),
    )
    slot = relationship('ParkingSlotORM', back_populates='detection_zone')
    points = relationship('PolygonPointORM', back_populates='zone', cascade='all, delete-orphan')

class PolygonPointORM(Base):
    __tablename__ = 'polygon_point'
    id = Column(Integer, primary_key=True)
    zone_id = Column(Integer, ForeignKey('detection_zone.id', ondelete='CASCADE'))
    x = Column(Float, nullable=False)
    y = Column(Float, nullable=False)
    order = Column(Integer, nullable=False)
    zone = relationship('DetectionZoneORM', back_populates='points')

class ParkingEventORM(Base):
    __tablename__ = 'parking_event'
    id = Column(Integer, primary_key=True) # Internal DB ID
    sequence_id = Column(Integer, nullable=False) # Sequence within an aggregate (lot)
    lot_id = Column(String(36), ForeignKey('parking_lot.id'), nullable=False)
    event_type = Column(String(50), nullable=False)
    description = Column(Text) # Optional human-readable info
    payload = Column(JSON, nullable=False) # Structured event data
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)
    lot = relationship('ParkingLotORM', back_populates='events')

    # Add a unique constraint for optimistic concurrency control (optional but good practice)
    __table_args__ = (UniqueConstraint('lot_id', 'sequence_id', name='_lot_seq_uc'),)
