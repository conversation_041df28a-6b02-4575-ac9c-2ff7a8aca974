document.addEventListener('DOMContentLoaded', function () {
    // Elements
    const runDetectionBtn = document.getElementById('run-detection-btn');
    const originalRunDetectionText = runDetectionBtn ? runDetectionBtn.innerHTML : '';
    const occupancyProgress = document.getElementById('occupancy-progress');
    const occupancyLabel = document.querySelector('.progress-label');
    const freeCount = document.getElementById('free-count');
    const occupiedCount = document.getElementById('occupied-count');
    const eventLogBody = document.getElementById('event-log-body');
    const clearLogBtn = document.getElementById('clear-log-btn');
    const parkingVisualization = document.getElementById('parking-visualization');

    // Ensure LOT_ID is available
    const lotId = window.LOT_ID;
    if (!lotId) {
        console.error('LOT_ID not found. Dashboard cannot initialize properly.');
        if(runDetectionBtn) runDetectionBtn.disabled = true;
        return;
    }

    // State management
    let currentViewpointId = null;
    let detectionInProgress = false;
    const eventsPerPage = 50;

    // Initialize
    initializeViewpoint();
    loadEvents(1);

    // Ensure we start with fresh slot data by invalidating any existing cache
    if (window.ParkingVisualization) {
        window.ParkingVisualization.invalidateCache(lotId);
    }

    // Setup viewpoint selector after a short delay to ensure it's rendered
    setTimeout(() => {
        setupViewpointSelector();
    }, 100);

    // Setup parking visualization component listeners
    if (parkingVisualization) {
        parkingVisualization.addEventListener('data-loaded', (event) => {
            console.log('Dashboard: Parking visualization data loaded:', event.detail);
            currentViewpointId = event.detail.viewpointId;
        });

        parkingVisualization.addEventListener('slot-updated', (event) => {
            console.log('Dashboard: Slot updated via component:', event.detail);

            // Add event to log
            const eventData = {
                timestamp: new Date().toISOString(),
                event_type: event.detail.isOccupied ? 'SLOT_OCCUPIED' : 'SLOT_VACATED',
                description: `Slot ${event.detail.slotId}`,
                source: 'detection'
            };
            addEventToList(eventData);

            refreshCounters();
        });

        parkingVisualization.addEventListener('stats-updated', (event) => {
            console.log('Dashboard: Stats updated via component:', event.detail);
            // Update the external occupancy meter
            updateOccupancyMeter(event.detail);
        });
    }

    // Helper function to update the external occupancy meter
    function updateOccupancyMeter(stats) {
        if (occupancyProgress) {
            occupancyProgress.style.width = `${stats.occupancyRate}%`;
        }
        if (occupancyLabel) {
            occupancyLabel.textContent = `${stats.occupancyRate.toFixed(1)}% Occupied`;
        }
        if (freeCount) {
            freeCount.textContent = stats.free;
        }
        if (occupiedCount) {
            occupiedCount.textContent = stats.occupied;
        }
    }

    // Setup viewpoint selector (same pattern as zone editor)
    function setupViewpointSelector() {
        const viewpointSelector = document.querySelector('viewpoint-selector');
        if (!viewpointSelector) {
            console.warn('Dashboard: Viewpoint selector not found');
            return;
        }

        // Listen for viewpoint changes
        viewpointSelector.addEventListener('viewpoint-changed', (event) => {
            handleViewpointChanged(event.detail);
        });

        // Listen for viewpoint actions
        viewpointSelector.addEventListener('viewpoint-action', (event) => {
            handleViewpointAction(event.detail);
        });

        // Listen for stats updates
        viewpointSelector.addEventListener('stats-updated', (event) => {
            handleStatsUpdated(event.detail);
        });
    }

    // Add event listener for system events toggle
    const systemEventsToggle = document.getElementById('show-system-events');
    if (systemEventsToggle) {
        systemEventsToggle.addEventListener('change', () => {
            loadEvents(1); // Reload events when toggle changes
        });
    }

    // --- Add Run Detection Logic ---
    if (runDetectionBtn) {
        runDetectionBtn.addEventListener('click', () => runDetection(lotId));
    } else {
        console.warn('Run Detection button not found.');
    }

    // --- Add Viewpoint Management Logic ---
    const addViewpointBtn = document.getElementById('add-viewpoint-btn');
    const manageViewpointsBtn = document.getElementById('manage-viewpoints-btn');
    const viewpointSelector = document.querySelector('viewpoint-selector');

    if (addViewpointBtn) {
        addViewpointBtn.addEventListener('click', () => {
            showAddViewpointModal();
        });
    }

    if (manageViewpointsBtn) {
        manageViewpointsBtn.addEventListener('click', () => {
            showManageViewpointsModal();
        });
    }

    // Viewpoint selector events are handled in setupViewpointSelector()

    function runDetection(currentLotId) {
        if (!currentLotId) {
            console.error('Lot ID is missing, cannot run detection.');
            const errContainer = document.querySelector('.alert-container') || createAlertContainer();
            showDetectionError(errContainer, 'Lot ID is missing.');
            return;
        }

        // Don't allow starting a new detection if one is already in progress
        if (detectionInProgress) {
            console.warn('Detection already in progress, ignoring request');
            return;
        }

        console.log(`Running detection for lot: ${currentLotId}`);
        // Set detection in progress flag
        detectionInProgress = true;
        
        // Disable button and show loading state
        if (runDetectionBtn) {
            runDetectionBtn.disabled = true;
            runDetectionBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Running...';
        }

        // Create alert container to show the "Running detection..." message
        const alertContainer = document.querySelector('.alert-container') || createAlertContainer();
        showDetectionInProgress(alertContainer);

        // Call manual update endpoint
        fetch(`/dashboard/${currentLotId}/update`, {
            method: 'POST'
        })
        .then(response => {
            // For 500 errors, we'll let the socket handle the error message
            // since it will contain more detailed information
            if (!response.ok && response.status !== 500) {
                return response.json().then(err => {
                    throw new Error(err.error || `HTTP error! status: ${response.status}`);
                }).catch(() => {
                    throw new Error(`HTTP error! status: ${response.status}`);
                });
            }
            // Try to parse response as JSON but don't fail if it's not valid JSON
            return response.json().catch(() => ({}));
        })
        .then(data => {
            console.log('Detection start requested:', data);
            // Socket events will handle status, updates and button re-enabling
            // We don't need to do anything here, as we wait for socket events
        })
        .catch(error => {
            // Only show error from HTTP request if it's not a 500 error
            // 500 errors will be handled by the socket event with more detail
            if (!error.message.includes('status: 500')) {
                console.error('Error starting detection:', error);
                const errContainer = document.querySelector('.alert-container') || createAlertContainer();
                showDetectionError(errContainer, error.message);
                
                // Reset detection state on error
                detectionInProgress = false;
                
                // Re-enable button on failure
                if (runDetectionBtn) {
                    runDetectionBtn.disabled = false;
                    runDetectionBtn.innerHTML = originalRunDetectionText;
                }
            }
        });
    }
    // --- End Run Detection Logic ---

    // Socket event handlers
    const socket = window.socketConnection;

    // Note: slot_status_updated is now handled by the parking-visualization component
    // The component will dispatch events that we can listen to for updating the event log

    // Note: image_updated is now handled by the parking-visualization component

    socket.on('detection_status', function (data) {
        if (data.lot_id === LOT_ID) {
            console.log('Received detection status update:', data);
            const alertContainer = document.querySelector('.alert-container') || createAlertContainer();

            if (data.status === 'started') {
                showDetectionInProgress(alertContainer);
            } else if (data.status === 'completed') {
                // Reset detection in progress flag
                detectionInProgress = false;
                
                let num_updates = 0;
                if (data.payload && data.payload.updates !== undefined) {
                    if (typeof data.payload.updates === 'object' && data.payload.updates !== null) {
                        num_updates = Object.keys(data.payload.updates).length;
                    } else if (typeof data.payload.updates === 'number') {
                        num_updates = data.payload.updates;
                    }
                }

                // Show completion message
                showDetectionComplete(alertContainer, num_updates);
                
                // Re-enable button
                if (runDetectionBtn) {
                    runDetectionBtn.disabled = false;
                    runDetectionBtn.innerHTML = originalRunDetectionText;
                }
                
                // Refresh parking visualization component to show changes from the detection
                if (parkingVisualization) {
                    parkingVisualization.refresh();
                }
                
            } else if (data.status === 'error') {
                // Reset detection in progress flag
                detectionInProgress = false;
                
                // Show error message
                showDetectionError(alertContainer, data.error);
                
                // Re-enable button
                if (runDetectionBtn) {
                    runDetectionBtn.disabled = false;
                    runDetectionBtn.innerHTML = originalRunDetectionText;
                }
            }
        }
    });

    socket.on('parking_event', function (data) {
        if (data.lot_id === LOT_ID) {
            addEventToList(data);
        }
    });

    // Handle stream status events
    const streamEvents = ['stream_started', 'stream_stopped', 'stream_error', 'stream_connecting'];

    streamEvents.forEach(eventType => {
        socket.on(eventType, function(data) {
            if (data.lot_id === LOT_ID) {
                console.log(`Lot Dashboard: ${eventType} for viewpoint ${data.viewpoint_id}`);

                // Update viewpoint selector if it exists
                const viewpointSelector = document.querySelector('viewpoint-selector');
                if (viewpointSelector) {
                    viewpointSelector.refreshViewpointData();
                }

                // Add event to log
                const event = {
                    timestamp: new Date().toISOString(),
                    event_type: eventType.toUpperCase(),
                    description: `Stream ${eventType.replace('stream_', '')} for viewpoint ${data.viewpoint_id}`,
                    source: 'stream'
                };
                addEventToList(event);
            }
        });
    });

    // Note: UI update functions removed - now handled by parking-visualization component

    // Note: Resize handling is now done by the parking-visualization component

    async function initializeViewpoint() {
        try {
            // Get viewpoints to determine the current/default viewpoint
            const response = await fetch(`/api/lots/${LOT_ID}/viewpoints`);
            if (response.ok) {
                const data = await response.json();
                const viewpoints = data.viewpoints || [];

                // Find default viewpoint or use first available
                const defaultViewpoint = viewpoints.find(v => v.is_default) || viewpoints[0];
                if (defaultViewpoint) {
                    currentViewpointId = defaultViewpoint.id;
                    console.log('Dashboard initialized with viewpoint:', defaultViewpoint.name, currentViewpointId);
                }
            }
        } catch (error) {
            console.error('Error initializing viewpoint:', error);
        }

        // The parking visualization component will handle loading its own data
        console.log('Dashboard: Viewpoint initialization complete');
    }

    // Note: Slot loading and rendering is now handled by the parking-visualization component



    // Note: Slot rendering functions removed - now handled by parking-visualization component

    // Note: Slot data refresh is now handled by the parking-visualization component

    function refreshCounters() {
        // Use grouped stats API instead of counting DOM elements
        // This ensures stats are consistent regardless of viewport
        fetch(`/api/lots/${LOT_ID}/grouped-stats`)
            .then(response => response.json())
            .then(stats => {
                if (stats.error) {
                    console.error('Error fetching grouped stats:', stats.error);
                    return;
                }

                const freeSlots = stats.available_groups;
                const occupiedSlots = stats.occupied_groups;
                const occupancyRate = stats.occupancy_rate;

                freeCount.textContent = freeSlots;
                occupiedCount.textContent = occupiedSlots;

                occupancyProgress.style.width = `${occupancyRate}%`;
                occupancyLabel.textContent = `${occupancyRate.toFixed(1)}% Occupied`;

                console.log(`Dashboard stats updated: ${freeSlots} free, ${occupiedSlots} occupied (${occupancyRate.toFixed(1)}% occupancy)`);
            })
            .catch(error => {
                console.error('Error refreshing counters:', error);
                // Fallback to DOM counting if API fails
                const totalSlots = document.querySelectorAll('.parking-slot').length;
                const occupiedSlots = document.querySelectorAll('.parking-slot.occupied').length;
                const freeSlots = totalSlots - occupiedSlots;

                freeCount.textContent = freeSlots;
                occupiedCount.textContent = occupiedSlots;

                const occupancyRate = totalSlots > 0 ? (occupiedSlots / totalSlots * 100) : 0;
                occupancyProgress.style.width = `${occupancyRate}%`;
                occupancyLabel.textContent = `${occupancyRate.toFixed(1)}% Occupied`;
            });
    }

    // Event handling functions
    function addEventToList(event) {
        const timestamp = new Date(event.timestamp);
        const formattedTime = timestamp.toLocaleString() + '.' + timestamp.getMilliseconds().toString().padStart(3, '0');

        let eventText = '';
        switch (event.event_type) {
            case 'SLOT_OCCUPIED':
                eventText = `Slot ${event.description} became occupied`;
                break;
            case 'SLOT_VACATED':
                eventText = `Slot ${event.description} became vacant`;
                break;
            default:
                eventText = `${event.event_type} event for slot ${event.description}`;
        }

        const row = document.createElement('tr');
        const changeClass = getEventChangeClass(event.event_type);
        const displayEventType = getDisplayEventType(event.event_type);

        row.innerHTML = `
            <td>${timestamp.toLocaleTimeString()}.${timestamp.getMilliseconds().toString().padStart(3, '0')}</td>
            <td>${event.description || 'N/A'}</td>
            <td>
                <span class="status-change ${changeClass}">
                    ${displayEventType}
                </span>
            </td>
            <td>
                <span class="source-badge source-${event.source || 'system'}">
                    ${event.source ? event.source.charAt(0).toUpperCase() + event.source.slice(1) : 'System'}
                </span>
            </td>
        `;

        if (eventLogBody) {
            eventLogBody.insertBefore(row, eventLogBody.firstChild);
        } else {
            console.warn("[Events] Event log body (event-log-body) not found. Cannot add event to table.");
        }
    }

    // Helper functions for event display
    function getEventChangeClass(eventType) {
        if (eventType.toLowerCase().includes('occupied')) {
            return 'to-occupied';
        } else if (eventType.toLowerCase().includes('vacated') || eventType.toLowerCase().includes('free')) {
            return 'to-free';
        } else if (eventType.includes('Added') || eventType.includes('Created') || eventType.includes('Activated')) {
            return 'to-occupied'; // Green for additions/activations
        } else if (eventType.includes('Removed') || eventType.includes('Deleted') || eventType.includes('Deactivated')) {
            return 'to-free'; // Red for removals/deactivations
        } else {
            return 'to-neutral'; // Default neutral color
        }
    }

    function getDisplayEventType(eventType) {
        // Convert technical event types to user-friendly names
        const eventTypeMap = {
            'ParkingSlotOccupancyChanged': 'Occupancy Changed',
            'ParkingSlotAdded': 'Slot Added',
            'ParkingSlotRemoved': 'Slot Removed',
            'ParkingSlotZoneUpdated': 'Zone Updated',
            'ParkingSlotThresholdUpdated': 'Threshold Updated',
            'ParkingLotCreated': 'Lot Created',
            'ParkingLotDeleted': 'Lot Deleted',
            'ParkingLotRenamed': 'Lot Renamed',
            'ParkingLotImageUpdated': 'Image Updated',
            'ViewpointAdded': 'Viewpoint Added',
            'ViewpointRemoved': 'Viewpoint Removed',
            'ViewpointUpdated': 'Viewpoint Updated',
            'ViewpointStreamConfigured': 'Stream Configured',
            'ViewpointStreamActivated': 'Stream Started',
            'ViewpointStreamDeactivated': 'Stream Stopped',
            'DetectionConfigCreated': 'Detection Configured',
            'DetectionConfigUpdated': 'Detection Updated',
            'DetectionConfigDeleted': 'Detection Removed',
            'SLOT_OCCUPIED': 'Occupied',
            'SLOT_VACATED': 'Vacated'
        };

        return eventTypeMap[eventType] || eventType;
    }

    // Alert handling functions
    function createAlertContainer() {
        const alertContainer = document.createElement('div');
        alertContainer.className = 'alert-container mt-3';
        const container = document.querySelector('.container');
        container.insertBefore(alertContainer, container.querySelector('.row'));
        return alertContainer;
    }

    function showDetectionInProgress(container) {
        clearAlerts(container);
        const alert = document.createElement('div');
        alert.className = 'alert alert-info';
        alert.innerHTML = `
            <div class="d-flex align-items-center">
                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Running detection...
            </div>
        `;
        container.appendChild(alert);
    }

    function showDetectionComplete(container, updates) {
        clearAlerts(container);
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show';
        alert.innerHTML = `
            Detection completed successfully! Updated ${updates} slot(s).
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        container.appendChild(alert);
    }

    function showDetectionError(container, error) {
        clearAlerts(container);
        const alert = document.createElement('div');
        alert.className = 'alert alert-danger alert-dismissible fade show';
        alert.innerHTML = `
            ${error}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        container.appendChild(alert);
    }

    function clearAlerts(container) {
        while (container.firstChild) {
            container.removeChild(container.firstChild);
        }
    }

    // Note: Visual update functions removed - now handled by parking-visualization component

    // Note: Event listeners for slot manipulation removed - handled by component

    if (clearLogBtn) {
        clearLogBtn.addEventListener('click', () => {
            fetch(`/api/lots/${LOT_ID}/events/clear`, {
                method: 'POST'
            })
                .then(() => {
                    const eventsList = document.getElementById('events-list');
                    if (eventsList) {
                        eventsList.innerHTML = '';
                    }
                    if (eventLogBody) {
                        eventLogBody.innerHTML = '';
                    }
                })
                .catch(error => console.error('Error clearing events:', error));
        });
    }

    function loadEvents(page = 1) {
        const showSystemEvents = document.getElementById('show-system-events')?.checked || false;
        const url = `/api/lots/${LOT_ID}/events?page=${page}&per_page=${eventsPerPage}&include_system_events=${showSystemEvents}`;

        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (!data.events) {
                    console.error('Invalid events data:', data);
                    return;
                }

                if (page === 1) {
                    const eventsList = document.getElementById('events-list');
                    if (eventsList) {
                        eventsList.innerHTML = '';
                    } else {
                        console.warn('Events list element not found');
                    }

                    // Also clear the event log table
                    const eventLogBody = document.getElementById('event-log-body');
                    if (eventLogBody) {
                        eventLogBody.innerHTML = '';
                    }
                }

                data.events.forEach(event => {
                    addEventToList(event);
                });

                if (data.total > eventsPerPage) {
                    updatePagination(page, Math.ceil(data.total / data.per_page));
                }
            })
            .catch(error => console.error('Error loading events:', error));
    }

    function updatePagination(currentPage, totalPages) {
        const paginationContainer = document.getElementById('events-pagination');
        if (!paginationContainer) return;

        paginationContainer.innerHTML = '';

        if (totalPages > 1) {
            const prevButton = document.createElement('button');
            prevButton.textContent = 'Previous';
            prevButton.disabled = currentPage === 1;
            prevButton.onclick = () => loadEvents(currentPage - 1);

            const nextButton = document.createElement('button');
            nextButton.textContent = 'Next';
            nextButton.disabled = currentPage === totalPages;
            nextButton.onclick = () => loadEvents(currentPage + 1);

            paginationContainer.appendChild(prevButton);
            paginationContainer.appendChild(nextButton);
        }
    }

    // --- Viewpoint Cache Management ---

    function updateCachedViewpointImagePath(viewpointId, newImagePath) {
        console.log(`Dashboard: Updating cached image path for viewpoint ${viewpointId} to ${newImagePath}`);

        // Update viewpoint selector cache if it exists
        const viewpointSelector = document.querySelector('viewpoint-selector');
        if (viewpointSelector && viewpointSelector.viewpoints) {
            const viewpoint = viewpointSelector.viewpoints.find(v => v.id === viewpointId);
            if (viewpoint) {
                console.log(`Dashboard: Updated viewpoint selector cache for ${viewpoint.name}`);
                viewpoint.image_path = newImagePath;
            }
        }

        // Dispatch event to notify other components
        window.dispatchEvent(new CustomEvent('viewpoint-image-updated', {
            detail: {
                viewpointId: viewpointId,
                imagePath: newImagePath
            }
        }));
    }

    // --- Viewpoint Management Functions ---

    function handleViewpointAction(detail) {
        const { action, viewpointId, viewpoint } = detail;

        switch (action) {
            case 'add':
                showAddViewpointModal();
                break;
            case 'edit':
                showEditViewpointModal(viewpoint);
                break;
            case 'delete':
                showDeleteViewpointConfirm(viewpoint);
                break;
            default:
                console.warn('Unknown viewpoint action:', action);
        }
    }

    function handleViewpointChanged(detail) {
        const { viewpointId, viewpoint } = detail;
        console.log('Dashboard: handleViewpointChanged called with detail:', detail);
        console.log('Dashboard: Viewpoint changed to:', viewpoint);
        console.log('Dashboard: New viewpoint ID:', viewpointId);

        // Update current viewpoint state
        currentViewpointId = viewpointId;

        // Update the parking visualization component
        if (parkingVisualization) {
            parkingVisualization.setAttribute('viewpoint-id', viewpointId || '');
            console.log('Dashboard: Updated parking visualization viewpoint to:', viewpointId);
        }
    }

    function handleStatsUpdated(detail) {
        const { overall, viewpoints } = detail;
        console.log('Dashboard: Stats updated:', { overall, viewpoints });

        // Update main stats card with overall stats
        if (overall) {
            const freeCountEl = document.getElementById('free-count');
            const occupiedCountEl = document.getElementById('occupied-count');

            if (freeCountEl) {
                freeCountEl.textContent = overall.available;
            }
            if (occupiedCountEl) {
                occupiedCountEl.textContent = overall.occupied;
            }

            console.log(`Dashboard: Updated main stats - Available: ${overall.available}, Occupied: ${overall.occupied}`);
        }
    }

    function showAddViewpointModal() {
        // Create a simple modal for adding viewpoints
        const modal = createViewpointModal('Add New Viewpoint', {
            name: '',
            image_path: '',
            camera_position: '',
            is_active: true,
            sort_order: 0
        }, (formData, isFormData, sourceType) => {
            createViewpoint(formData, isFormData, sourceType);
        });

        document.body.appendChild(modal);
        modal.style.display = 'block';
    }

    function showEditViewpointModal(viewpoint) {
        const modal = createViewpointModal('Edit Viewpoint', viewpoint, (formData, isFormData, sourceType) => {
            updateViewpoint(viewpoint.id, formData, isFormData, sourceType);
        });

        document.body.appendChild(modal);
        modal.style.display = 'block';
    }

    function showManageViewpointsModal() {
        createManageViewpointsModal();
    }

    async function createManageViewpointsModal() {
        // First, fetch current viewpoints
        let viewpoints = [];
        try {
            const response = await fetch(`/api/lots/${LOT_ID}/viewpoints`);
            if (response.ok) {
                const data = await response.json();
                viewpoints = data.viewpoints || [];
            }
        } catch (error) {
            console.error('Error fetching viewpoints:', error);
        }

        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.cssText = `
            display: block;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        `;

        const viewpointsListHtml = viewpoints.length > 0 ? viewpoints.map(viewpoint => `
            <div class="viewpoint-item" style="
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                margin-bottom: 10px;
                background: ${viewpoint.is_default ? '#e7f3ff' : '#fff'};
            ">
                <div class="viewpoint-info">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <span style="font-size: 18px;">${viewpoint.is_default ? '⭐' : '📹'}</span>
                        <div>
                            <h6 style="margin: 0; font-weight: 600;">
                                ${viewpoint.name}
                                ${viewpoint.is_default ? '<span style="background: #0d6efd; color: white; font-size: 10px; padding: 2px 6px; border-radius: 10px; margin-left: 8px;">DEFAULT</span>' : ''}
                            </h6>
                            <small style="color: #6c757d;">
                                ${viewpoint.zone_count || 0} zones •
                                ${viewpoint.is_active ? 'Active' : 'Inactive'}
                            </small>
                        </div>
                    </div>
                </div>
                <div class="viewpoint-actions" style="display: flex; gap: 8px;">
                    <button class="btn btn-sm btn-outline-primary edit-viewpoint-btn" data-viewpoint-id="${viewpoint.id}">
                        <i class="bi bi-pencil"></i> Edit
                    </button>
                    ${!viewpoint.is_default ? `
                        <button class="btn btn-sm btn-outline-success set-default-btn" data-viewpoint-id="${viewpoint.id}">
                            <i class="bi bi-star"></i> Set Default
                        </button>
                        <button class="btn btn-sm btn-outline-danger delete-viewpoint-btn" data-viewpoint-id="${viewpoint.id}">
                            <i class="bi bi-trash"></i> Delete
                        </button>
                    ` : ''}
                </div>
            </div>
        `).join('') : '<div style="text-align: center; padding: 40px; color: #6c757d;">No viewpoints found</div>';

        modal.innerHTML = `
            <div class="modal-content" style="
                background-color: #fefefe;
                margin: 5% auto;
                padding: 0;
                border: 1px solid #888;
                width: 90%;
                max-width: 800px;
                border-radius: 8px;
                max-height: 80vh;
                overflow-y: auto;
            ">
                <div class="modal-header" style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 20px;
                    border-bottom: 1px solid #dee2e6;
                    background: #f8f9fa;
                    border-radius: 8px 8px 0 0;
                ">
                    <h4 style="margin: 0;">Manage Viewpoints</h4>
                    <span class="close" style="font-size: 28px; font-weight: bold; cursor: pointer;">&times;</span>
                </div>
                <div class="modal-body" style="padding: 20px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h6 style="margin: 0;">Viewpoints (${viewpoints.length})</h6>
                        <button class="btn btn-primary add-new-viewpoint-btn">
                            <i class="bi bi-plus"></i> Add New Viewpoint
                        </button>
                    </div>
                    <div class="viewpoints-list">
                        ${viewpointsListHtml}
                    </div>
                </div>
            </div>
        `;

        // Add event listeners
        const closeBtn = modal.querySelector('.close');
        const addNewBtn = modal.querySelector('.add-new-viewpoint-btn');

        const closeModal = () => {
            modal.remove();
        };

        closeBtn.addEventListener('click', closeModal);
        addNewBtn.addEventListener('click', () => {
            closeModal();
            showAddViewpointModal();
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) closeModal();
        });

        // Edit viewpoint buttons
        modal.querySelectorAll('.edit-viewpoint-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const viewpointId = btn.dataset.viewpointId;
                const viewpoint = viewpoints.find(v => v.id === viewpointId);
                closeModal();
                showEditViewpointModal(viewpoint);
            });
        });

        // Set default buttons
        modal.querySelectorAll('.set-default-btn').forEach(btn => {
            btn.addEventListener('click', async () => {
                const viewpointId = btn.dataset.viewpointId;
                if (confirm('Set this viewpoint as the default?')) {
                    try {
                        const response = await fetch(`/api/lots/${LOT_ID}/viewpoints/default`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ viewpoint_id: viewpointId })
                        });

                        if (response.ok) {
                            alert('Default viewpoint updated successfully!');
                            closeModal();
                            // Refresh viewpoint selector
                            refreshViewpointSelector();
                        } else {
                            alert('Failed to set default viewpoint');
                        }
                    } catch (error) {
                        console.error('Error setting default viewpoint:', error);
                        alert('Failed to set default viewpoint');
                    }
                }
            });
        });

        // Delete buttons
        modal.querySelectorAll('.delete-viewpoint-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const viewpointId = btn.dataset.viewpointId;
                const viewpoint = viewpoints.find(v => v.id === viewpointId);
                closeModal();
                showDeleteViewpointConfirm(viewpoint);
            });
        });

        document.body.appendChild(modal);
    }

    function showDeleteViewpointConfirm(viewpoint) {
        if (confirm(`Are you sure you want to delete the viewpoint "${viewpoint.name}"?`)) {
            deleteViewpoint(viewpoint.id);
        }
    }

    function refreshViewpointSelector() {
        console.log('Refreshing viewpoint selector...');

        // Try multiple ways to find and refresh the viewpoint selector
        const selectors = [
            document.querySelector('viewpoint-selector'),
            document.getElementById('viewpoint-selector'),
            document.querySelector('[is="viewpoint-selector"]')
        ];

        for (const selector of selectors) {
            if (selector && typeof selector.refresh === 'function') {
                console.log('Found viewpoint selector, refreshing...');
                selector.refresh();
                return;
            }
        }

        // If we can't find the selector, try to trigger a page refresh of viewpoint data
        console.warn('Viewpoint selector not found, trying alternative refresh...');

        // Dispatch a custom event that the viewpoint selector can listen for
        window.dispatchEvent(new CustomEvent('viewpoints-changed', {
            detail: { lotId: LOT_ID }
        }));

        // Also try to refresh after a short delay in case the component needs time to initialize
        setTimeout(() => {
            const delayedSelector = document.querySelector('viewpoint-selector');
            if (delayedSelector && typeof delayedSelector.refresh === 'function') {
                console.log('Found viewpoint selector after delay, refreshing...');
                delayedSelector.refresh();
            }
        }, 100);
    }

    function createViewpointModal(title, initialData, onSubmit) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.cssText = `
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        `;

        modal.innerHTML = `
            <div class="modal-content" style="
                background-color: #fefefe;
                margin: 15% auto;
                padding: 20px;
                border: 1px solid #888;
                width: 80%;
                max-width: 500px;
                border-radius: 8px;
            ">
                <div class="modal-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3>${title}</h3>
                    <span class="close" style="font-size: 28px; font-weight: bold; cursor: pointer;">&times;</span>
                </div>
                <form id="viewpoint-form">
                    <div class="form-group" style="margin-bottom: 15px;">
                        <label for="viewpoint-name">Name:</label>
                        <input type="text" id="viewpoint-name" name="name" value="${initialData.name || ''}"
                               class="form-control" required style="width: 100%; padding: 8px; margin-top: 5px;">
                    </div>

                    <!-- Source Type Selection -->
                    <div class="form-group" style="margin-bottom: 15px;">
                        <label style="margin-bottom: 8px; font-weight: 500;">Image Source:</label>
                        <div style="display: flex; gap: 15px; margin-bottom: 10px;">
                            <label style="display: flex; align-items: center; cursor: pointer;">
                                <input type="radio" name="source_type" value="image" checked style="margin-right: 5px;">
                                📷 Upload Image File
                            </label>
                            <label style="display: flex; align-items: center; cursor: pointer;">
                                <input type="radio" name="source_type" value="stream" style="margin-right: 5px;">
                                📡 Video Stream URL
                            </label>
                        </div>
                    </div>

                    <!-- Image Upload Section -->
                    <div class="form-group" id="image-upload-section" style="margin-bottom: 15px;">
                        <label for="viewpoint-image">Upload Image:</label>
                        <input type="file" id="viewpoint-image" name="image_file" accept="image/*"
                               class="form-control" ${!initialData.image_path ? 'required' : ''} style="width: 100%; padding: 8px; margin-top: 5px;">
                        ${initialData.image_path ? `<small style="color: #666; margin-top: 5px; display: block;">Current: ${initialData.image_path}</small>` : ''}
                        <div style="font-size: 12px; color: #666; margin-top: 5px;">Supported formats: JPG, PNG</div>
                    </div>

                    <!-- Stream URL Section -->
                    <div class="form-group" id="stream-url-section" style="margin-bottom: 15px; display: none;">
                        <label for="viewpoint-stream-url">Video Stream URL:</label>
                        <input type="url" id="viewpoint-stream-url" name="stream_url"
                               class="form-control" style="width: 100%; padding: 8px; margin-top: 5px;"
                               placeholder="rtsp://camera.example.com/stream or http://camera.example.com/mjpeg">
                        <div style="font-size: 12px; color: #666; margin-top: 5px;">Supported protocols: RTSP, HTTP/MJPEG, HTTPS</div>

                        <div style="margin-top: 10px;">
                            <label for="viewpoint-capture-fps">Capture Rate (FPS):</label>
                            <input type="number" id="viewpoint-capture-fps" name="capture_fps"
                                   value="1.0" min="0.1" max="30" step="0.1"
                                   class="form-control" style="width: 100%; padding: 8px; margin-top: 5px;">
                            <div style="font-size: 12px; color: #666; margin-top: 5px;">Frames per second to capture from stream</div>
                        </div>
                    </div>
                    <div class="form-group" style="margin-bottom: 15px;">
                        <label for="viewpoint-camera">Camera Position (JSON):</label>
                        <textarea id="viewpoint-camera" name="camera_position" class="form-control"
                                  style="width: 100%; padding: 8px; margin-top: 5px; height: 80px;">${JSON.stringify(initialData.camera_position || {}, null, 2)}</textarea>
                    </div>
                    <div class="form-group" style="margin-bottom: 15px;">
                        <label>
                            <input type="checkbox" name="is_active" ${initialData.is_active ? 'checked' : ''}>
                            Active
                        </label>
                    </div>
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label for="viewpoint-sort">Sort Order:</label>
                        <input type="number" id="viewpoint-sort" name="sort_order" value="${initialData.sort_order || 0}"
                               class="form-control" style="width: 100%; padding: 8px; margin-top: 5px;">
                    </div>
                    <div class="form-actions" style="text-align: right;">
                        <button type="button" class="btn btn-secondary cancel-btn" style="margin-right: 10px;">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save</button>
                    </div>
                </form>
            </div>
        `;

        // Add event listeners
        const closeBtn = modal.querySelector('.close');
        const cancelBtn = modal.querySelector('.cancel-btn');
        const form = modal.querySelector('#viewpoint-form');

        const closeModal = () => {
            modal.remove();
        };

        closeBtn.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);

        modal.addEventListener('click', (e) => {
            if (e.target === modal) closeModal();
        });

        // Handle source type switching
        const sourceImageRadio = form.querySelector('input[name="source_type"][value="image"]');
        const sourceStreamRadio = form.querySelector('input[name="source_type"][value="stream"]');
        const imageUploadSection = form.querySelector('#image-upload-section');
        const streamUrlSection = form.querySelector('#stream-url-section');
        const imageFileInput = form.querySelector('#viewpoint-image');
        const streamUrlInput = form.querySelector('#viewpoint-stream-url');

        function toggleSourceType() {
            if (sourceStreamRadio.checked) {
                imageUploadSection.style.display = 'none';
                streamUrlSection.style.display = 'block';
                imageFileInput.removeAttribute('required');
                streamUrlInput.setAttribute('required', 'required');
            } else {
                imageUploadSection.style.display = 'block';
                streamUrlSection.style.display = 'none';
                streamUrlInput.removeAttribute('required');
                if (!initialData.image_path) {
                    imageFileInput.setAttribute('required', 'required');
                }
            }
        }

        // Add event listeners for source type switching
        sourceImageRadio.addEventListener('change', toggleSourceType);
        sourceStreamRadio.addEventListener('change', toggleSourceType);

        // Initialize the form state based on existing data
        const isExistingStream = initialData.stream_url && initialData.stream_url.trim() !== '';
        if (isExistingStream) {
            // Set stream radio button and populate stream fields
            sourceStreamRadio.checked = true;
            sourceImageRadio.checked = false;
            streamUrlInput.value = initialData.stream_url || '';
            form.querySelector('#viewpoint-capture-fps').value = initialData.capture_fps || 1.0;
        }

        toggleSourceType();

        form.addEventListener('submit', (e) => {
            e.preventDefault();
            const formData = new FormData(form);
            const sourceType = formData.get('source_type');

            // Validate based on source type
            if (sourceType === 'stream') {
                const streamUrl = formData.get('stream_url');
                if (!streamUrl || !streamUrl.trim()) {
                    alert('Please enter a valid stream URL');
                    return;
                }
            } else {
                const imageFile = formData.get('image_file');
                const hasImageFile = imageFile && imageFile.size > 0;
                if (!hasImageFile && !initialData.image_path) {
                    alert('Please select an image file');
                    return;
                }
            }

            // Parse camera position JSON
            try {
                const cameraPositionText = formData.get('camera_position');
                if (cameraPositionText) {
                    const cameraPosition = JSON.parse(cameraPositionText);
                    formData.set('camera_position', JSON.stringify(cameraPosition));
                }
            } catch (error) {
                alert('Invalid JSON in camera position field');
                return;
            }

            // Convert checkbox to proper boolean string
            formData.set('is_active', formData.has('is_active') ? 'true' : 'false');

            // Handle stream creation vs image upload
            if (sourceType === 'stream') {
                // For stream viewpoints, we always use FormData to include all fields
                onSubmit(formData, true, 'stream');
            } else {
                // Check if we have an image file
                const imageFile = formData.get('image_file');
                const hasImageFile = imageFile && imageFile.size > 0;

                if (hasImageFile) {
                    // Image upload - use FormData
                    onSubmit(formData, true, 'image');
                } else {
                    // No image file - use JSON for updates without image changes
                    const data = Object.fromEntries(formData.entries());

                    // Parse camera position JSON
                    try {
                        if (data.camera_position) {
                            data.camera_position = JSON.parse(data.camera_position);
                        }
                    } catch (error) {
                        alert('Invalid JSON in camera position field');
                        return;
                    }

                    data.is_active = formData.has('is_active');
                    data.sort_order = parseInt(data.sort_order) || 0;

                    // Remove fields not needed for JSON submission
                    delete data.image_file;
                    delete data.source_type;
                    delete data.stream_url;
                    delete data.capture_fps;

                    onSubmit(data, false, 'image');
                }
            }
            closeModal();
        });

        return modal;
    }

    async function createViewpoint(data, isFormData = false, sourceType = 'image') {
        try {
            let requestOptions;
            let endpoint = `/api/lots/${LOT_ID}/viewpoints`;

            // Handle stream viewpoints differently
            if (sourceType === 'stream' && isFormData) {
                // For stream viewpoints, use the stream creation endpoint
                endpoint = `/api/lots/${LOT_ID}/viewpoints/stream`;
                requestOptions = {
                    method: 'POST',
                    body: data // FormData object with stream configuration
                };
            } else if (isFormData) {
                // Use multipart/form-data for image file uploads
                requestOptions = {
                    method: 'POST',
                    body: data // FormData object
                };
            } else {
                // Use JSON for updates without file uploads
                requestOptions = {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                };
            }

            const response = await fetch(endpoint, requestOptions);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const result = await response.json();
            console.log('Viewpoint created:', result);

            // Refresh the viewpoint selector
            refreshViewpointSelector();

            alert('Viewpoint created successfully!');
        } catch (error) {
            console.error('Error creating viewpoint:', error);
            alert('Failed to create viewpoint. Please try again.');
        }
    }

    async function updateViewpoint(viewpointId, data, isFormData = false) {
        try {
            let requestOptions;

            if (isFormData) {
                // Use multipart/form-data for file uploads
                requestOptions = {
                    method: 'PUT',
                    body: data // FormData object
                };
            } else {
                // Use JSON for updates without file uploads
                requestOptions = {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                };
            }

            const response = await fetch(`/api/lots/${LOT_ID}/viewpoints/${viewpointId}`, requestOptions);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const result = await response.json();
            console.log('Viewpoint updated:', result);

            // Refresh the viewpoint selector
            refreshViewpointSelector();

            alert('Viewpoint updated successfully!');
        } catch (error) {
            console.error('Error updating viewpoint:', error);
            alert('Failed to update viewpoint. Please try again.');
        }
    }

    async function deleteViewpoint(viewpointId) {
        try {
            const response = await fetch(`/api/lots/${LOT_ID}/viewpoints/${viewpointId}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const result = await response.json();
            console.log('Viewpoint deleted:', result);

            // Refresh the viewpoint selector
            refreshViewpointSelector();

            alert('Viewpoint deleted successfully!');
        } catch (error) {
            console.error('Error deleting viewpoint:', error);
            alert('Failed to delete viewpoint. Please try again.');
        }
    }
});