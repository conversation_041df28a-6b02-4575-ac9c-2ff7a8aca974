/* Base container styles */
.zoom-wrapper {
    position: relative;
    overflow: hidden;
    width: 100%;
    height: auto;
    min-height: 200px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f8f9fa;
}

/* Image container positioning */
#image-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* Default aspect ratio 16:9, will be dynamically adjusted */
    overflow: hidden;
}

#parking-image {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: contain;
    max-height: 80vh;
    display: block;
    margin: 0;
    padding: 0;
    z-index: 1;
}

/* Zone polygon and parking slot styles */
.zone-polygon,
.parking-slot {
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    width: 100% !important;
    height: 100% !important;
    pointer-events: all;
    margin: 0 !important;
    padding: 0 !important;
    z-index: 2;
    opacity: 0.7;
}

.zone-polygon {
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 123, 255, 0.2);
    border: 2px solid rgba(0, 123, 255, 0.5);
    pointer-events: all;
    cursor: pointer;
    transition: all 0.2s ease;
    margin: 0 !important;
    padding: 0 !important;
    z-index: 2;
}

.zone-polygon:hover {
    background-color: rgba(0, 123, 255, 0.3);
    border-color: rgba(0, 123, 255, 0.7);
}

/* Active zones during editing */
.zone-polygon.active {
    background-color: rgba(0, 123, 255, 0.4);
    border-color: #007bff;
    z-index: 3;
}

/* Ensure temporary polygon visibility during drawing */
#image-container.drawing-active .zone-polygon:not(.active) {
    z-index: 2;
}

/* Drawing mode cursor */
.drawing-active {
    cursor: crosshair !important;
}

/* Vertex and point styles */
.vertex-point {
    position: absolute;
    width: 12px;
    height: 12px;
    background-color: #fff;
    border: 2px solid #007bff;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    cursor: move;
    z-index: 15;
    box-shadow: 0 0 3px rgba(0,0,0,0.5);
    pointer-events: all;
}

.vertex-point:hover {
    background-color: #007bff;
    border-color: #0056b3;
}

.drawing-point {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: #ff0000;
    border-radius: 50%;
    border: 1px solid white;
    transform: translate(-50%, -50%) !important;
    transform-origin: center !important;
    z-index: 5;
    pointer-events: all;
    box-shadow: 0 0 3px rgba(0,0,0,0.5);
}

/* Overlay line styles */
.overlay-line {
    position: absolute;
    border-top: 2px dashed #ff0000;
    z-index: 4;
    pointer-events: none;
    transform-origin: 0 0;
}

/* Labels */
.zone-label,
.label {
    position: absolute;
    background: rgba(255, 255, 255, 0.9);
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: bold;
    z-index: 10;
    transform: translate(-50%, -50%);
    pointer-events: none;
    font-size: 14px;
    color: #007bff;
    white-space: nowrap;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    min-width: 20px;
    height: auto;
    line-height: normal;
    border: 1px solid rgba(0, 0, 0, 0.2);
}

/* Make temporary polygon visible */
[data-vertex-type="temp-polygon"] {
    pointer-events: none;
    z-index: 5;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 100%;
    height: 100%;
}

/* Selected zones in the list */
#zones-list li.active {
    background-color: #e7f1ff;
}

/* Force consistent transform behavior */
.zone-polygon, 
.zone-label, 
.vertex-point, 
.drawing-point, 
.overlay-line,
.parking-slot,
.label {
    transform-origin: 0 0 !important;
    backface-visibility: hidden;
    will-change: transform;
    box-sizing: border-box;
}

/* Animations */
@keyframes slot-status-change {
    0% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.3); opacity: 0.9; }
    100% { transform: scale(1); opacity: 0.7; }
}

.parking-slot.status-changed {
    animation: slot-status-change 1000ms cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
    z-index: 5;
    pointer-events: none;
    box-shadow: 0 0 25px currentColor;
}

/* Slot states */
.parking-slot.free {
    border-color: #198754;
    background-color: rgba(25, 135, 84, 0.2);
}

.parking-slot.occupied {
    border-color: #dc3545;
    background-color: rgba(220, 53, 69, 0.2);
}

#zones-list .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

#zones-list .btn i {
    font-size: 1rem;
}

/* Card body styling */
.card-body {
    padding: 1.25rem;
}

/* Range input styling */
.form-range {
    -webkit-appearance: none;
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e9ecef;
    outline: none;
    margin: 1rem 0;
}

.form-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #0d6efd;
    cursor: pointer;
    border: none;
    margin-top: -6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.form-range::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #0d6efd;
    cursor: pointer;
    border: none;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

/* Colored track */
.form-range::-webkit-slider-runnable-track {
    height: 6px;
    border-radius: 3px;
    background: linear-gradient(to right, #0d6efd 0%, #0d6efd var(--range-progress), #e9ecef var(--range-progress), #e9ecef 100%);
}

.form-range::-moz-range-track {
    height: 6px;
    border-radius: 3px;
    background: #e9ecef;
}

.form-range::-moz-range-progress {
    height: 6px;
    border-radius: 3px;
    background: #0d6efd;
}

/* Value display styling */
.slider-value-display {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
    color: #495057;
    margin: 0 0.5rem;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Vertex popup menu */
.vertex-popup {
    position: absolute;
    background: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    padding: 4px 0;
    z-index: 1000;
    min-width: 100px;
    transform: scale(1) !important;
    pointer-events: all;
}

.vertex-popup-item {
    padding: 4px 8px;
    cursor: pointer;
    font-size: 14px;
    color: #333;
}

.vertex-popup-item:hover {
    background-color: #f0f0f0;
}

.vertex-popup-item.delete {
    color: #dc3545;
}

/* Prevent text selection during vertex interactions */
.vertex-point, .vertex-popup {
    user-select: none;
}

/* Disabled zone styles for zones not in current viewpoint */
.disabled-zone {
    opacity: 0.6;
    background-color: #f8f9fa !important;
    color: #6c757d !important;
}

.disabled-zone span {
    color: #6c757d !important;
}

.disabled-zone .btn {
    opacity: 0.5;
    cursor: not-allowed;
}

.disabled-zone .btn:disabled {
    pointer-events: none;
}

.disabled-zone small.text-muted {
    font-style: italic;
    font-size: 0.75em;
}

/* Viewpoint badges styling */
.badge {
    font-size: 0.7em;
}

.badge i {
    font-size: 0.9em;
}

/* Ensure badges wrap nicely */
.list-group-item .badge {
    white-space: nowrap;
}

/* Stream status indicators for viewpoint dropdown */
.stream-status-dot {
    font-size: 10px;
    margin-left: 2px;
}

.stream-status-dot.status-active {
    color: #28a745; /* Green */
}

.stream-status-dot.status-connecting {
    color: #ffc107; /* Yellow */
}

.stream-status-dot.status-error {
    color: #dc3545; /* Red */
}

.stream-status-dot.status-inactive {
    color: #6c757d; /* Gray */
}
