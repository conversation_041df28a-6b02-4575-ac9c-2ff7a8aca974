"""
Stream capture service for managing video stream processing and frame capture.
"""
import urllib

import cv2
import time
import threading
import logging
import numpy as np
import requests
from typing import Dict, Optional, Callable
from datetime import datetime

from src.utils.logger import setup_logger
from src.utils.storage import StorageService
from src.services.image_handler import ImageHandler

LOGGER = setup_logger(__name__)


class StreamCaptureService:
    """Manages video stream capture and frame processing for parking lots."""
    
    # Active stream capture threads
    _capture_threads: Dict[str, threading.Thread] = {}
    _stop_events: Dict[str, threading.Event] = {}
    
    @classmethod
    def start_stream_capture(cls, lot_id: str, viewpoint_id: str) -> bool:
        """
        Start stream capture for a specific viewpoint.
        
        Args:
            lot_id: ID of the parking lot
            viewpoint_id: ID of the viewpoint with stream configuration
            
        Returns:
            bool: True if capture started successfully, False otherwise
        """
        # Get the parking lot and viewpoint
        lot = StorageService.get_parking_lot(lot_id)
        if not lot:
            LOGGER.error(f"Parking lot {lot_id} not found")
            return False
            
        viewpoint = lot.get_viewpoint(viewpoint_id)
        if not viewpoint:
            LOGGER.error(f"Viewpoint {viewpoint_id} not found in lot {lot_id}")
            return False
            
        if not viewpoint.stream_url:
            LOGGER.error(f"No stream URL configured for viewpoint {viewpoint_id}")
            return False
        
        # Stop any existing capture for this viewpoint
        capture_key = f"{lot_id}_{viewpoint_id}"
        cls.stop_stream_capture(lot_id, viewpoint_id)
        
        # Create stop event
        stop_event = threading.Event()
        cls._stop_events[capture_key] = stop_event
        
        # Create and start capture thread
        thread = threading.Thread(
            target=cls._capture_loop,
            args=(lot_id, viewpoint_id, viewpoint.stream_url, viewpoint.capture_fps, stop_event),
            daemon=True
        )
        cls._capture_threads[capture_key] = thread
        LOGGER.info(f"Starting stream capture for viewpoint {viewpoint_id} in lot {lot_id}")

        # Emit connecting event (no persistence needed for stream status)
        cls._emit_stream_event('stream_connecting', lot_id, viewpoint_id)

        thread.start()

        return True
    
    @classmethod
    def stop_stream_capture(cls, lot_id: str, viewpoint_id: str) -> bool:
        """
        Stop stream capture for a specific viewpoint.
        
        Args:
            lot_id: ID of the parking lot
            viewpoint_id: ID of the viewpoint
            
        Returns:
            bool: True if capture stopped successfully, False otherwise
        """
        capture_key = f"{lot_id}_{viewpoint_id}"
        
        if capture_key in cls._stop_events:
            LOGGER.info(f"Stopping stream capture for viewpoint {viewpoint_id} in lot {lot_id}")
            cls._stop_events[capture_key].set()

            if capture_key in cls._capture_threads and cls._capture_threads[capture_key].is_alive():
                cls._capture_threads[capture_key].join(timeout=5.0)
                del cls._capture_threads[capture_key]

            del cls._stop_events[capture_key]

            # Emit stopped event (no persistence needed for stream status)
            cls._emit_stream_event('stream_stopped', lot_id, viewpoint_id)

            return True
        
        LOGGER.debug(f"No active stream capture to stop for viewpoint {viewpoint_id} in lot {lot_id}")
        return False
    
    @classmethod
    def stop_all_captures(cls):
        """Stop all active stream captures."""
        for capture_key in list(cls._stop_events.keys()):
            lot_id, viewpoint_id = capture_key.split('_', 1)
            cls.stop_stream_capture(lot_id, viewpoint_id)

    @classmethod
    def start_all_configured_streams(cls):
        """
        Start stream capture for all viewpoints that have active stream configurations.
        This should be called during application startup to resume all streams.
        """
        LOGGER.info("Starting all configured streams on application startup...")

        try:
            from src.utils.storage import StorageService

            # Get all parking lots
            lots = StorageService.get_all_parking_lots()
            started_count = 0
            failed_count = 0

            for lot in lots:
                for viewpoint in lot.viewpoints:
                    # Check if viewpoint has stream configuration and is active
                    if (viewpoint.stream_url and
                        viewpoint.is_stream_active and
                        viewpoint.is_active):

                        LOGGER.info(f"Auto-starting stream for viewpoint {viewpoint.name} "
                                  f"in lot {lot.name} (URL: {viewpoint.stream_url})")

                        try:
                            success = cls.start_stream_capture(lot.id, viewpoint.id)
                            if success:
                                started_count += 1
                                LOGGER.info(f"✓ Successfully started stream for viewpoint {viewpoint.id}")
                                # Emit WebSocket event for stream started
                                cls._emit_stream_event('stream_started', lot.id, viewpoint.id)
                            else:
                                failed_count += 1
                                LOGGER.warning(f"✗ Failed to start stream for viewpoint {viewpoint.id}")
                                # Emit WebSocket event for stream error
                                cls._emit_stream_event('stream_error', lot.id, viewpoint.id)
                        except Exception as e:
                            failed_count += 1
                            LOGGER.error(f"✗ Error starting stream for viewpoint {viewpoint.id}: {str(e)}")
                            # Emit WebSocket event for stream error
                            cls._emit_stream_event('stream_error', lot.id, viewpoint.id)

            LOGGER.info(f"Stream startup complete: {started_count} started, {failed_count} failed")
            return started_count, failed_count

        except Exception as e:
            LOGGER.error(f"Error during stream startup: {str(e)}")
            return 0, 0

    @classmethod
    def get_active_streams_status(cls) -> dict:
        """
        Get status of all active stream captures.

        Returns:
            dict: Status information for all active streams
        """
        status = {
            'active_streams': [],
            'total_active': 0
        }

        try:
            from src.utils.storage import StorageService

            for capture_key, thread in cls._capture_threads.items():
                if thread.is_alive():
                    lot_id, viewpoint_id = capture_key.split('_', 1)

                    # Get lot and viewpoint info
                    lot = StorageService.get_parking_lot(lot_id)
                    if lot:
                        viewpoint = lot.get_viewpoint(viewpoint_id)
                        if viewpoint:
                            status['active_streams'].append({
                                'lot_id': lot_id,
                                'lot_name': lot.name,
                                'viewpoint_id': viewpoint_id,
                                'viewpoint_name': viewpoint.name,
                                'stream_url': viewpoint.stream_url,
                                'capture_fps': viewpoint.capture_fps,
                                'thread_alive': thread.is_alive()
                            })

            status['total_active'] = len(status['active_streams'])

        except Exception as e:
            LOGGER.error(f"Error getting stream status: {str(e)}")

        return status

    @classmethod
    def _emit_stream_event(cls, event_type: str, lot_id: str, viewpoint_id: str, extra_data: dict = None):
        """
        Emit WebSocket event for stream status changes.

        Args:
            event_type: Type of event (stream_started, stream_stopped, stream_error, stream_connecting)
            lot_id: ID of the parking lot
            viewpoint_id: ID of the viewpoint
            extra_data: Additional data to include in the event
        """
        try:
            # Import here to avoid circular imports
            from src.config.settings import get_settings
            settings = get_settings()
            socketio = getattr(settings, 'socketio', None)

            if socketio:
                event_data = {
                    'lot_id': lot_id,
                    'viewpoint_id': viewpoint_id,
                    'timestamp': time.time()
                }

                if extra_data:
                    event_data.update(extra_data)

                # Emit to dashboard namespace
                import asyncio
                try:
                    # Try to emit the event
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # If we're in an async context, schedule the emission
                        asyncio.create_task(socketio.emit(event_type, event_data, namespace='/dashboard'))
                    else:
                        # If not in async context, run it
                        asyncio.run(socketio.emit(event_type, event_data, namespace='/dashboard'))
                except Exception as emit_error:
                    LOGGER.debug(f"Could not emit WebSocket event {event_type}: {emit_error}")
                    # Don't fail the stream operation if WebSocket emission fails

            else:
                LOGGER.debug(f"No SocketIO instance available for event {event_type}")

        except Exception as e:
            LOGGER.debug(f"Error emitting stream event {event_type}: {str(e)}")
            # Don't fail the stream operation if event emission fails


    @classmethod
    def is_capture_active(cls, lot_id: str, viewpoint_id: str) -> bool:
        """Check if stream capture is active for a viewpoint."""
        capture_key = f"{lot_id}_{viewpoint_id}"
        return capture_key in cls._capture_threads and cls._capture_threads[capture_key].is_alive()
    
    @classmethod
    def _setup_stream(cls, rtsp_url: str, attempts: int = 3) -> Optional[cv2.VideoCapture]:
        """
        Set up video capture with optimized settings.
        
        Args:
            rtsp_url: Stream URL
            attempts: Number of connection attempts
            
        Returns:
            VideoCapture object or None if failed
        """
        import os
        # Configure FFmpeg options for better stream handling
        # os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = (
        #     'protocol_whitelist;file,rtp,udp,tcp,rtsp,http,https|'
        #     'rtsp_transport;tcp|'
        #     'max_delay;500000|'
        #     'fflags;nobuffer|'
        #     'flags;low_delay'
        # )

        # URL should be encoded to handle special characters
        # rtsp_url = urllib.parse.quote(rtsp_url, safe=':/@', encoding='utf-8', errors='strict')
        
        for i in range(attempts):
            try:
                cap = cv2.VideoCapture(rtsp_url)
                if not cap.isOpened():
                    if i < attempts - 1:
                        LOGGER.warning(f"Stream connection attempt {i+1}/{attempts} failed, retrying...")
                        time.sleep(2)
                        continue
                    raise ValueError(f"Failed to open stream after {attempts} attempts")
                
                # Configure stream parameters
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Minimize buffer size
                cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('H', '2', '6', '4'))
                
                return cap
                
            except Exception as e:
                if i == attempts - 1:
                    raise ValueError(f"Failed to initialize stream: {str(e)}")
                LOGGER.error(f"Error during setup attempt {i+1}: {str(e)}")
                time.sleep(2)
        
        return None
    
    @classmethod
    def _validate_frame(cls, frame: np.ndarray) -> bool:
        """
        Validate frame quality and check for corruption.
        
        Args:
            frame: Frame to validate
            
        Returns:
            bool: True if frame is valid, False otherwise
        """
        if frame is None:
            return False
            
        # Check if frame is empty or has invalid dimensions
        if frame.size == 0 or frame.shape[0] == 0 or frame.shape[1] == 0:
            return False
            
        # Check for completely black or white frames
        if np.mean(frame) < 1 or np.mean(frame) > 254:
            return False
            
        # Check for severe corruption (high contrast areas)
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            laplacian = cv2.Laplacian(gray, cv2.CV_64F)
            if np.var(laplacian) < 1:  # Too smooth, might be corrupted
                return False
        except Exception:
            return False
            
        return True
    
    @classmethod
    def _capture_loop(cls, lot_id: str, viewpoint_id: str, stream_url: str, 
                     target_fps: float, stop_event: threading.Event):
        """
        Main capture loop for processing video stream frames.
        
        Args:
            lot_id: ID of the parking lot
            viewpoint_id: ID of the viewpoint
            stream_url: Stream URL
            target_fps: Target frame capture rate
            stop_event: Event to signal stop
        """
        LOGGER.info(f"Starting stream capture loop for {stream_url}")
        
        cap = None
        stream_ended_with_error = False
        try:
            cap = cls._setup_stream(stream_url)
            if not cap:
                LOGGER.error(f"Failed to setup stream for {stream_url}")
                cls._emit_stream_event('stream_error', lot_id, viewpoint_id, {'error': 'Failed to setup stream'})
                stream_ended_with_error = True
                return
            
            # Get stream properties
            actual_fps = cap.get(cv2.CAP_PROP_FPS)
            frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            LOGGER.info(f"Stream properties - FPS: {actual_fps}, Resolution: {frame_width}x{frame_height}")
            
            # Calculate frame interval
            upload_fps = target_fps if actual_fps <= 0 or actual_fps > 30 else min(target_fps, actual_fps)
            frame_interval = 1.0 / upload_fps

            # Emit stream started event once we have a successful connection
            cls._emit_stream_event('stream_started', lot_id, viewpoint_id, {
                'fps': actual_fps,
                'resolution': f"{frame_width}x{frame_height}",
                'capture_fps': upload_fps
            })

            last_capture_time = 0
            frames_received = 0
            frames_processed = 0
            consecutive_errors = 0
            max_consecutive_errors = 10
            
            while not stop_event.is_set():
                current_time = time.time()
                
                # Check if it's time to capture a frame
                if current_time - last_capture_time < frame_interval:
                    # Drop frame by reading and immediately discarding
                    cap.grab()
                    time.sleep(0.001)
                    continue
                
                # Read frame with validation
                ret = cap.grab()
                if not ret:
                    consecutive_errors += 1
                    if consecutive_errors >= max_consecutive_errors:
                        LOGGER.error(f"Too many consecutive errors ({consecutive_errors}), attempting reconnection")
                        cap.release()
                        try:
                            cap = cls._setup_stream(stream_url)
                            consecutive_errors = 0
                        except Exception as e:
                            LOGGER.error(f"Failed to reconnect: {str(e)}")
                            break
                    continue
                
                ret, frame = cap.retrieve()
                if not ret or not cls._validate_frame(frame):
                    LOGGER.warning("Invalid or corrupted frame detected, skipping")
                    consecutive_errors += 1
                    continue
                
                consecutive_errors = 0  # Reset error counter on successful frame
                frames_received += 1
                
                # Process frame (save as viewpoint image)
                try:
                    # Convert frame to JPEG
                    encode_params = [
                        int(cv2.IMWRITE_JPEG_QUALITY), 95,
                        int(cv2.IMWRITE_JPEG_OPTIMIZE), 1,
                        int(cv2.IMWRITE_JPEG_PROGRESSIVE), 0
                    ]
                    success, buffer = cv2.imencode('.jpg', frame, encode_params)
                    if not success:
                        LOGGER.error("Failed to encode frame as JPEG")
                        continue

                    # Update viewpoint image
                    cls._update_viewpoint_image(lot_id, viewpoint_id, buffer.tobytes())
                    frames_processed += 1
                    current_time = time.time()

                    # Emit stream started event on first successful frame
                    # and then periodically to maintain active status in UI
                    if frames_processed == 1:
                        cls._emit_stream_event('stream_started', lot_id, viewpoint_id)
                    elif frames_processed % 30 == 0:  # Emit active status every 30 frames to keep UI updated
                        cls._emit_stream_event('stream_active', lot_id, viewpoint_id)

                    if frames_processed % 10 == 0:
                        LOGGER.info(f"Stream capture stats - Received: {frames_received}, "
                                  f"Processed: {frames_processed}")
                    
                except Exception as e:
                    LOGGER.error(f"Error processing frame: {str(e)}")
                
                last_capture_time = time.time()
        
        except Exception as e:
            LOGGER.error(f"Error in stream capture loop: {str(e)}")
            cls._emit_stream_event('stream_error', lot_id, viewpoint_id, {'error': str(e)})
            stream_ended_with_error = True
        finally:
            if cap is not None:
                cap.release()
            LOGGER.info(f"Stream capture loop ended for viewpoint {viewpoint_id}")

            # Emit stopped event (stream status is not persisted)
            if not stream_ended_with_error:
                cls._emit_stream_event('stream_stopped', lot_id, viewpoint_id)
            else:
                LOGGER.info(f"Stream ended with error for viewpoint {viewpoint_id}")
    
    @classmethod
    def _update_viewpoint_image(cls, lot_id: str, viewpoint_id: str, image_bytes: bytes):
        """
        Update the viewpoint image with a new frame.

        Args:
            lot_id: ID of the parking lot
            viewpoint_id: ID of the viewpoint
            image_bytes: Image data as bytes
        """
        try:
            # Get current viewpoint to get image path
            from src.utils.storage import StorageService
            lot = StorageService.get_parking_lot(lot_id)
            if not lot:
                return

            viewpoint = lot.get_viewpoint(viewpoint_id)
            if not viewpoint:
                return

            # Update the image file
            from src.config.settings import get_settings
            from src.services.image_handler import ImageHandler
            settings = get_settings()
            upload_folder = settings.get_upload_folder_str()

            relative_path = ImageHandler.update_image_from_bytes(
                image_bytes,
                viewpoint.image_path,
                upload_folder
            )

            # For stream updates, we don't need to update the viewpoint record
            # since we're just overwriting the same image file. This avoids
            # triggering domain events that would change sequence numbers.

            # Emit image update event for real-time updates
            from src.services.event_service import emit_image_update
            emit_image_update(lot_id, relative_path, viewpoint_id)
            
        except Exception as e:
            LOGGER.error(f"Error updating viewpoint image: {str(e)}")
