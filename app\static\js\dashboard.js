document.addEventListener('DOMContentLoaded', function () {
    // Elements
    const runDetectionBtn = document.getElementById('run-detection-btn');
    const occupancyProgress = document.getElementById('occupancy-progress');
    const occupancyLabel = document.querySelector('.progress-label');
    const freeCount = document.getElementById('free-count');
    const occupiedCount = document.getElementById('occupied-count');
    const parkingSlotsOverlay = document.getElementById('parking-slots-overlay');
    const clearLogBtn = document.getElementById('clear-log-btn');
    const imageContainer = document.getElementById('image-container');
    const parkingImage = document.getElementById('parking-image');

    // Initialize zoom manager
    const zoomManager = new ZoomManager(imageContainer, parkingImage);

    let slotsCache = [];
    let lastUpdateTime = 0;
    const UPDATE_DEBOUNCE = 100; // Minimum time between updates in ms
    let previousSlotStates = new Map(); // Track previous states for animation
    const eventsPerPage = 50;

    // Initialize
    loadSlotCoordinates();  // This will handle initial render
    loadEvents(1);

    // Socket.IO setup
    const socket = window.socketConnection;
    
    socket.on('connect', function() {
        console.log('Socket.IO connected successfully');
    });

    socket.on('connect_error', function(error) {
        console.error('Socket.IO connection error:', error);
    });

    socket.on('slot_status_updated', function(data) {
        console.log('Received slot status update:', data);
        if (data.lot_id === LOT_ID) {
            const slot = slotsCache.find(s => s.id === data.slot_id);
            if (slot) {
                const prevState = slot.is_occupied;
                slot.is_occupied = data.is_occupied;
                updateSlotVisual(slot, true);
                updateTableRow(slot);
                  // Add to event log if state actually changed
                if (prevState !== data.is_occupied) {
                    const event = {
                        timestamp: new Date().toISOString(),
                        event_type: data.is_occupied ? 'SLOT_OCCUPIED' : 'SLOT_VACATED',
                        description: slot.slot_name || data.slot_id,
                        source: 'detection'
                    };
                    addEventToList(event);
                }
            }
            refreshCounters();
        }
    });

    socket.on('image_updated', function(data) {
        console.log('Received image update:', data);
        if (data.lot_id === LOT_ID) {
            // For the main dashboard, only update on main lot updates (not viewpoint-specific)
            if (data.viewpoint_id && !data.is_main_lot_update) {
                console.log('Dashboard: Ignoring viewpoint-specific update, this is the main dashboard');
                return;
            }

            // Update the parking lot image
            const parkingImage = document.getElementById('parking-image');
            const timestamp = new Date().getTime(); // Cache busting
            const newImageUrl = `/static/${data.image_path}?t=${timestamp}`;

            // Create new image object to preload
            const newImage = new Image();
            newImage.onload = function() {
                parkingImage.src = newImageUrl;
                // Delay resize handling to ensure image is rendered
                setTimeout(handleResize, 100);
            };
            newImage.src = newImageUrl;
        }
    });

    socket.on('detection_status', function(data) {
        console.log('Received detection status:', data);
        if (data.lot_id === LOT_ID) {
            const alertContainer = document.querySelector('.alert-container') || createAlertContainer();
            
            if (data.status === 'started') {
                showDetectionInProgress(alertContainer);
            } else if (data.status === 'completed') {
                showDetectionComplete(alertContainer, data.updates);
            } else if (data.status === 'error') {
                showDetectionError(alertContainer, data.error);
            }
        }
    });

    socket.on('parking_event', function(data) {
        console.log('Received parking event:', data);
        if (data.lot_id === LOT_ID) {
            addEventToList(data);
        }
    });

    // Debounced version of refreshSlotData to prevent multiple rapid updates
    function debouncedRefreshSlotData(source = 'system') {
        const now = Date.now();
        if (now - lastUpdateTime < UPDATE_DEBOUNCE) {
            setTimeout(() => debouncedRefreshSlotData(source), UPDATE_DEBOUNCE);
            return;
        }
        lastUpdateTime = now;
        refreshSlotData(source);
    }

    function handleResize() {
        const img = document.querySelector('#parking-image');
        const imgContainer = document.querySelector('.img-container');
        if (!img || !imgContainer) return;

        // Calculate and set the correct aspect ratio padding
        const aspectRatio = (img.naturalHeight / img.naturalWidth) * 100;
        imgContainer.style.paddingTop = `${aspectRatio}%`;

        // Update overlay dimensions to match actual image display size
        const imgRect = img.getBoundingClientRect();
        const actualWidth = imgRect.width;
        const actualHeight = imgRect.height;

        if (actualWidth <= 0 || actualHeight <= 0) {
            console.error("Invalid image dimensions:", actualWidth, actualHeight);
            return;
        }

        // Size the overlay to match current image dimensions
        parkingSlotsOverlay.style.width = `${actualWidth}px`;
        parkingSlotsOverlay.style.height = `${actualHeight}px`;

        // Only re-render slots if we have them cached and the image is ready
        if (slotsCache.length > 0 && img.complete) {
            renderParkingSlots(slotsCache);
        }
    }

    // Load slot coordinates and display them on the image
    function loadSlotCoordinates() {
        console.log('Loading slot coordinates...');
        fetch(`/api/lots/${LOT_ID}/slots`)
            .then(response => response.json())
            .then(slots => {
                if (!Array.isArray(slots)) {
                    console.error('Invalid slots data:', slots);
                    return;
                }
                // Initialize previous states
                slots.forEach(slot => {
                    previousSlotStates.set(slot.id, slot.is_occupied);
                });
                
                // Update cache
                slotsCache = slots;
                
                // Wait for image to load before initial render
                const img = document.querySelector('#parking-image');
                if (img.complete) {
                    handleResize();  // This will handle both resize and render
                } else {
                    img.onload = handleResize;  // This will handle both resize and render when image loads
                }
            })
            .catch(error => console.error('Error loading slot coordinates:', error));
    }

    // Coordinate conversion helpers
    function convertToPercentage(x, y, img) {
        // Use displayed dimensions - coordinates in DB are relative to displayed size, not natural size
        const width = img.offsetWidth;
        const height = img.offsetHeight;

        if (width === 0 || height === 0) {
            console.warn('Image dimensions not available for coordinate conversion');
            return { x: 0, y: 0 };
        }

        return {
            x: (x / width) * 100,
            y: (y / height) * 100
        };
    }

    function convertToPixels(x, y, img) {
        // Use displayed dimensions - coordinates in DB are relative to displayed size, not natural size
        const width = img.offsetWidth;
        const height = img.offsetHeight;

        if (width === 0 || height === 0) {
            console.warn('Image dimensions not available for coordinate conversion');
            return { x: 0, y: 0 };
        }

        return {
            x: (x * width) / 100,
            y: (y * height) / 100
        };
    }

    // Render parking slots on the image
    function renderParkingSlots(slots) {
        console.log('Rendering parking slots:', slots);
        parkingSlotsOverlay.innerHTML = '';

        const img = document.querySelector('#parking-image');
        if (!img || !img.complete) {
            console.error("Image not ready");
            return;
        }

        const imgWidth = img.offsetWidth;
        const imgHeight = img.offsetHeight;

        if (imgWidth <= 0 || imgHeight <= 0) {
            console.error("Invalid image dimensions:", imgWidth, imgHeight);
            return;
        }

        parkingSlotsOverlay.style.width = `${imgWidth}px`;
        parkingSlotsOverlay.style.height = `${imgHeight}px`;

        slots.forEach(slot => {
            if (slot.detection_zone && slot.detection_zone.points) {
                // API returns pixel coordinates, so we need to convert them to percentages for clip-path
                const pixelPoints = slot.detection_zone.points;
                const percentagePoints = pixelPoints.map(point => convertToPercentage(point.x, point.y, img));

                // Create polygon
                const polygon = document.createElement('div');
                polygon.className = `parking-slot ${slot.is_occupied ? 'occupied' : 'free'}`;
                polygon.dataset.slotId = slot.id;

                // Set up clip-path for polygon shape using percentage coordinates
                const clipPath = `polygon(${percentagePoints.map(p => `${p.x}% ${p.y}%`).join(', ')})`;
                polygon.style.clipPath = clipPath;
                polygon.style.webkitClipPath = clipPath;

                // Position the polygon container
                polygon.style.position = 'absolute';
                polygon.style.left = '0';
                polygon.style.top = '0';
                polygon.style.width = '100%';
                polygon.style.height = '100%';

                // Create label
                const label = document.createElement('div');
                label.className = 'label';
                label.textContent = slot.slot_name || slot.id;

                // Calculate center position from percentage coordinates
                const centerX = percentagePoints.reduce((sum, p) => sum + p.x, 0) / percentagePoints.length;
                const centerY = percentagePoints.reduce((sum, p) => sum + p.y, 0) / percentagePoints.length;

                label.style.left = `${centerX}%`;
                label.style.top = `${centerY}%`;

                parkingSlotsOverlay.appendChild(polygon);
                polygon.appendChild(label);
            }
        });
    }

    // Toggle slot status manually
    function toggleSlotStatus(e) {
        const btn = e.currentTarget;
        const slotId = btn.dataset.slotId;
        const currentStatus = btn.dataset.status;
        const newStatus = currentStatus !== 'occupied';

        btn.disabled = true;
        btn.textContent = 'Updating...';

        fetch(`/api/lot/${LOT_ID}/slots/${slotId}/occupy`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                occupied: newStatus
            })
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to update slot status');
                }
                return response.json();
            })
            .then(slot => {
                debouncedRefreshSlotData('manual');  // Pass source for manual changes
            })
            .catch(error => {
                console.error('Error toggling slot status:', error);
                alert('Failed to update slot status. Please try again.');
            })
            .finally(() => {
                btn.disabled = false;
                btn.textContent = 'Toggle Status';
            });
    }

    // Make refreshSlotData available globally
    window.refreshSlotData = function(source = 'system') {
        fetch(`/api/lots/${LOT_ID}/slots`)
            .then(response => response.json())
            .then(slots => {
                slotsCache = slots;

                slots.forEach(slot => {
                    const previousState = previousSlotStates.get(slot.id);
                    if (previousState !== undefined) {
                        const previousIsOccupied = previousState;
                        const currentIsOccupied = slot.is_occupied;
                        
                        if (previousIsOccupied !== currentIsOccupied) {
                            addEvent(
                                slot.id,
                                previousIsOccupied ? 'occupied' : 'free',
                                currentIsOccupied ? 'occupied' : 'free',
                                source
                            );
                        }
                    }

                    // Update visualizations
                    updateSlotVisual(slot, previousState !== slot.is_occupied);
                    updateTableRow(slot);

                    // Update previous state
                    previousSlotStates.set(slot.id, slot.is_occupied);
                });

                refreshCounters();
            })
            .catch(error => console.error('Error refreshing slot data:', error));
    };

    // Expose refreshSlotStatus function globally so detection_engine.js can use it
    window.refreshSlotStatus = function() {
        const runDetectionBtn = document.getElementById('run-detection-btn');
        if (!runDetectionBtn) return;
        
        runDetectionBtn.disabled = true;
        const originalContent = runDetectionBtn.innerHTML;
        runDetectionBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Updating...';

        fetch(`/dashboard/${LOT_ID}/update`, {
            method: 'POST'
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update stats
                    freeCount.textContent = data.free_slots;
                    occupiedCount.textContent = data.busy_slots;

                    const occupancyRate = data.occupancy_rate;
                    occupancyProgress.style.width = `${occupancyRate}%`;
                    occupancyLabel.textContent = `${occupancyRate.toFixed(1)}% Occupied`;

                    // Update cached data
                    if (window.lot) {
                        window.lot.busy_slots = data.busy_slots;
                        window.lot.free_slots = data.free_slots;
                    }

                    // Show success message
                    const messageContainer = document.createElement('div');
                    messageContainer.className = 'alert alert-success mt-3';
                    messageContainer.textContent = `Updated ${data.updates} slot(s)`;

                    const container = runDetectionBtn.closest('.card-footer');
                    container.appendChild(messageContainer);

                    // Remove message after a delay
                    setTimeout(() => {
                        messageContainer.remove();
                    }, 3000);
                }
            })
            .catch(error => {
                console.error('Error updating statuses:', error);
                alert('Failed to update slot statuses. Please try again.');
            })
            .finally(() => {
                runDetectionBtn.disabled = false;
                runDetectionBtn.innerHTML = originalContent;
            });
    };

    // Update counters based on current slot states
    function refreshCounters() {
        const totalSlots = document.querySelectorAll('.parking-slot').length;
        const occupiedSlots = document.querySelectorAll('.parking-slot.occupied').length;
        const freeSlots = totalSlots - occupiedSlots;

        freeCount.textContent = freeSlots;
        occupiedCount.textContent = occupiedSlots;

        const occupancyRate = totalSlots > 0 ? (occupiedSlots / totalSlots * 100) : 0;
        occupancyProgress.style.width = `${occupancyRate}%`;
        occupancyLabel.textContent = `${occupancyRate.toFixed(1)}% Occupied`;
    }

    function createAlertContainer() {
        const alertContainer = document.createElement('div');
        alertContainer.className = 'alert-container mt-3';
        const container = document.querySelector('.container');
        container.insertBefore(alertContainer, container.querySelector('.row'));
        return alertContainer;
    }

    function showDetectionInProgress(container) {
        clearAlerts(container);
        const alert = document.createElement('div');
        alert.className = 'alert alert-info';
        alert.innerHTML = `
            <div class="d-flex align-items-center">
                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Running detection...
            </div>
        `;
        container.appendChild(alert);
    }

    function showDetectionComplete(container, updates) {
        clearAlerts(container);
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show';
        alert.innerHTML = `
            Detection completed successfully! Updated ${updates} slot(s).
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        container.appendChild(alert);
    }

    function showDetectionError(container, error) {
        clearAlerts(container);
        const alert = document.createElement('div');
        alert.className = 'alert alert-danger alert-dismissible fade show';
        alert.innerHTML = `
            ${error}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        container.appendChild(alert);
    }

    function clearAlerts(container) {
        while (container.firstChild) {
            container.removeChild(container.firstChild);
        }
    }

    // Unified event handling function
    function addEventToList(event) {
        const eventsList = document.getElementById('events-list');
        if (!eventsList) return;

        const eventItem = document.createElement('div');
        eventItem.className = 'event-item';

        const timestamp = new Date(event.timestamp);
        const formattedTime = timestamp.toLocaleString() + '.' + timestamp.getMilliseconds().toString().padStart(3, '0');
        
        let eventText = '';
        switch (event.event_type) {
            case 'SLOT_OCCUPIED':
                eventText = `Slot ${event.description} became occupied`;
                break;
            case 'SLOT_VACATED':
                eventText = `Slot ${event.description} became vacant`;
                break;
            case 'SLOT_UPDATED':
                eventText = `Slot ${event.description} was updated`;
                break;
            case 'SLOT_CREATED':
                eventText = `New slot ${event.description} was created`;
                break;
            case 'SLOT_DELETED':
                eventText = `Slot ${event.description} was deleted`;
                break;
            default:
                eventText = `${event.event_type} event for slot ${event.description}`;
        }
        
        eventItem.innerHTML = `
            <span class="event-time">${formattedTime}</span>
            <span class="event-text">${eventText}</span>
        `;
        
        // Insert at the beginning of the list
        eventsList.insertBefore(eventItem, eventsList.firstChild);

        // Also update the event log table
        const row = document.createElement('tr');
        const changeClass = getEventChangeClass(event.event_type);
        const displayEventType = getDisplayEventType(event.event_type);

        row.innerHTML = `
            <td>${timestamp.toLocaleTimeString()}.${timestamp.getMilliseconds().toString().padStart(3, '0')}</td>
            <td>${event.description || 'N/A'}</td>
            <td>
                <span class="status-change ${changeClass}">
                    ${displayEventType}
                </span>
            </td>
            <td>
                <span class="source-badge source-${event.source || 'system'}">
                    ${event.source ? event.source.charAt(0).toUpperCase() + event.source.slice(1) : 'System'}
                </span>
            </td>
        `;
        
        const eventLogBody = document.getElementById('event-log-body');
        if (eventLogBody) {
            eventLogBody.insertBefore(row, eventLogBody.firstChild);
        }
    }

    // Helper functions for event display
    function getEventChangeClass(eventType) {
        if (eventType.toLowerCase().includes('occupied')) {
            return 'to-occupied';
        } else if (eventType.toLowerCase().includes('vacated') || eventType.toLowerCase().includes('free')) {
            return 'to-free';
        } else if (eventType.includes('Added') || eventType.includes('Created') || eventType.includes('Activated')) {
            return 'to-occupied'; // Green for additions/activations
        } else if (eventType.includes('Removed') || eventType.includes('Deleted') || eventType.includes('Deactivated')) {
            return 'to-free'; // Red for removals/deactivations
        } else {
            return 'to-neutral'; // Default neutral color
        }
    }

    function getDisplayEventType(eventType) {
        // Convert technical event types to user-friendly names
        const eventTypeMap = {
            'ParkingSlotOccupancyChanged': 'Occupancy Changed',
            'ParkingSlotAdded': 'Slot Added',
            'ParkingSlotRemoved': 'Slot Removed',
            'ParkingSlotZoneUpdated': 'Zone Updated',
            'ParkingSlotThresholdUpdated': 'Threshold Updated',
            'ParkingLotCreated': 'Lot Created',
            'ParkingLotDeleted': 'Lot Deleted',
            'ParkingLotRenamed': 'Lot Renamed',
            'ParkingLotImageUpdated': 'Image Updated',
            'ViewpointAdded': 'Viewpoint Added',
            'ViewpointRemoved': 'Viewpoint Removed',
            'ViewpointUpdated': 'Viewpoint Updated',
            'ViewpointStreamConfigured': 'Stream Configured',
            'ViewpointStreamActivated': 'Stream Started',
            'ViewpointStreamDeactivated': 'Stream Stopped',
            'DetectionConfigCreated': 'Detection Configured',
            'DetectionConfigUpdated': 'Detection Updated',
            'DetectionConfigDeleted': 'Detection Removed',
            'SLOT_OCCUPIED': 'Occupied',
            'SLOT_VACATED': 'Vacated'
        };

        return eventTypeMap[eventType] || eventType;
    }

    if (clearLogBtn) {
        clearLogBtn.addEventListener('click', () => {
            fetch(`/api/lots/${LOT_ID}/events/clear`, {
                method: 'POST'
            })
            .then(() => {
                const eventsList = document.getElementById('events-list');
                if (eventsList) {
                    eventsList.innerHTML = '';
                }
                const eventLogBody = document.getElementById('event-log-body');
                if (eventLogBody) {
                    eventLogBody.innerHTML = '';
                }
            })
            .catch(error => console.error('Error clearing events:', error));
        });
    }

    function updateSlotVisual(slot, hasChanged) {
        const slotVisual = document.querySelector(`.parking-slot[data-slot-id="${slot.id}"]`);
        if (!slotVisual) return;

        if (hasChanged) {
            slotVisual.classList.remove('status-changed');
            slotVisual.className = `parking-slot ${slot.is_occupied ? 'occupied' : 'free'}`;
            void slotVisual.offsetWidth;
            requestAnimationFrame(() => {
                slotVisual.classList.add('status-changed');
                setTimeout(() => {
                    slotVisual.classList.remove('status-changed');
                }, 1100);
            });
        } else {
            slotVisual.className = `parking-slot ${slot.is_occupied ? 'occupied' : 'free'}`;
        }
    }

    function updateTableRow(slot) {
        const row = document.querySelector(`tr[data-slot-id="${slot.id}"]`);
        if (!row) return;

        const statusBadge = row.querySelector('.status-badge');
        const statusText = slot.is_occupied ? 'Occupied' : 'Free';
        const statusClass = slot.is_occupied ? 'occupied' : 'free';

        statusBadge.className = `status-badge ${statusClass}`;
        statusBadge.textContent = statusText;

        const toggleBtn = row.querySelector('.btn-toggle-status');
        if (toggleBtn) {
            toggleBtn.dataset.status = statusClass;
        }
    }

    // Event listeners
    window.addEventListener('resize', handleResize);

    document.querySelectorAll('.btn-toggle-status').forEach(btn => {
        btn.addEventListener('click', toggleSlotStatus);
    });

    function loadEvents(page = 1) {
        fetch(`/api/lots/${LOT_ID}/events?page=${page}&per_page=${eventsPerPage}`)
            .then(response => response.json())
            .then(data => {
                if (!data.events) {
                    console.error('Invalid events data:', data);
                    return;
                }
                
                if (page === 1) {
                    eventLog = [];
                    document.getElementById('events-list').innerHTML = '';
                }
                
                data.events.forEach(event => {
                    addEventToList(event);
                });
                
                if (data.total > eventsPerPage) {
                    updatePagination(page, Math.ceil(data.total / data.per_page));
                }
            })
            .catch(error => console.error('Error loading events:', error));
    }

    function updatePagination(currentPage, totalPages) {
        const paginationContainer = document.getElementById('events-pagination');
        if (!paginationContainer) return;
        
        paginationContainer.innerHTML = '';
        
        if (totalPages > 1) {
            const prevButton = document.createElement('button');
            prevButton.textContent = 'Previous';
            prevButton.disabled = currentPage === 1;
            prevButton.onclick = () => loadEvents(currentPage - 1);
            
            const nextButton = document.createElement('button');
            nextButton.textContent = 'Next';
            nextButton.disabled = currentPage === totalPages;
            nextButton.onclick = () => loadEvents(currentPage + 1);
            
            paginationContainer.appendChild(prevButton);
            paginationContainer.appendChild(nextButton);
        }
    }

    // Load initial events when dashboard loads
    loadEvents(1);

    // Add fluent hover effect to lot card images
    document.querySelectorAll('.card-img-container').forEach(container => {
        const effect = document.createElement('div');
        effect.className = 'fluent-hover-effect';
        container.appendChild(effect);

        container.addEventListener('mousemove', (e) => {
            const rect = container.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            effect.style.left = `${x}px`;
            effect.style.top = `${y}px`;
        });

        container.addEventListener('mouseleave', () => {
            effect.style.opacity = '0';
        });

        container.addEventListener('mouseenter', () => {
            effect.style.opacity = '1';
        });
    });
});
