#!/usr/bin/env python3
import sys
import os
sys.path.append('app')

from src.utils.event_sourced_storage import EventSourcedStorage
from src.utils.sqlalchemy_event_store import SQLAlchemyEventStore
from src.services.detection_manager import DetectionManager

def main():
    try:
        # Initialize storage
        event_store = SQLAlchemyEventStore()
        storage = EventSourcedStorage(event_store)

        # Get all parking lots
        lots = storage.get_all_parking_lots()
        print(f'Found {len(lots)} parking lots')

        for lot in lots:
            print(f'\nLot: {lot.name} (ID: {lot.id})')
            
            # Get initial state
            initial_events = storage._get_events_for_lot(lot.id)
            initial_occupancy_events = len([e for e in initial_events if 'Occupancy' in e.event_type])
            initial_occupied = sum(1 for slot in lot.slots if slot.is_occupied)
            
            print(f'  Initial state:')
            print(f'    Occupancy events: {initial_occupancy_events}')
            print(f'    Occupied slots: {initial_occupied}/{len(lot.slots)}')
            
            # Check active viewpoints
            active_viewpoints = lot.get_active_viewpoints()
            print(f'    Active viewpoints: {len(active_viewpoints)}')
            
            # Simulate the UI detection call
            print(f'  Simulating UI detection call...')
            
            if len(active_viewpoints) > 1:
                print(f'    Using batch detection (multiple viewpoints)')
                try:
                    batch_results = DetectionManager.run_batch_detection(lot.id)
                    print(f'    Batch results: {batch_results}')
                    if batch_results:
                        total_results = sum(len(results) for results in batch_results.values())
                        print(f'    Total detection results: {total_results}')
                    else:
                        print(f'    No batch results returned')
                except Exception as e:
                    print(f'    Error in batch detection: {e}')
                    import traceback
                    traceback.print_exc()
            else:
                print(f'    Using single detection')
                try:
                    single_results = DetectionManager.run_single_detection(lot.id)
                    print(f'    Single results: {single_results}')
                    if single_results:
                        print(f'    Total detection results: {len(single_results)}')
                    else:
                        print(f'    No single results returned')
                except Exception as e:
                    print(f'    Error in single detection: {e}')
                    import traceback
                    traceback.print_exc()
            
            # Check final state
            final_lot = storage.get_parking_lot(lot.id)
            final_events = storage._get_events_for_lot(lot.id)
            final_occupancy_events = len([e for e in final_events if 'Occupancy' in e.event_type])
            final_occupied = sum(1 for slot in final_lot.slots if slot.is_occupied)
            
            print(f'  Final state:')
            print(f'    Occupancy events: {final_occupancy_events}')
            print(f'    Occupied slots: {final_occupied}/{len(final_lot.slots)}')
            print(f'    New occupancy events: {final_occupancy_events - initial_occupancy_events}')
            
            # Show any new occupancy events
            if final_occupancy_events > initial_occupancy_events:
                new_events = [e for e in final_events if 'Occupancy' in e.event_type][-5:]
                print(f'    Recent occupancy events:')
                for event in new_events:
                    slot_id = event.payload.get('slot_id', 'unknown')
                    is_occupied = event.payload.get('is_occupied', 'unknown')
                    slot = final_lot.get_slot(slot_id)
                    slot_name = slot.slot_name if slot else f'slot_id:{slot_id}'
                    print(f'      {event.timestamp}: {slot_name} -> {"occupied" if is_occupied else "free"}')

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
