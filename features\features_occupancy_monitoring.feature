Feature: Parking Slot Occupancy Monitoring
  As a parking manager
  I want to see the current status of each parking slot
  So that I know which spots are available

  Scenario: View parking slot statuses
    Given the parking lot "Downtown" has 10 configured parking slots
    And 5 slots are currently occupied
    When the user views the dashboard
    Then each slot should display its current status
    And occupied slots should be highlighted in red
    And free slots should be highlighted in green

  Scenario: Connect detection engine
    Given the user has administrator access
    And the parking lot "Downtown" has 12 configured parking slots
    When the user configures a detection engine implementation
    And the user enables automatic detection
    Then the system should use the detection engine to update slot statuses

  Scenario: Update parking slot status
    Given the parking lot "Downtown" has 4 configured parking slots
    And the parking lot "Downtown" has a slot "B3" that is currently free
    When the detection engine identifies a vehicle in slot "B3"
    Then the system should update slot "B3" status to "occupied"
    And slot "B3" should change color to red on the dashboard
