document.addEventListener('DOMContentLoaded', function () {
    const runDetectionBtn = document.getElementById('run-detection-btn');

    if (runDetectionBtn) {
        runDetectionBtn.addEventListener('click', handleRunDetection);
    }

    function handleRunDetection() {
        runDetectionBtn.disabled = true;
        const originalContent = runDetectionBtn.innerHTML;
        runDetectionBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Running detection...';

        // Create alert container if it doesn't exist
        let alertContainer = document.querySelector('.alert-container');
        if (!alertContainer) {
            alertContainer = document.createElement('div');
            alertContainer.className = 'alert-container mt-3';
            const container = document.querySelector('.container');
            container.insertBefore(alertContainer, container.querySelector('.row'));
        }

        // Call the detection endpoint
        fetch(`/dashboard/${LOT_ID}/update`, {
            method: 'POST'
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Detection failed. Please try again.');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Let the socket event handle the update
                    console.log('Detection completed successfully');
                } else {
                    throw new Error(data.error || 'Detection failed. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorMessage(alertContainer, error.message);
            })
            .finally(() => {
                runDetectionBtn.disabled = false;
                runDetectionBtn.innerHTML = originalContent;
            });
    }

    function showErrorMessage(container, message) {
        container.innerHTML = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
    }
});
