"""
FastAPI router for image upload routes
Migrated from routes/image_upload.py
"""

from fastapi import APIRouter, Request, HTTPException, Depends, File, UploadFile, Form
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
import uuid

# Import business logic (unchanged from Flask)
from src.utils.storage import StorageService
from src.utils.logger import setup_logger
from src.services.image_handler import ImageHandler
from src.dependencies import get_templates, get_base_template_context, get_socketio, get_config

# Initialize logger
LOGGER = setup_logger(__name__)

# Create router (no prefix to match Flask blueprint)
router = APIRouter(tags=["image_upload"])

@router.get("/", response_class=HTMLResponse)
async def index(
    request: Request,
    templates: Jinja2Templates = Depends(get_templates)
):
    """Home page with a list of parking lots (matches Flask blueprint)."""
    LOGGER.debug("Starting index page request")
    try:
        LOGGER.debug("Getting all parking lots")
        parking_lots = StorageService.get_all_parking_lots()
        LOGGER.debug(f"Found {len(parking_lots)} parking lots")

        LOGGER.debug("Building template context")
        context = get_base_template_context(request)
        context.update({
            "parking_lots": parking_lots
        })
        LOGGER.debug(f"Context keys: {list(context.keys())}")
        LOGGER.debug("Rendering index.html template")

        return templates.TemplateResponse('index.html', context)
    except Exception as e:
        LOGGER.error(f"Error loading home page: {str(e)}", exc_info=True)
        import traceback
        LOGGER.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/")
async def create_lot_with_image_root(
    request: Request,
    name: str = Form(...),
    file: UploadFile = File(...),
    templates: Jinja2Templates = Depends(get_templates),
    config: dict = Depends(get_config),
    socketio = Depends(get_socketio)
):
    """Handle parking lot creation with image upload at root path."""
    # This is the same implementation as /upload but at root path
    return await create_lot_with_image(request, name, file, templates, config, socketio)

@router.get("/upload", response_class=HTMLResponse)
async def upload_form(
    request: Request,
    templates: Jinja2Templates = Depends(get_templates)
):
    """Upload form page (matches Flask blueprint)."""
    LOGGER.debug("Starting upload form request")
    try:
        LOGGER.debug("Building template context for upload form")
        context = get_base_template_context(request)
        LOGGER.debug(f"Context keys: {list(context.keys())}")
        LOGGER.debug("Rendering upload.html template")

        return templates.TemplateResponse('upload.html', context)
    except Exception as e:
        LOGGER.error(f"Error loading upload form: {str(e)}", exc_info=True)
        import traceback
        LOGGER.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# Helper function for file validation (matches Flask implementation)
def _validate_file(file: UploadFile) -> bool:
    """Validate uploaded file."""
    LOGGER.debug(f"UPLOAD DEBUG: Validating file - filename: {file.filename if file else 'None'}")
    if not file.filename:
        LOGGER.debug(f"UPLOAD DEBUG: File validation failed - no filename")
        return False

    is_allowed = ImageHandler.allowed_file(file.filename)
    LOGGER.debug(f"UPLOAD DEBUG: File validation result for '{file.filename}': {is_allowed}")
    return is_allowed

@router.post("/upload")
async def create_lot_with_source(
    request: Request,
    name: str = Form(...),
    source_type: str = Form("image"),
    file: UploadFile = File(None),
    stream_url: str = Form(None),
    capture_fps: float = Form(1.0),
    viewpoint_name: str = Form("Main Camera"),
    templates: Jinja2Templates = Depends(get_templates),
    config: dict = Depends(get_config),
    socketio = Depends(get_socketio)
):
    """Handle parking lot creation with image upload or stream."""
    LOGGER.info(f"=== UPLOAD DEBUG: Starting creation process ===")
    LOGGER.info(f"UPLOAD DEBUG: Received name='{name}', source_type='{source_type}'")

    if source_type == "stream":
        LOGGER.info(f"UPLOAD DEBUG: Stream URL='{stream_url}', FPS={capture_fps}, Viewpoint='{viewpoint_name}'")
        return await _create_lot_with_stream(request, name, stream_url, capture_fps, viewpoint_name, templates, config, socketio)
    else:
        LOGGER.info(f"UPLOAD DEBUG: File='{file.filename if file else None}', content_type='{file.content_type if file else None}'")
        return await _create_lot_with_image(request, name, file, templates, config, socketio)


async def _create_lot_with_image(
    request: Request,
    name: str,
    file: UploadFile,
    templates: Jinja2Templates,
    config: dict,
    socketio
):
    """Handle parking lot creation with image upload."""
    try:
        # Validate file
        LOGGER.debug(f"UPLOAD DEBUG: Validating file...")
        if not _validate_file(file):
            LOGGER.error(f"UPLOAD DEBUG: File validation failed for: {file.filename if file else 'None'}")
            await socketio.emit('notification', {
                'type': 'error',
                'message': 'No file selected or invalid file type. Only JPG and PNG are supported.'
            }, namespace='/dashboard')

            # Return to upload form with error
            LOGGER.debug(f"UPLOAD DEBUG: Returning to upload form with error")
            parking_lots = StorageService.get_all_parking_lots()
            context = get_base_template_context(request)
            context.update({"parking_lots": parking_lots})
            return templates.TemplateResponse('upload.html', context)

        try:
            # Generate unique lot ID
            lot_id = str(uuid.uuid4())
            LOGGER.info(f"UPLOAD DEBUG: Generated lot ID: {lot_id}")

            # Save the image
            LOGGER.debug(f"UPLOAD DEBUG: Creating ImageHandler with storage_dir: {config['UPLOAD_FOLDER']}")
            image_handler = ImageHandler(storage_dir=config['UPLOAD_FOLDER'])

            LOGGER.debug(f"UPLOAD DEBUG: Reading file bytes...")
            image_bytes = await file.read()
            LOGGER.info(f"UPLOAD DEBUG: Read {len(image_bytes)} bytes from file")

            LOGGER.debug(f"UPLOAD DEBUG: Saving image...")
            relative_path = image_handler.save_image(image_bytes, lot_id, file.filename)
            LOGGER.info(f"UPLOAD DEBUG: Image saved to: {relative_path}")

            # Create parking lot using event sourcing
            LOGGER.debug(f"UPLOAD DEBUG: Creating parking lot in database...")
            created_lot_id = StorageService.create_parking_lot_with_id(
                lot_id=lot_id,
                name=name,
                image_path=relative_path
            )

            if not created_lot_id:
                LOGGER.error(f'UPLOAD DEBUG: Failed to create parking lot {lot_id} via web interface')
                await socketio.emit('notification', {
                    'type': 'error',
                    'message': 'Failed to create parking lot'
                }, namespace='/dashboard')
                return RedirectResponse(url="/upload", status_code=302)

            # Create a default viewpoint for the parking lot
            LOGGER.debug(f"UPLOAD DEBUG: Creating default viewpoint for parking lot...")
            default_viewpoint_id = StorageService.add_viewpoint(
                lot_id=created_lot_id,
                name="Default View",
                image_path=relative_path,
                camera_position=None,
                is_active=True,
                sort_order=0
            )

            if default_viewpoint_id:
                # Set this viewpoint as the default
                LOGGER.debug(f"UPLOAD DEBUG: Setting default viewpoint {default_viewpoint_id}")
                StorageService.set_default_viewpoint(created_lot_id, default_viewpoint_id)
                LOGGER.info(f"UPLOAD DEBUG: Created default viewpoint {default_viewpoint_id} for lot {created_lot_id}")
            else:
                LOGGER.warning(f"UPLOAD DEBUG: Failed to create default viewpoint for lot {created_lot_id}")
                # Don't fail the entire upload if viewpoint creation fails

            LOGGER.info(f'UPLOAD DEBUG: Image saved and parking lot created successfully for lot {name} via web interface')

            # Emit WebSocket event for real-time updates
            LOGGER.debug(f"UPLOAD DEBUG: Emitting WebSocket events...")
            await socketio.emit('lot_created', {
                'lot_id': created_lot_id,
                'name': name,
                'image_path': relative_path
            }, namespace='/dashboard')

            await socketio.emit('notification', {
                'type': 'success',
                'message': 'Image uploaded successfully'
            }, namespace='/dashboard')

            LOGGER.info(f"UPLOAD DEBUG: Redirecting to lot editor for {created_lot_id}")
            return RedirectResponse(url=f"/lot/{created_lot_id}/edit", status_code=302)

        except Exception as e:
            LOGGER.error(f'UPLOAD DEBUG: Error saving file: {str(e)}', exc_info=True)
            await socketio.emit('notification', {
                'type': 'error',
                'message': f'Error saving file: {str(e)}'
            }, namespace='/dashboard')
            return RedirectResponse(url="/upload", status_code=302)

    except Exception as e:
        LOGGER.error(f'UPLOAD DEBUG: Error in lot creation: {str(e)}', exc_info=True)
        await socketio.emit('notification', {
            'type': 'error',
            'message': f'Error creating parking lot: {str(e)}'
        }, namespace='/dashboard')
        return RedirectResponse(url="/upload", status_code=302)

@router.get("/update/{lot_id}", response_class=HTMLResponse)
async def update_image_form(
    request: Request,
    lot_id: str,
    templates: Jinja2Templates = Depends(get_templates),
    socketio = Depends(get_socketio)
):
    """Show image update form for a parking lot."""
    try:
        lot = StorageService.get_parking_lot(lot_id)
        if not lot:
            LOGGER.warning(f'Parking lot not found: {lot_id}')
            await socketio.emit('notification', {
                'type': 'error',
                'message': 'Parking lot not found'
            }, namespace='/dashboard')
            return RedirectResponse(url="/dashboard/", status_code=302)

        context = get_base_template_context(request)
        context.update({
            "lot": lot,
            "lot_id": lot_id
        })

        return templates.TemplateResponse('update_image.html', context)

    except Exception as e:
        LOGGER.error(f"Error loading update form for lot {lot_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/update/{lot_id}")
async def update_image(
    request: Request,
    lot_id: str,
    file: UploadFile = File(...),
    viewpoint_id: str = Form(...),
    templates: Jinja2Templates = Depends(get_templates),
    config: dict = Depends(get_config),
    socketio = Depends(get_socketio)
):
    """Handle parking lot image update using event sourcing."""
    try:
        lot = StorageService.get_parking_lot(lot_id)
        if not lot:
            LOGGER.warning(f'Parking lot not found: {lot_id}')
            await socketio.emit('notification', {
                'type': 'error',
                'message': 'Parking lot not found'
            }, namespace='/dashboard')
            return RedirectResponse(url="/dashboard/", status_code=302)

        # Validate file
        if not _validate_file(file):
            await socketio.emit('notification', {
                'type': 'error',
                'message': 'No file selected or invalid file type. Only JPG and PNG are supported.'
            }, namespace='/dashboard')
            return RedirectResponse(url=f"/update/{lot_id}", status_code=302)

        if not viewpoint_id:
            await socketio.emit('notification', {
                'type': 'error',
                'message': 'Please select a viewpoint'
            }, namespace='/dashboard')
            return RedirectResponse(url=f"/update/{lot_id}", status_code=302)

        try:
            # Read file data
            image_bytes = await file.read()

            # Process image update using ImageHandler
            relative_path = ImageHandler.update_image(
                image_bytes,
                lot.image_path,
                config['UPLOAD_FOLDER']
            )

            # Update lot with new image path using event sourcing
            success = StorageService.update_parking_lot_image(lot_id, relative_path)
            if not success:
                LOGGER.warning(f"Failed to update image path in database for lot {lot_id}")
                await socketio.emit('notification', {
                    'type': 'error',
                    'message': 'Failed to update image path in database'
                }, namespace='/dashboard')
                return RedirectResponse(url=f"/update/{lot_id}", status_code=302)

            # Emit WebSocket event for real-time updates
            await socketio.emit('image_updated', {
                'lot_id': lot_id,
                'image_path': relative_path,
                'viewpoint_id': viewpoint_id
            }, namespace='/dashboard')

            LOGGER.info(f"Successfully updated image for parking lot {lot_id}")
            await socketio.emit('notification', {
                'type': 'success',
                'message': 'Image updated successfully'
            }, namespace='/dashboard')

            return RedirectResponse(url=f"/dashboard/{lot_id}", status_code=302)

        except Exception as e:
            LOGGER.error(f'Error updating image for lot {lot_id}: {str(e)}')
            await socketio.emit('notification', {
                'type': 'error',
                'message': f'Error updating image: {str(e)}'
            }, namespace='/dashboard')
            return RedirectResponse(url=f"/update/{lot_id}", status_code=302)

    except Exception as e:
        LOGGER.error(f'Error in image update endpoint for lot {lot_id}: {str(e)}')
        await socketio.emit('notification', {
            'type': 'error',
            'message': f'Error updating image: {str(e)}'
        }, namespace='/dashboard')
        return RedirectResponse(url=f"/update/{lot_id}", status_code=302)


async def _create_lot_with_stream(
    request: Request,
    name: str,
    stream_url: str,
    capture_fps: float,
    viewpoint_name: str,
    templates: Jinja2Templates,
    config: dict,
    socketio
):
    """Handle parking lot creation with video stream."""
    try:
        # Validate stream URL
        if not stream_url or not stream_url.strip():
            LOGGER.error(f"UPLOAD DEBUG: No stream URL provided")
            await socketio.emit('notification', {
                'type': 'error',
                'message': 'Please provide a valid stream URL.'
            }, namespace='/dashboard')

            parking_lots = StorageService.get_all_parking_lots()
            context = get_base_template_context(request)
            context.update({"parking_lots": parking_lots})
            return templates.TemplateResponse('upload.html', context)

        try:
            # Generate unique lot ID
            lot_id = str(uuid.uuid4())
            LOGGER.info(f"UPLOAD DEBUG: Generated lot ID: {lot_id}")

            # Create parking lot using event sourcing (without image initially)
            LOGGER.debug(f"UPLOAD DEBUG: Creating parking lot in database...")
            created_lot_id = StorageService.create_parking_lot_with_id(
                lot_id=lot_id,
                name=name,
                image_path=None  # Will be set when first frame is captured
            )

            if not created_lot_id:
                LOGGER.error(f'UPLOAD DEBUG: Failed to create parking lot {lot_id} via web interface')
                await socketio.emit('notification', {
                    'type': 'error',
                    'message': 'Failed to create parking lot'
                }, namespace='/dashboard')
                return RedirectResponse(url="/upload", status_code=302)

            # Create a placeholder image for the viewpoint
            LOGGER.debug(f"UPLOAD DEBUG: Creating placeholder image...")
            image_handler = ImageHandler(storage_dir=config['UPLOAD_FOLDER'])
            placeholder_path = image_handler.create_placeholder_image(lot_id, "stream_placeholder.jpg")

            # Create a viewpoint with stream configuration
            LOGGER.debug(f"UPLOAD DEBUG: Creating stream viewpoint for parking lot...")
            viewpoint_id = StorageService.add_viewpoint(
                lot_id=created_lot_id,
                name=viewpoint_name,
                image_path=placeholder_path,
                camera_position=None,
                is_active=True,
                sort_order=0
            )

            if not viewpoint_id:
                LOGGER.error(f"UPLOAD DEBUG: Failed to create viewpoint for lot {created_lot_id}")
                await socketio.emit('notification', {
                    'type': 'error',
                    'message': 'Failed to create viewpoint'
                }, namespace='/dashboard')
                return RedirectResponse(url="/upload", status_code=302)

            # Configure stream for the viewpoint
            LOGGER.debug(f"UPLOAD DEBUG: Configuring stream for viewpoint...")
            success = StorageService.configure_viewpoint_stream(
                lot_id=created_lot_id,
                viewpoint_id=viewpoint_id,
                stream_url=stream_url,
                capture_fps=capture_fps
            )

            # Activate stream if configuration succeeded
            if success:
                LOGGER.debug(f"UPLOAD DEBUG: Activating stream for viewpoint...")
                success = StorageService.activate_viewpoint_stream(created_lot_id, viewpoint_id)

            if not success:
                LOGGER.error(f"UPLOAD DEBUG: Failed to update viewpoint with stream config")
                await socketio.emit('notification', {
                    'type': 'warning',
                    'message': 'Parking lot created but stream configuration failed'
                }, namespace='/dashboard')
            else:
                # Set this viewpoint as the default
                LOGGER.debug(f"UPLOAD DEBUG: Setting default viewpoint {viewpoint_id}")
                StorageService.set_default_viewpoint(created_lot_id, viewpoint_id)

                # Start stream capture
                LOGGER.debug(f"UPLOAD DEBUG: Starting stream capture...")
                from src.services.stream_capture_service import StreamCaptureService
                capture_started = StreamCaptureService.start_stream_capture(created_lot_id, viewpoint_id)

                if capture_started:
                    LOGGER.info(f"UPLOAD DEBUG: Stream capture started for viewpoint {viewpoint_id}")
                else:
                    LOGGER.warning(f"UPLOAD DEBUG: Failed to start stream capture for viewpoint {viewpoint_id}")
                    await socketio.emit('notification', {
                        'type': 'warning',
                        'message': 'Parking lot created but stream capture failed to start'
                    }, namespace='/dashboard')

            LOGGER.info(f'UPLOAD DEBUG: Parking lot created successfully with stream for lot {name}')

            # Emit WebSocket event for real-time updates
            LOGGER.debug(f"UPLOAD DEBUG: Emitting WebSocket events...")
            await socketio.emit('lot_created', {
                'lot_id': created_lot_id,
                'name': name,
                'image_path': placeholder_path,
                'stream_url': stream_url
            }, namespace='/dashboard')

            await socketio.emit('notification', {
                'type': 'success',
                'message': 'Parking lot created with video stream successfully'
            }, namespace='/dashboard')

            LOGGER.info(f"UPLOAD DEBUG: Redirecting to lot editor for {created_lot_id}")
            return RedirectResponse(url=f"/lot/{created_lot_id}/edit", status_code=302)

        except Exception as e:
            LOGGER.error(f'UPLOAD DEBUG: Error creating lot with stream: {str(e)}', exc_info=True)
            await socketio.emit('notification', {
                'type': 'error',
                'message': f'Error creating parking lot: {str(e)}'
            }, namespace='/dashboard')
            return RedirectResponse(url="/upload", status_code=302)

    except Exception as e:
        LOGGER.error(f"Unexpected error in _create_lot_with_stream: {str(e)}", exc_info=True)
        await socketio.emit('notification', {
            'type': 'error',
            'message': 'An unexpected error occurred'
        }, namespace='/dashboard')
        return RedirectResponse(url="/upload", status_code=302)
