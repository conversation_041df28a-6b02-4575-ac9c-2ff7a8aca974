"""
Comprehensive tests for the SQLAlchemyStorage class.
"""
import pytest
import os
import uuid
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock, mock_open
import tempfile
import unittest

from app.src.utils.sqlalchemy_storage import SQLAlchemyStorage
from app.src.models.parking import Parking<PERSON>ot, ParkingSlot
from app.src.models.geometry import Polygon, Point
from app.src.models.detection import DetectionEngineConfig
from app.src.models.events import ParkingEvent
from app.src.models.orm import (
    Base, ParkingLotORM, ParkingSlotORM, DetectionZoneORM, PolygonPointORM,
    ParkingEventORM, DetectionEngineConfigORM
)
from sqlalchemy import create_engine
from sqlalchemy.orm import Session


class TestSQLAlchemyStorage:
    """Test suite for SQLAlchemy Storage"""

    @pytest.fixture
    def temp_db_path(self):
        """Create a temporary database file for testing."""
        # Create a temporary file path but don't create the file itself
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            db_path = tmp.name
        
        # Delete the file immediately so SQLAlchemy can create it
        if os.path.exists(db_path):
            os.unlink(db_path)
            
        yield db_path
        
        # Clean up the temporary file after the test
        if os.path.exists(db_path):
            try:
                os.unlink(db_path)
            except PermissionError:
                # On Windows, we might not be able to delete the file
                # if it's still being used by SQLAlchemy
                pass

    @pytest.fixture
    def storage(self, temp_db_path):
        """Create a SQLAlchemyStorage instance with a temporary database."""
        storage = SQLAlchemyStorage(db_path=temp_db_path)
        yield storage
        # Close engine connections to allow file deletion
        if hasattr(storage, 'engine') and storage.engine:
            storage.engine.dispose()

    @pytest.fixture
    def sample_lot(self):
        """Create a sample ParkingLot instance for testing."""
        lot_id = str(uuid.uuid4())
        
        # Create a polygon for the detection zone
        points = [
            Point(x=10, y=10),
            Point(x=100, y=10),
            Point(x=100, y=100),
            Point(x=10, y=100)
        ]
        polygon = Polygon(points=points)
          # Create a slot with the detection zone
        slot = ParkingSlot(
            id=str(uuid.uuid4()),
            slot_name="Test Slot A1",
            detection_zone=polygon,
            is_occupied=False,
            overlap_threshold=0.5
        )
        
        # Create a detection config
        detection_config = DetectionEngineConfig(
            engine_type="yolo",
            is_enabled=True,
            update_interval=30,
            parameters={"confidence": 0.5}
        )
        
        # Create an event
        event = ParkingEvent(
            lot_id=lot_id,
            event_type="lot_created",
            description="Parking lot created",
            payload={"name": "Test Lot"},
            timestamp=datetime.now()
        )
        
        # Create the parking lot
        lot = ParkingLot(
            id=lot_id,
            name="Test Lot",
            image_path="/path/to/image.jpg",
            slots=[slot],
            detection_config=detection_config,
            events=[event]
        )
        
        return lot

    def test_initialization(self, temp_db_path):
        """Test initialization of SQLAlchemyStorage."""
        # Act
        storage = SQLAlchemyStorage(db_path=temp_db_path)
        
        # Assert
        assert storage.engine is not None
        assert storage.SessionLocal is not None
        assert storage._singleton is False

    def test_initialization_with_default_path(self):
        """Test initialization with default database path."""
        # This is the issue - we need to patch the exact location where create_engine is imported
        with patch('app.src.utils.sqlalchemy_storage.create_engine') as mock_create_engine, \
             patch('os.path.exists', return_value=True), \
             patch('os.makedirs') as mock_makedirs, \
             patch('sqlalchemy.orm.sessionmaker'), \
             patch('app.src.utils.sqlalchemy_storage.Base.metadata.create_all'):
            
            # Need to patch open to avoid file system interaction
            with patch('builtins.open', mock_open()):
                # Act
                storage = SQLAlchemyStorage()
                
                # Assert
                assert not mock_makedirs.called
                # The create_engine function should be called during initialization
                assert mock_create_engine.called
                assert storage._singleton is False

    def test_initialization_creates_directory(self):
        """Test initialization creates directory if it doesn't exist."""
        with patch('os.path.exists', return_value=False):
            with patch('os.makedirs') as mock_makedirs:
                with patch('sqlalchemy.create_engine'), patch('sqlalchemy.orm.sessionmaker'), patch('app.src.utils.sqlalchemy_storage.Base.metadata.create_all'):
                    # Act
                    storage = SQLAlchemyStorage(db_path="/some/path/db.sqlite")
                    
                    # Assert
                    mock_makedirs.assert_called_once()

    def test_singleton_instance(self):
        """Test the singleton pattern through the instance class method."""
        # Clear any previous instance
        if hasattr(SQLAlchemyStorage, '_instance'):
            delattr(SQLAlchemyStorage, '_instance')
            
        # Mock initialization to avoid actual DB creation
        with patch.object(SQLAlchemyStorage, '__init__', return_value=None) as mock_init:
            # Act - First call should initialize a new instance
            first_instance = SQLAlchemyStorage.instance()
            
            # Reset the mock to check second call
            mock_init.reset_mock()
            
            # Act - Second call should return existing instance
            second_instance = SQLAlchemyStorage.instance()
            
            # Assert
            assert first_instance is second_instance
            assert mock_init.call_count == 0  # Should not be called the second time

    def test_uow_context_manager_commit(self, storage):
        """Test the Unit of Work context manager with successful commit."""
        # Arrange
        mock_session = MagicMock()
        
        with patch.object(storage, 'SessionLocal', return_value=mock_session):
            # Act
            with storage.uow() as session:
                # Perform some operation
                pass
            
            # Assert
            mock_session.commit.assert_called_once()
            mock_session.rollback.assert_not_called()
            mock_session.close.assert_called_once()

    def test_uow_context_manager_rollback(self, storage):
        """Test the Unit of Work context manager with exception and rollback."""
        # Arrange
        mock_session = MagicMock()
        
        with patch.object(storage, 'SessionLocal', return_value=mock_session):
            # Act
            try:
                with storage.uow() as session:
                    # Raise an exception
                    raise ValueError("Test exception")
            except ValueError:
                pass
            
            # Assert
            mock_session.commit.assert_not_called()
            mock_session.rollback.assert_called_once()
            mock_session.close.assert_called_once()

    def test_get_parking_lot_not_found(self, storage):
        """Test get_parking_lot when the lot doesn't exist."""
        # Arrange
        lot_id = str(uuid.uuid4())
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.options.return_value.filter_by.return_value.first.return_value = None
        
        with patch.object(storage, 'uow') as mock_uow:
            mock_uow.return_value.__enter__.return_value = mock_session
            
            # Act
            result = storage.get_parking_lot(lot_id)
            
            # Assert
            assert result is None
            mock_session.query.assert_called_with(ParkingLotORM)

    def test_get_parking_lot_success(self, storage, sample_lot):
        """Test get_parking_lot with successful retrieval."""
        # Arrange
        lot_id = sample_lot.id
        mock_orm = MagicMock()
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.options.return_value.filter_by.return_value.first.return_value = mock_orm
        
        with patch.object(storage, 'uow') as mock_uow:
            mock_uow.return_value.__enter__.return_value = mock_session
            
            with patch('app.src.utils.mapper.Mapper.orm_to_dataclass_lot', return_value=sample_lot) as mock_mapper:
                # Act
                result = storage.get_parking_lot(lot_id)
                
                # Assert
                assert result is sample_lot
                mock_mapper.assert_called_once_with(mock_orm)

    def test_get_all_parking_lots_empty(self, storage):
        """Test get_all_parking_lots when no lots exist."""
        # Arrange
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.options.return_value.all.return_value = []
        
        with patch.object(storage, 'uow') as mock_uow:
            mock_uow.return_value.__enter__.return_value = mock_session
            
            # Act
            result = storage.get_all_parking_lots()
            
            # Assert
            assert result == []
            mock_session.query.assert_called_with(ParkingLotORM)

    def test_get_all_parking_lots_with_data(self, storage, sample_lot):
        """Test get_all_parking_lots with existing lots."""
        # Arrange
        mock_orm1 = MagicMock()
        mock_orm2 = MagicMock()
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.options.return_value.all.return_value = [mock_orm1, mock_orm2]
        
        with patch.object(storage, 'uow') as mock_uow:
            mock_uow.return_value.__enter__.return_value = mock_session
            
            with patch('app.src.utils.mapper.Mapper.orm_to_dataclass_lot', side_effect=[sample_lot, sample_lot]) as mock_mapper:
                # Act
                result = storage.get_all_parking_lots()
                
                # Assert
                assert len(result) == 2
                assert result[0] is sample_lot
                assert result[1] is sample_lot
                assert mock_mapper.call_count == 2

    def test_create_parking_lot_existing(self, storage, sample_lot):
        """Test create_parking_lot when lot already exists."""
        # Arrange
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.filter_by.return_value.first.return_value = MagicMock()  # Existing lot
        
        with patch.object(storage, 'uow') as mock_uow:
            mock_uow.return_value.__enter__.return_value = mock_session
            
            # Act
            result = storage.create_parking_lot(sample_lot)
            
            # Assert
            assert result is False
            mock_session.add.assert_not_called()

    def test_create_parking_lot_success(self, storage, sample_lot):
        """Test create_parking_lot with successful creation."""
        # Arrange
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.filter_by.return_value.first.return_value = None  # No existing lot
        
        # Mock the 'dataclass_to_orm_config' function which doesn't exist yet
        config_orm = DetectionEngineConfigORM()
        with patch.object(storage, 'uow') as mock_uow, \
             patch('app.src.utils.mapper.Mapper.dataclass_to_orm_config', return_value=config_orm) as mock_mapper:
            
            mock_uow.return_value.__enter__.return_value = mock_session
            
            # Act
            result = storage.create_parking_lot(sample_lot)
            
            # Assert
            assert result is True
            assert mock_session.add.call_count >= 1  # Called for lot and possibly slots, events, etc.
            mock_mapper.assert_called_once()

    def test_update_parking_lot_not_found(self, storage, sample_lot):
        """Test update_parking_lot when lot doesn't exist."""
        # Arrange
        lot_id = sample_lot.id
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.filter_by.return_value.first.return_value = None  # Lot not found
        
        with patch.object(storage, 'uow') as mock_uow:
            mock_uow.return_value.__enter__.return_value = mock_session
            
            # Act
            result = storage.update_parking_lot(lot_id, sample_lot)
            
            # Assert
            assert result is False

    def test_update_parking_lot_success(self, storage, sample_lot):
        """Test update_parking_lot with successful update."""
        # Arrange
        lot_id = sample_lot.id
        mock_orm = MagicMock()
        mock_orm.detection_config = None  # No existing config
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.filter_by.return_value.first.return_value = mock_orm
        
        # Mock the 'dataclass_to_orm_config' function
        config_orm = DetectionEngineConfigORM()
        with patch.object(storage, 'uow') as mock_uow, \
             patch('app.src.utils.mapper.Mapper.dataclass_to_orm_config', return_value=config_orm) as mock_mapper:
            
            mock_uow.return_value.__enter__.return_value = mock_session
            
            # Act
            result = storage.update_parking_lot(lot_id, sample_lot)
            
            # Assert
            assert result is True
            assert mock_orm.name == sample_lot.name
            assert mock_orm.image_path == sample_lot.image_path
            assert mock_orm.slots.clear.called
            mock_mapper.assert_called_once()

    def test_update_parking_lot_with_existing_config(self, storage, sample_lot):
        """Test update_parking_lot when lot has an existing config."""
        # Arrange
        lot_id = sample_lot.id
        mock_orm = MagicMock()
        mock_orm.detection_config = MagicMock()  # Existing config
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.filter_by.return_value.first.return_value = mock_orm
        
        # Mock the 'dataclass_to_orm_config' function
        config_orm = DetectionEngineConfigORM()
        with patch.object(storage, 'uow') as mock_uow, \
             patch('app.src.utils.mapper.Mapper.dataclass_to_orm_config', return_value=config_orm) as mock_mapper:
            
            mock_uow.return_value.__enter__.return_value = mock_session
            
            # Act
            result = storage.update_parking_lot(lot_id, sample_lot)
            
            # Assert
            assert result is True
            assert mock_session.delete.called
            mock_mapper.assert_called_once()

    def test_delete_parking_lot_not_found(self, storage):
        """Test delete_parking_lot when lot doesn't exist."""
        # Arrange
        lot_id = str(uuid.uuid4())
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.filter_by.return_value.first.return_value = None  # Lot not found
        
        with patch.object(storage, 'uow') as mock_uow:
            mock_uow.return_value.__enter__.return_value = mock_session
            
            # Act
            result = storage.delete_parking_lot(lot_id)
            
            # Assert
            assert result is False
            mock_session.delete.assert_not_called()

    def test_delete_parking_lot_success(self, storage):
        """Test delete_parking_lot with successful deletion."""
        # Arrange
        lot_id = str(uuid.uuid4())
        mock_orm = MagicMock()
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.filter_by.return_value.first.return_value = mock_orm
        
        with patch.object(storage, 'uow') as mock_uow:
            mock_uow.return_value.__enter__.return_value = mock_session
            
            # Act
            result = storage.delete_parking_lot(lot_id)
            
            # Assert
            assert result is True
            mock_session.delete.assert_called_once_with(mock_orm)

    def test_add_parking_event_lot_not_found(self, storage):
        """Test add_parking_event when lot doesn't exist."""
        # Arrange
        lot_id = str(uuid.uuid4())
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.filter_by.return_value.first.return_value = None  # Lot not found
        
        event = ParkingEvent(
            lot_id=lot_id,
            event_type="test_event",
            description="Test event",
            payload={"test": "data"},
            timestamp=datetime.now()
        )
        
        with patch.object(storage, 'uow') as mock_uow:
            mock_uow.return_value.__enter__.return_value = mock_session
            
            # Act
            result = storage.add_parking_event(event)
            
            # Assert
            assert result is False
            mock_session.add.assert_not_called()

    def test_add_parking_event_success(self, storage):
        """Test add_parking_event with successful addition."""
        # Arrange
        lot_id = str(uuid.uuid4())
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.filter_by.return_value.first.return_value = True  # Lot exists
        
        event = ParkingEvent(
            lot_id=lot_id,
            event_type="test_event",
            description="Test event",
            payload={"test": "data"},
            timestamp=datetime.now()
        )
        
        with patch.object(storage, 'uow') as mock_uow:
            mock_uow.return_value.__enter__.return_value = mock_session
            
            # Act
            result = storage.add_parking_event(event)
            
            # Assert
            assert result is True
            assert mock_session.add.call_count == 1

    def test_get_detection_config_not_found(self, storage):
        """Test get_detection_config when config doesn't exist."""
        # Arrange
        lot_id = str(uuid.uuid4())
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.filter_by.return_value.first.return_value = None  # Config not found
        
        with patch.object(storage, 'uow') as mock_uow:
            mock_uow.return_value.__enter__.return_value = mock_session
            
            # Act
            result = storage.get_detection_config(lot_id)
            
            # Assert
            assert result is None

    def test_get_detection_config_success(self, storage):
        """Test get_detection_config with successful retrieval."""
        # Arrange
        lot_id = str(uuid.uuid4())
        mock_orm = MagicMock()
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.filter_by.return_value.first.return_value = mock_orm
        
        expected_config = DetectionEngineConfig(
            engine_type="yolo",
            is_enabled=True,
            update_interval=30,
            parameters={"confidence": 0.5}
        )
        
        with patch.object(storage, 'uow') as mock_uow:
            mock_uow.return_value.__enter__.return_value = mock_session
            
            with patch('app.src.utils.mapper.Mapper.orm_to_dataclass_config', return_value=expected_config) as mock_mapper:
                # Act
                result = storage.get_detection_config(lot_id)
                
                # Assert
                assert result is expected_config
                mock_mapper.assert_called_once_with(mock_orm)

    def test_update_detection_config_with_existing_config(self, storage):
        """Test update_detection_config when config already exists."""
        # Arrange
        lot_id = str(uuid.uuid4())
        mock_orm = MagicMock()
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.filter_by.return_value.first.return_value = mock_orm
        
        config = DetectionEngineConfig(
            engine_type="yolo",
            is_enabled=True,
            update_interval=30,
            parameters={"confidence": 0.5}
        )
        
        with patch.object(storage, 'uow') as mock_uow:
            mock_uow.return_value.__enter__.return_value = mock_session
            
            # Act
            result = storage.update_detection_config(lot_id, config)
            
            # Assert
            assert result is True
            assert mock_orm.engine_type == config.engine_type
            assert mock_orm.is_enabled == config.is_enabled
            assert mock_orm.update_interval == config.update_interval
            assert mock_orm.parameters == config.parameters

    def test_update_detection_config_new_config_lot_not_found(self, storage):
        """Test update_detection_config with new config when lot doesn't exist."""
        # Arrange
        lot_id = str(uuid.uuid4())
        mock_session = MagicMock()
        
        # First query for existing config (returns None)
        mock_query1 = MagicMock()
        mock_query1.filter_by.return_value.first.return_value = None
        
        # Second query for lot existence (returns None)
        mock_query2 = MagicMock()
        mock_query2.filter_by.return_value.first.return_value = None
        
        # Set up mock_session to return different query objects
        mock_session.query.side_effect = [mock_query1, mock_query2]
        
        config = DetectionEngineConfig(
            engine_type="yolo",
            is_enabled=True,
            update_interval=30,
            parameters={"confidence": 0.5}
        )
        
        with patch.object(storage, 'uow') as mock_uow:
            mock_uow.return_value.__enter__.return_value = mock_session
            
            # Act
            result = storage.update_detection_config(lot_id, config)
            
            # Assert
            assert result is False
            mock_session.add.assert_not_called()

    def test_update_detection_config_new_config_success(self, storage):
        """Test update_detection_config with new config."""
        # Arrange
        lot_id = str(uuid.uuid4())
        mock_session = MagicMock()
        
        # First query for existing config (returns None)
        mock_query1 = MagicMock()
        mock_query1.filter_by.return_value.first.return_value = None
        
        # Second query for lot existence (returns True)
        mock_query2 = MagicMock()
        mock_query2.filter_by.return_value.first.return_value = True
        
        # Set up mock_session to return different query objects
        mock_session.query.side_effect = [mock_query1, mock_query2]
        
        config = DetectionEngineConfig(
            engine_type="yolo",
            is_enabled=True,
            update_interval=30,
            parameters={"confidence": 0.5}
        )
        
        # Mock the 'dataclass_to_orm_config' function
        config_orm = DetectionEngineConfigORM()
        with patch.object(storage, 'uow') as mock_uow, \
             patch('app.src.utils.mapper.Mapper.dataclass_to_orm_config', return_value=config_orm) as mock_mapper:
            
            mock_uow.return_value.__enter__.return_value = mock_session
            
            # Act
            result = storage.update_detection_config(lot_id, config)
            
            # Assert
            assert result is True
            mock_session.add.assert_called_once()
            mock_mapper.assert_called_once()

    def test_get_parking_events_empty(self, storage):
        """Test get_parking_events when no events exist."""
        # Arrange
        lot_id = str(uuid.uuid4())
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.filter_by.return_value.count.return_value = 0
        mock_query.filter_by.return_value.order_by.return_value.offset.return_value.limit.return_value.all.return_value = []
        
        with patch.object(storage, 'uow') as mock_uow:
            mock_uow.return_value.__enter__.return_value = mock_session
            
            # Act
            events, total = storage.get_parking_events(lot_id, page=1, per_page=10)
            
            # Assert
            assert events == []
            assert total == 0
            mock_session.query.assert_called_with(ParkingEventORM)

    def test_get_parking_events_with_data(self, storage):
        """Test get_parking_events with existing events."""
        # Arrange
        lot_id = str(uuid.uuid4())
        mock_orm1 = MagicMock()
        mock_orm2 = MagicMock()
        
        mock_session = MagicMock()
        mock_query = mock_session.query.return_value
        mock_query.filter_by.return_value.count.return_value = 2
        mock_query.filter_by.return_value.order_by.return_value.offset.return_value.limit.return_value.all.return_value = [mock_orm1, mock_orm2]
        
        event1 = ParkingEvent(
            lot_id=lot_id,
            event_type="event1",
            description="Event 1",
            payload={"data": 1},
            timestamp=datetime.now()
        )
        
        event2 = ParkingEvent(
            lot_id=lot_id,
            event_type="event2",
            description="Event 2",
            payload={"data": 2},
            timestamp=datetime.now()
        )
        
        with patch.object(storage, 'uow') as mock_uow:
            mock_uow.return_value.__enter__.return_value = mock_session
            
            with patch('app.src.utils.mapper.Mapper.orm_to_dataclass_event', side_effect=[event1, event2]) as mock_mapper:
                # Act
                events, total = storage.get_parking_events(lot_id, page=1, per_page=10)
                
                # Assert
                assert len(events) == 2
                assert events[0] == event1
                assert events[1] == event2
                assert total == 2
                assert mock_mapper.call_count == 2