#!/usr/bin/env python3
import sys
import os
sys.path.append('app')

from src.utils.event_sourced_storage import EventSourcedStorage
from src.utils.sqlalchemy_event_store import SQLAlchemyEventStore
from src.services.detection_manager import DetectionManager

def main():
    try:
        # Initialize storage
        event_store = SQLAlchemyEventStore()
        storage = EventSourcedStorage(event_store)

        # Get all parking lots
        lots = storage.get_all_parking_lots()
        print(f'Found {len(lots)} parking lots')

        for lot in lots:
            print(f'\nLot: {lot.name} (ID: {lot.id})')
            
            # Check detection config from storage
            if lot.detection_config:
                print(f'  Detection Config from Storage:')
                print(f'    Engine Type: {lot.detection_config.engine_type}')
                print(f'    Enabled: {lot.detection_config.is_enabled}')
                print(f'    Update Interval: {lot.detection_config.update_interval}')
                print(f'    Parameters: {lot.detection_config.parameters}')
                
                # Check confidence threshold specifically
                if 'confidence_threshold' in lot.detection_config.parameters:
                    print(f'    Confidence Threshold: {lot.detection_config.parameters["confidence_threshold"]}')
            else:
                print('  No detection config in storage')
            
            # Check if there's an engine instance
            engine = DetectionManager.get_engine_instance(lot.id)
            if engine:
                print(f'  Engine Instance: {type(engine).__name__}')
                if hasattr(engine, 'config'):
                    print(f'  Engine Config: {engine.config}')
                    if 'confidence_threshold' in engine.config:
                        print(f'  Engine Confidence Threshold: {engine.config["confidence_threshold"]}')
                else:
                    print('  Engine has no config attribute')
            else:
                print('  No engine instance found')
            
            # Check if detection is running
            is_running = DetectionManager.is_detection_running(lot.id)
            print(f'  Detection Running: {is_running}')

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
