# Services Directory

## Overview
This directory contains the business logic services that orchestrate domain operations and coordinate between different parts of the system.

## Key Services

### Detection & Computer Vision
- **`detection_manager.py`** - Central orchestrator for detection operations across parking lots
- **`detection_service.py`** - Core detection service handling image analysis
- **`yolo_detection_engine.py`** - YOLO-based computer vision engine for vehicle detection
- **`mock_detection_engine.py`** - Mock implementation for testing and development

### Business Logic
- **`occupancy_manager.py`** - Manages parking slot occupancy state and transitions
- **`zone_manager.py`** - Handles parking zone definitions and spatial calculations
- **`event_service.py`** - Event emission and handling for domain events
- **`image_handler.py`** - Image processing and file management

## Architecture Patterns
- **Service Layer Pattern** - Each service encapsulates specific business operations
- **Strategy Pattern** - Multiple detection engines with common interface
- **Event-Driven Architecture** - Services communicate through domain events
- **Dependency Injection** - Services depend on abstractions, not concrete implementations

## Current Capabilities
- Multi-engine detection support (<PERSON><PERSON><PERSON>, <PERSON><PERSON>)
- Real-time occupancy tracking
- Event-driven state management
- Image processing and storage
- Zone-based spatial calculations

## Integration Points
- Database operations through storage layer
- Domain model manipulation
- External detection engines
- File system for image storage
