{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "vscode-edge-devtools.debug",
            "request": "attach",
            "name": "Attach to Microsoft Edge and open the Edge DevTools",
            "url": "http://localhost:5000",
            "webRoot": "${workspaceFolder}/static",
            "port": 9222
        },
        {
            "name": "Python: Flask (app.py)",
            "type": "debugpy",
            "request": "launch",
            "module": "flask",
            "env": {
                "FLASK_APP": "app.py",
                "FLASK_ENV": "development",
                "FLASK_DEBUG": "1"
            },
            "args": [
                "run",
                "--no-debugger", // Important for VS Code's Python debugger to work alongside Flask's
                "--no-reload"    // Helps VS Code's debugger manage sessions
            ],
            "jinja": true,
            "cwd": "${workspaceFolder}"
        },
        {
            "name": "Launch Edge for Old Frontend",
            "type": "msedge", // Changed from "msedge"
            "request": "launch",
            // Make sure your Flask server (from the "Python: Flask" config) is running before launching this.
            // This now points to the general dashboard.
            // If your Flask app runs on a different port, change 5000 accordingly.
            "url": "http://127.0.0.1:5000/dashboard/",
            // webRoot should point to the folder that serves as the root for your static assets.
            // Since your JS files are like /static/js/file.js, and Flask serves the 'static' folder,
            // this should be "${workspaceFolder}/static".
            "webRoot": "${workspaceFolder}/static",
            "sourceMaps": true
        }
    ]
}
