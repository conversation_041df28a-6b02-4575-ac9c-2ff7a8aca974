"""
FastAPI application entry point for ParkingMan
Migrated from Flask while maintaining 100% API compatibility
"""

import os
import traceback
import time
from pathlib import Path
from typing import Optional
import socketio
from contextlib import asynccontextmanager

from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware

# Import Alembic components
from alembic.config import Config
from alembic import command

# Import routers (equivalent to Flask blueprints)
from src.routers import api, dashboard, events, image_upload, zones, viewpoints

# Import business logic (unchanged from Flask)
from src.utils.storage import StorageService
from src.utils.logger import setup_logger

# Import centralized settings
from src.config.settings import settings

# Initialize logger with DEBUG level
import logging

logging.getLogger().setLevel(logging.DEBUG)
LOGGER = setup_logger(__name__)
LOGGER.setLevel(logging.DEBUG)

# Initialize SocketIO
sio = socketio.AsyncServer(
    cors_allowed_origins="*",
    logger=True,
    engineio_logger=True,
    async_mode='asgi'
)


def run_migrations():
    """Run database migrations using Alembic."""
    LOGGER.info("Checking and running database migrations (if applicable)...")

    # Since the app should run from the app directory, use relative paths
    alembic_ini_path = Path("alembic.ini")

    if not alembic_ini_path.exists():
        LOGGER.warning(f"Alembic configuration file not found at {alembic_ini_path}. Skipping migrations.")
        return

    try:
        alembic_cfg = Config(str(alembic_ini_path))
        # The script location should be relative to the current working directory (app)
        alembic_cfg.set_main_option("script_location", "alembic")
        command.upgrade(alembic_cfg, "head")
        LOGGER.info("Database migrations checked/applied successfully.")
    except Exception as e:
        LOGGER.error(f"Error running database migrations: {e}")
        # Don't raise the exception - let the app continue without migrations
        LOGGER.warning("Continuing without running migrations. Database may be in an inconsistent state.")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    LOGGER.info("STARTUP: Starting FastAPI application...")
    LOGGER.debug("STARTUP: Application startup sequence initiated")

    # Run migrations on startup
    try:
        LOGGER.debug("STARTUP: Initializing storage service...")
        LOGGER.info("Storage initialized implicitly.")
        # run_migrations()  # Temporarily disabled - migrations already applied

        # Start all configured streams
        LOGGER.debug("STARTUP: Starting configured video streams...")
        try:
            from src.services.stream_capture_service import StreamCaptureService
            started_count, failed_count = StreamCaptureService.start_all_configured_streams()
            LOGGER.info(f"STARTUP: Stream initialization complete - {started_count} started, {failed_count} failed")
        except Exception as stream_error:
            LOGGER.error(f"STARTUP: Error starting streams: {stream_error}")

        LOGGER.debug("STARTUP: Startup sequence completed successfully")
    except Exception as e:
        LOGGER.error(f"STARTUP: Failed during startup checks: {e}")

    yield

    # Shutdown
    LOGGER.info("SHUTDOWN: Shutting down FastAPI application...")

    # Stop all stream captures
    try:
        LOGGER.debug("SHUTDOWN: Stopping all video stream captures...")
        from src.services.stream_capture_service import StreamCaptureService
        StreamCaptureService.stop_all_captures()
        LOGGER.info("SHUTDOWN: All stream captures stopped")
    except Exception as e:
        LOGGER.error(f"SHUTDOWN: Error stopping streams: {e}")
    LOGGER.debug("SHUTDOWN: Application shutdown sequence completed")


def create_app() -> FastAPI:
    """Create and configure FastAPI application"""

    # Create FastAPI instance
    app = FastAPI(
        title="ParkingMan API",
        description="Parking lot management system with real-time monitoring",
        version="2.0.0-fastapi",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan
    )

    # Store configuration in app state for backward compatibility
    # The actual configuration is now managed by the settings class
    app.state.config = settings.get_config_dict()

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Add request logging middleware
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        """Log all HTTP requests with timing information"""
        start_time = time.time()

        # Log request details
        LOGGER.debug(
            f"REQUEST {request.method} {request.url.path} - Query: {dict(request.query_params)} - Headers: {dict(request.headers)}")

        # Process request
        response = await call_next(request)

        # Calculate processing time
        process_time = time.time() - start_time

        # Log response details
        LOGGER.debug(
            f"RESPONSE {request.method} {request.url.path} - Status: {response.status_code} - Time: {process_time:.4f}s")

        return response

    # Configure Jinja2 templates
    import os
    template_dir = os.path.join(os.path.dirname(__file__), "templates")
    templates = Jinja2Templates(directory=template_dir)
    app.state.templates = templates
    settings.templates = templates

    # Mount static files
    app.mount("/static", StaticFiles(directory="static"), name="static")

    LOGGER.debug("Registering routers and initializing storage...")

    # Register routers (equivalent to Flask blueprints)
    # Order matters - more specific routes first
    LOGGER.debug("ROUTER: Registering API router...")
    app.include_router(api.router)
    LOGGER.debug("ROUTER: Registering dashboard router...")
    app.include_router(dashboard.router)
    LOGGER.debug("ROUTER: Registering events router...")
    app.include_router(events.router)
    LOGGER.debug("ROUTER: Registering viewpoints router...")
    app.include_router(viewpoints.router)
    LOGGER.debug("ROUTER: Registering zones router...")
    app.include_router(zones.router)
    LOGGER.debug("ROUTER: Registering image upload router...")
    app.include_router(image_upload.router)
    LOGGER.debug("ROUTER: All routers registered successfully")

    # Add special routes that were in Flask app.py
    # Note: The root "/" route is handled by image_upload router

    @app.get("/lots/{lot_id}", response_class=HTMLResponse)
    async def lots_redirect(lot_id: str):
        """Redirect /lots/<lot_id> to the proper dashboard route."""
        return RedirectResponse(url=f"/dashboard/{lot_id}", status_code=302)

    # Global exception handler
    @app.exception_handler(Exception)
    async def handle_exception(request: Request, exc: Exception):
        """Handle exceptions globally and log them"""
        LOGGER.error(f"Unhandled exception: {str(exc)}", exc_info=True)

        # Extract detailed error information
        tb_info = traceback.extract_tb(exc.__traceback__)
        last_call = tb_info[-1] if tb_info else None

        error_details = {
            'type': exc.__class__.__name__,
            'message': str(exc),
            'traceback': traceback.format_exc(),
            'filename': last_call.filename if last_call else 'N/A',
            'lineno': last_call.lineno if last_call else 'N/A',
            'inner_exception': str(exc.__cause__) if exc.__cause__ else None
        }

        return templates.TemplateResponse(
            'error.html',
            {"request": request, "error": error_details},
            status_code=500
        )

    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint for load balancers"""
        try:
            # Test database connection
            StorageService.get_all_parking_lots()
            return {
                "status": "healthy",
                "version": "2.0.0-fastapi"
            }
        except Exception as e:
            raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")

    LOGGER.debug("SOCKETIO: Initializing socket.io...")

    # Store SocketIO instance in app state for access by routers and settings
    app.state.socketio = sio
    settings.socketio = sio
    LOGGER.debug("SOCKETIO: SocketIO instance stored in app state and settings")

    # Socket.IO event handlers
    @sio.event(namespace='/dashboard')
    async def connect(sid, environ):
        """Handle client connection"""
        LOGGER.info(f'SOCKETIO: Client {sid} connected to dashboard namespace')
        LOGGER.debug(f'SOCKETIO: Connection environment: {environ}')
        await sio.emit('status', {'status': 'connected'}, room=sid, namespace='/dashboard')

    @sio.event(namespace='/dashboard')
    async def disconnect(sid):
        """Handle client disconnect"""
        LOGGER.info(f'SOCKETIO: Client {sid} disconnected from dashboard namespace')

    @sio.on('*', namespace='/dashboard')
    async def catch_all(event, sid, data):
        """Catch all events for debugging"""
        LOGGER.debug(f'Received event {event} from {sid}: {data}')

    # Create SocketIO ASGI app that wraps the FastAPI app
    socket_app = socketio.ASGIApp(sio, app)

    from starlette.concurrency import iterate_in_threadpool

    req_logger = setup_logger('request_logger')

    @app.middleware("http")
    async def middleware(request: Request, call_next):
        try:
            req_body = await request.json()
        except Exception:
            req_body = None

        start_time = time.perf_counter()
        response = await call_next(request)
        process_time = time.perf_counter() - start_time

        res_body = [section async for section in response.body_iterator]
        response.body_iterator = iterate_in_threadpool(iter(res_body))

        is_image_request = request.url.path.startswith("/static/uploads")
        is_over_100_chars = len(str(res_body)) > 100
        resp_msg = "[IMAGE]" if is_image_request else str(res_body) if not is_over_100_chars else "[TRUNCATED]"

        req_logger.info(
            f"{request.method} {request.url.path} - Status: {response.status_code} - Time: {process_time:.4f}s - Request Body: {req_body} - Response Body: {resp_msg}"
        )
        return response

    return socket_app


# Create the FastAPI app instance
app = create_app()

# Run the application
if __name__ == '__main__':
    import uvicorn

    uvicorn.run(
        "main:app",
        host='0.0.0.0',
        port=8000,
        reload=False,
        log_level="debug",
        reload_excludes=["**/logs/**", "**/__pycache__/**"]
    )
