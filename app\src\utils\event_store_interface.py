
from abc import ABC, abstractmethod
from typing import List, Optional
from src.models.events import BaseEvent
from src.models.orm import Parking<PERSON>ventORM # Or just use BaseEvent if ORM details are hidden

class EventStore(ABC):
    """Abstract interface for storing and retrieving domain events."""

    @abstractmethod
    def append_event(self, event_orm: ParkingEventORM) -> bool:
        """
        Appends a single event ORM object to the store for a given aggregate.
        Should handle sequence ID assignment and concurrency control (e.g., unique constraint).
        Returns True on success, False on failure (e.g., concurrency conflict).
        """
        pass

    @abstractmethod
    def get_raw_events_for_aggregate(self, aggregate_id: str) -> List[ParkingEventORM]:
        """
        Retrieves all raw event ORM objects for a given aggregate ID,
        ordered by sequence number.
        Returns an empty list if the aggregate doesn't exist or has no events.
        """
        pass

    @abstractmethod
    def get_all_aggregate_ids(self) -> List[str]:
        """
        Retrieves a list of all unique aggregate IDs present in the event store.
        """
        pass
