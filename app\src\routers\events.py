"""
FastAPI router for events endpoints
Migrated from routes/events.py
"""

from fastapi import APIRouter, HTTPException, Request, Query
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import Dict, Any, List

# Import business logic (unchanged from Flask)
from src.utils.storage import StorageService
from src.utils.logger import setup_logger
from src.models.events import BaseEvent

# Initialize logger
LOGGER = setup_logger(__name__)

# Create router (note: different prefix structure to match Flask routes)
router = APIRouter(tags=["events"])

# Define which event types are relevant for users vs system/technical events
USER_RELEVANT_EVENT_TYPES = {
    'ParkingSlotOccupancyChanged',
    'ParkingSlotAdded',
    'ParkingSlotRemoved',
    'ParkingSlotZoneUpdated',
    'ParkingSlotThresholdUpdated',
    'ParkingLotCreated',
    'ParkingLotDeleted',
    'ParkingLotRenamed',
    'ParkingLotImageUpdated',
    'DetectionConfigCreated',
    'DetectionConfigUpdated',
    'DetectionConfigDeleted',
    'ViewpointAdded',
    'ViewpointRemoved',
    'ViewpointUpdated',
    'ViewpointStreamConfigured',
    'ViewpointStreamActivated',
    'ViewpointStreamDeactivated',
    'DefaultViewpointSet'
}

SYSTEM_EVENT_TYPES = {
    'ViewpointStreamStatusChanged',  # Technical stream status updates
    'ViewpointZoneAdded',           # Internal zone management
    'ViewpointZoneUpdated',         # Internal zone management
    'ViewpointZoneRemoved'          # Internal zone management
}


def _filter_user_relevant_events(events: List[BaseEvent]) -> List[BaseEvent]:
    """Filter events to only include those relevant to users."""
    return [event for event in events if event.event_type in USER_RELEVANT_EVENT_TYPES]


def _format_event_for_display(event: BaseEvent) -> Dict[str, Any]:
    """Format an event for display in the UI."""
    # Start with the basic event data
    formatted = event.to_dict()

    # Add display-specific fields
    formatted['description'] = _get_event_description(event)
    formatted['source'] = _get_event_source(event)

    return formatted


def _get_event_description(event: BaseEvent) -> str:
    """Generate a user-friendly description for an event."""
    event_type = event.event_type
    payload = event.payload

    if event_type == 'ParkingSlotOccupancyChanged':
        slot_id = payload.get('slot_id', 'Unknown')
        is_occupied = payload.get('is_occupied', False)
        return f"Slot {slot_id}"

    elif event_type == 'ParkingSlotAdded':
        slot_name = payload.get('slot_name', payload.get('slot_id', 'Unknown'))
        return f"Added slot {slot_name}"

    elif event_type == 'ParkingSlotRemoved':
        slot_id = payload.get('slot_id', 'Unknown')
        return f"Removed slot {slot_id}"

    elif event_type == 'ParkingLotRenamed':
        new_name = payload.get('new_name', 'Unknown')
        return f"Renamed to {new_name}"

    elif event_type == 'ViewpointAdded':
        name = payload.get('name', 'Unknown')
        return f"Added viewpoint {name}"

    elif event_type == 'ViewpointRemoved':
        viewpoint_id = payload.get('viewpoint_id', 'Unknown')
        return f"Removed viewpoint {viewpoint_id}"

    elif event_type == 'ViewpointStreamConfigured':
        viewpoint_id = payload.get('viewpoint_id', 'Unknown')
        return f"Configured stream for {viewpoint_id}"

    elif event_type == 'ViewpointStreamActivated':
        viewpoint_id = payload.get('viewpoint_id', 'Unknown')
        return f"Activated stream for {viewpoint_id}"

    elif event_type == 'ViewpointStreamDeactivated':
        viewpoint_id = payload.get('viewpoint_id', 'Unknown')
        return f"Deactivated stream for {viewpoint_id}"

    # Default fallback
    return event_type.replace('Parking', '').replace('Viewpoint', '')


def _get_event_source(event: BaseEvent) -> str:
    """Determine the source of an event."""
    event_type = event.event_type

    if event_type == 'ParkingSlotOccupancyChanged':
        return 'detection'
    elif event_type.startswith('ViewpointStream'):
        return 'stream'
    elif event_type.startswith('Detection'):
        return 'detection'
    else:
        return 'system'

@router.get("/api/lots/{lot_id}/events")
async def get_lot_events(
    lot_id: str,
    page: int = Query(1, ge=1),
    per_page: int = Query(50, ge=1, le=100),
    include_system_events: bool = Query(False, description="Include technical/system events")
) -> Dict[str, Any]:
    """Get paginated events for a parking lot."""
    try:
        lot = StorageService.get_parking_lot(lot_id)
        if not lot:
            raise HTTPException(status_code=404, detail="Parking lot not found")

        events = StorageService.get_events_for_lot(lot_id)

        # Filter events based on user preference
        if not include_system_events:
            events = _filter_user_relevant_events(events)

        total = len(events)

        # Pagination
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        paged_events = events[start_idx:end_idx]

        # Convert events to display format
        formatted_events = []
        for event in paged_events:
            formatted_event = _format_event_for_display(event)
            formatted_events.append(formatted_event)

        return {
            "events": formatted_events,
            "total": total,
            "page": page,
            "per_page": per_page
        }
    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error retrieving events for lot {lot_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/api/lots/{lot_id}/events/clear")
async def clear_lot_events(lot_id: str) -> Dict[str, Any]:
    """Clear all events for a parking lot."""
    try:
        lot = StorageService.get_parking_lot(lot_id)
        if not lot:
            raise HTTPException(status_code=404, detail="Parking lot not found")
        
        # Use the event-sourced method to clear events
        success = StorageService.clear_events(lot_id)
        return {"success": success}
    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error clearing events for lot {lot_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Event handling functions (used by other routers)
async def handle_slot_status_update(lot_id: str, slot_id: str, is_occupied: bool, socketio):
    """Emit slot status update event."""
    await socketio.emit('slot_status_updated', {
        'lot_id': lot_id,
        'slot_id': slot_id,
        'is_occupied': is_occupied,
        'payload': {
            'slot_id': slot_id,
            'is_occupied': is_occupied
        }
    }, namespace='/dashboard')

async def handle_image_update(lot_id: str, image_path: str, socketio, viewpoint_id: str = None):
    """Emit image update event with optional viewpoint information."""
    await socketio.emit('image_updated', {
        'lot_id': lot_id,
        'image_path': image_path,
        'viewpoint_id': viewpoint_id
    }, namespace='/dashboard')

async def handle_detection_status(lot_id: str, status: str, socketio, updates=None, error=None):
    """Emit detection status event."""
    event_data = {
        'lot_id': lot_id,
        'status': status
    }

    if updates is not None:
        event_data['updates'] = updates
    if error is not None:
        event_data['error'] = error

    await socketio.emit('detection_status', event_data, namespace='/dashboard')

async def handle_parking_event(lot_id: str, event, socketio):
    """Handle a parking event by emitting it."""
    await socketio.emit('parking_event', {
        'lot_id': lot_id,
        'event_type': event.event_type,
        'description': event.description,
        'timestamp': event.timestamp.isoformat() if event.timestamp else None
    }, namespace='/dashboard')
