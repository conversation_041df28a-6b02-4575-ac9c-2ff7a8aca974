# Models Directory

## Overview
This directory contains the core domain models and database ORM definitions for the parking management system.

## Key Files

### Core Domain Models
- **`parking.py`** - Main domain models: `ParkingLot` and `ParkingSlot` with business logic and event handling
- **`geometry.py`** - Geometric primitives: `Point` and `Polygon` for defining detection zones
- **`detection.py`** - Detection engine configuration and result models
- **`events.py`** - Event sourcing models for domain events and parking events
- **`status.py`** - Status tracking models for parking slot states

### Database Layer
- **`orm.py`** - SQLAlchemy ORM models mapping domain models to database tables:
  - `ParkingLotORM` - Parking lot persistence
  - `ParkingSlotORM` - Individual parking slot persistence
  - `DetectionZoneORM` - Geometric zone definitions
  - `PolygonPointORM` - Points defining polygon zones
  - `ParkingEventORM` - Event store for domain events
  - `DetectionEngineConfigORM` - Detection engine configuration persistence

## Current Architecture
- Domain-driven design with rich domain models
- Event sourcing for state changes
- Clear separation between domain models and persistence layer
- Geometric abstractions for spatial calculations

## Key Relationships
- Each `ParkingLot` has multiple `ParkingSlot`s
- Each slot has one `DetectionZone` defined by polygon points
- Detection configuration is associated with parking lots
- Events are stored for audit trail and state reconstruction
