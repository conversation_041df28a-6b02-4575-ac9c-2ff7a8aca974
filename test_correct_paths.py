#!/usr/bin/env python3
import sys
import os
sys.path.append('app')

from src.utils.event_sourced_storage import EventSourcedStorage
from src.utils.sqlalchemy_event_store import SQLAlchemyEventStore

def main():
    try:
        # Initialize storage
        event_store = SQLAlchemyEventStore()
        storage = EventSourcedStorage(event_store)

        # Get all parking lots
        lots = storage.get_all_parking_lots()
        print(f'Found {len(lots)} parking lots')

        for lot in lots:
            print(f'\nLot: {lot.name} (ID: {lot.id})')
            
            # Check viewpoints and their image paths
            active_viewpoints = lot.get_active_viewpoints()
            print(f'  Active viewpoints: {len(active_viewpoints)}')
            
            for viewpoint in active_viewpoints:
                print(f'    Viewpoint: {viewpoint.name} (ID: {viewpoint.id})')
                print(f'      Stored path: {viewpoint.image_path}')
                
                if viewpoint.image_path:
                    # Try with static/ prefix
                    corrected_path = f"static/{viewpoint.image_path}"
                    path1 = corrected_path
                    exists1 = os.path.exists(path1)
                    print(f'      Exists (static/): {exists1} - {path1}')
                    
                    # Try with app/static/ prefix
                    path2 = os.path.join('app', corrected_path)
                    exists2 = os.path.exists(path2)
                    print(f'      Exists (app/static/): {exists2} - {path2}')
                    
                    # Show what the detection engine is looking for
                    detection_path = f"static\\uploads/{os.path.basename(viewpoint.image_path)}"
                    path3 = detection_path
                    exists3 = os.path.exists(path3)
                    print(f'      Detection engine path: {exists3} - {path3}')
                    
                    # Try app/static with backslashes (Windows)
                    path4 = f"app\\static\\uploads\\{os.path.basename(viewpoint.image_path)}"
                    exists4 = os.path.exists(path4)
                    print(f'      Windows path: {exists4} - {path4}')

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
