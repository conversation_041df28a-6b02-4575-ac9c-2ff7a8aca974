from src.models.detection import DetectionEngine, DetectionEngineConfig, DetectionResult
from src.models.parking import ParkingLot
from src.services.mock_detection_engine import MockDetectionEngine
from src.services.yolo_detection_engine import YOLODetectionEngine
from src.services.occupancy_manager import OccupancyManager
from typing import Dict, List, Optional, Type
import threading
import time

from src.utils import logger
from src.utils.storage import StorageService

LOGGER = logger.setup_logger(__name__)


class DetectionManager:
    """Manages detection engines and runs detection processes."""

    # Registry of available detection engine types
    _engine_types: Dict[str, Type[DetectionEngine]] = {
        "mock": MockDetectionEngine,
        "yolo": YOLODetectionEngine
    }

    # Active detection engines
    _engines: Dict[str, DetectionEngine] = {}

    # Detection threads
    _detection_threads: Dict[str, threading.Thread] = {}
    _stop_events: Dict[str, threading.Event] = {}

    @classmethod
    def get_engine_instance(cls, lot_id: str) -> Optional[DetectionEngine]:
        """Get the detection engine instance for a parking lot."""
        engine = cls._engines.get(lot_id)
        if engine:
            return engine

        # If not found, try to create a new instance based on the parking lot's config
        lot = StorageService.get_parking_lot(lot_id)
        if lot and lot.detection_config:
            config = DetectionEngineConfig(**lot.detection_config) if isinstance(lot.detection_config,
                                                                                 dict) else lot.detection_config
            if config.engine_type in cls._engine_types:
                engine = cls._engine_types[config.engine_type]()
                # Configure the engine with the current parameters
                if config.parameters:
                    engine.configure(config.parameters)
                cls._engines[lot_id] = engine
                return engine
        LOGGER.debug("No engine instance found for lot %s", lot_id)
        return None

    @classmethod
    def get_classes(cls, lot_id: str) -> List[str]:
        """Get the list of classes the detection engine can detect."""
        engine = cls.get_engine_instance(lot_id)
        if engine:
            return engine.get_classes()
        return []

    @classmethod
    def get_available_engine_types(cls) -> List[str]:
        """Get the list of available detection engine types."""
        engine_types = list(cls._engine_types.keys())
        LOGGER.debug("Available detection engine types: %s", engine_types)
        return engine_types

    @classmethod
    def get_engine_config(cls, lot_id: str) -> Optional[DetectionEngineConfig]:
        """Get detection engine configuration for a parking lot."""
        # Get from the parking lot's config
        lot = StorageService.get_parking_lot(lot_id)
        if lot and lot.detection_config:
            return DetectionEngineConfig(**lot.detection_config) if isinstance(lot.detection_config,
                                                                               dict) else lot.detection_config
        return None

    @classmethod
    def get_default_config(cls, lot_id: str) -> DetectionEngineConfig:
        """Get a default detection engine configuration."""
        # First try YOLO, then fall back to mock if needed
        try:
            yolo_engine = cls._engine_types["yolo"]()
            if yolo_engine.is_available():
                LOGGER.debug("Creating default YOLO config for lot %s", lot_id)
                return DetectionEngineConfig.for_yolo()
        except Exception as e:
            LOGGER.warning("Could not create YOLO config: %s", str(e))

        # Fall back to mock
        LOGGER.debug("Creating default mock config for lot %s", lot_id)
        return DetectionEngineConfig(
            engine_type="mock",
            is_enabled=False,
            update_interval=60,
            parameters={
                "random_seed": 42,
                "probability_threshold": 0.5,
                "min_confidence": 0.7,
                "max_confidence": 0.95
            }
        )

    @classmethod
    def configure_engine(cls, lot_id: str, config: DetectionEngineConfig) -> bool:
        """
        Configure a detection engine for a parking lot using event sourcing.

        Args:
            lot_id: ID of the parking lot
            config: The detection engine configuration

        Returns:
            True if configuration was successful, False otherwise
        """
        LOGGER.debug("Configuring detection engine for lot %s: %s", lot_id, config)

        # Verify the lot exists
        lot = StorageService.get_parking_lot(lot_id)
        if not lot:
            LOGGER.error("Cannot configure engine: Parking lot %s not found", lot_id)
            return False

        # Stop any existing detection thread
        cls.stop_detection(lot_id)

        if config.engine_type not in cls._engine_types:
            LOGGER.error("Unknown engine type: %s", config.engine_type)
            return False

        # Create or update detection config using event sourcing
        success = StorageService.create_detection_config(
            lot_id=lot_id,
            engine_type=config.engine_type,
            is_enabled=config.is_enabled,
            update_interval=config.update_interval,
            parameters=config.parameters
        )

        if not success:
            LOGGER.error("Failed to update detection config in storage")
            return False

        # Create an engine instance if it doesn't exist or if the engine type has changed
        current_engine = cls._engines.get(lot_id)
        engine_type_changed = False

        if current_engine:
            # Check if the engine type has changed by comparing class names
            current_engine_type = current_engine.__class__.__name__
            new_engine_class = cls._engine_types[config.engine_type]
            new_engine_type = new_engine_class.__name__ if hasattr(new_engine_class, '__name__') else str(
                new_engine_class)
            engine_type_changed = current_engine_type != new_engine_type

        if lot_id not in cls._engines or engine_type_changed:
            LOGGER.debug("Creating new detection engine for lot %s", lot_id)
            cls._engines[lot_id] = cls._engine_types[config.engine_type]()

        # Configure the engine
        if config.parameters:
            cls._engines[lot_id].configure(config.parameters)

        return True

    @classmethod
    def start_detection(cls, lot_id: str) -> bool:
        """Start automatic detection for a parking lot."""
        # Get the current config from a parking lot
        lot = StorageService.get_parking_lot(lot_id)
        if not lot or not lot.detection_config:
            LOGGER.debug("No detection config for lot %s", lot_id)
            return False

        config = DetectionEngineConfig(**lot.detection_config) if isinstance(lot.detection_config,
                                                                             dict) else lot.detection_config

        if not config.is_enabled:
            LOGGER.debug("Auto detection not enabled for lot %s", lot_id)
            return False

        # Stop any existing thread
        cls.stop_detection(lot_id)

        # Create a stop event
        stop_event = threading.Event()
        cls._stop_events[lot_id] = stop_event

        # Create and start a detection thread
        thread = threading.Thread(
            target=cls._detection_loop,
            args=(lot_id, stop_event),
            daemon=True
        )
        cls._detection_threads[lot_id] = thread
        LOGGER.debug("Starting detection thread for lot %s", lot_id)
        thread.start()

        return True

    @classmethod
    def stop_detection(cls, lot_id: str) -> bool:
        """Stop automatic detection for a parking lot."""
        if lot_id in cls._stop_events:
            LOGGER.debug("Stopping detection thread for lot %s", lot_id)
            cls._stop_events[lot_id].set()
            if lot_id in cls._detection_threads and cls._detection_threads[lot_id].is_alive():
                LOGGER.debug("Joining detection thread for lot %s", lot_id)
                cls._detection_threads[lot_id].join(timeout=2.0)
                del cls._detection_threads[lot_id]
            del cls._stop_events[lot_id]
            return True
        LOGGER.debug("No detection thread to stop for lot %s", lot_id)
        return False

    @classmethod
    def run_single_detection(cls, lot_id: str) -> Dict[str, bool]:
        """
        Run a single detection pass for a parking lot using event sourcing.

        This method will automatically use batch detection if multiple viewpoints are available
        and the engine supports it, otherwise falls back to single image detection.

        Args:
            lot_id: ID of the parking lot

        Returns:
            Dictionary mapping slot_ids to true if is occupied, false otherwise
        """
        # Get the parking lot first, regardless of engine existence
        parking_lot = StorageService.get_parking_lot(lot_id)
        if not parking_lot:
            LOGGER.debug("Parking lot %s not found", lot_id)
            return {}

        # Check if we should use batch detection for multiple viewpoints
        active_viewpoints = parking_lot.get_active_viewpoints()
        if len(active_viewpoints) > 1:
            engine = cls.get_engine_instance(lot_id)
            if engine and hasattr(engine, 'detect_batch'):
                LOGGER.info(f"Auto-switching to batch detection for lot {lot_id} with {len(active_viewpoints)} viewpoints")
                batch_results = cls.run_batch_detection(lot_id)
                # Convert batch results to single detection format
                slot_results = {}
                for viewpoint_results in batch_results.values():
                    for result in viewpoint_results:
                        slot_results[result.slot_id] = result.is_occupied
                return slot_results

        if lot_id not in cls._engines:
            # Get engine type from parking lot config
            engine_type = "yolo"  # Default if not specified

            if parking_lot.detection_config:
                config_dict = parking_lot.detection_config
                if isinstance(config_dict, dict):
                    engine_type = config_dict.get("engine_type", "yolo")
                else:
                    engine_type = config_dict.engine_type

            LOGGER.debug("Creating engine of type %s for lot %s", engine_type, lot_id)

            if engine_type in cls._engine_types:
                cls._engines[lot_id] = cls._engine_types[engine_type]()
            else:
                LOGGER.warning("Unknown engine type %s, falling back to yolo", engine_type)
                cls._engines[lot_id] = cls._engine_types["yolo"]()

            # Apply configuration if available
            if parking_lot.detection_config:
                config_dict = parking_lot.detection_config
                if isinstance(config_dict, dict) and "parameters" in config_dict:
                    cls._engines[lot_id].configure(config_dict["parameters"])
                elif hasattr(config_dict, "parameters"):
                    cls._engines[lot_id].configure(config_dict.parameters)

        engine = cls._engines[lot_id]
        if not engine.is_available():
            LOGGER.debug("Detection engine not available for lot %s", lot_id)
            return {}

        # Store previous states to detect changes
        previous_states = {slot.slot_name: slot.is_occupied for slot in parking_lot.slots}

        # Run detection and update slot statuses
        results = engine.detect(lot_id)
        if not results:
            return {}

        # Use the event-sourced update method
        updates = OccupancyManager.update_from_detection_results(lot_id, results)

        # For tracking UI events, determine which slots changed
        for result in results:
            slot = parking_lot.get_slot(result.slot_id)
            if slot:
                slot_has_changed = previous_states.get(slot.slot_name) != result.is_occupied
                if slot_has_changed:
                    previous_states[slot.slot_name] = result.is_occupied
                    # Emit slot status update event
                    from src.routers.events import handle_slot_status_update
                    handle_slot_status_update(lot_id, slot.slot_name, result.is_occupied)

        return updates

    @classmethod
    def run_batch_detection(cls, lot_id: str, viewpoint_ids: Optional[List[str]] = None) -> Dict[str, List[DetectionResult]]:
        """
        Run batch detection for all viewpoints in a parking lot.

        Args:
            lot_id: ID of the parking lot
            viewpoint_ids: Optional list of specific viewpoint IDs to process. If None, processes all active viewpoints.

        Returns:
            Dict mapping viewpoint_id to list of DetectionResult objects
        """
        from src.models.detection import DetectionResult

        parking_lot = StorageService.get_parking_lot(lot_id)
        if not parking_lot:
            LOGGER.debug("Parking lot %s not found", lot_id)
            return {}

        # Ensure engine is available
        engine = cls.get_engine_instance(lot_id)
        if not engine:
            LOGGER.debug("No detection engine available for lot %s", lot_id)
            return {}

        # Check if engine supports batch detection
        if not hasattr(engine, 'detect_batch'):
            LOGGER.warning("Engine does not support batch detection, falling back to individual viewpoint processing")
            return cls._fallback_individual_detection(lot_id, viewpoint_ids)

        # Store previous states to detect changes
        previous_states = {slot.id: slot.is_occupied for slot in parking_lot.slots}

        # Run batch detection
        LOGGER.info(f"Running batch YOLO detection for lot {lot_id} with {len(parking_lot.get_active_viewpoints())} viewpoints")
        batch_results = engine.detect_batch(lot_id, viewpoint_ids)

        if not batch_results:
            LOGGER.warning(f"No batch detection results returned for lot {lot_id}")
            return {}

        # Update occupancy for all results and track changes
        all_detection_results = []
        for viewpoint_id, viewpoint_results in batch_results.items():
            all_detection_results.extend(viewpoint_results)

        # Log detection results before updating occupancy
        LOGGER.info("🔍 BATCH DETECTION RESULTS: Found %d total detection results", len(all_detection_results))
        for result in all_detection_results:
            LOGGER.info("🔍 RESULT: Slot '%s' (ID: %s) -> %s (confidence: %.2f)",
                       result.slot_name, result.slot_id, "OCCUPIED" if result.is_occupied else "FREE", result.confidence)

        # Use event-sourced update method for all results
        updates = OccupancyManager.update_from_detection_results(lot_id, all_detection_results)

        # Emit slot status update events for changed slots
        for result in all_detection_results:
            if result.slot_id in previous_states and previous_states[result.slot_id] != result.is_occupied:
                from src.routers.events import handle_slot_status_update  # Fixed import path
                handle_slot_status_update(lot_id, result.slot_name, result.is_occupied)

        LOGGER.info(f"Batch detection completed for lot {lot_id}: {len(all_detection_results)} total results, {sum(updates.values())} slots updated")
        return batch_results

    @classmethod
    def _fallback_individual_detection(cls, lot_id: str, viewpoint_ids: Optional[List[str]] = None) -> Dict[str, List[DetectionResult]]:
        """
        Fallback method for engines that don't support batch detection.
        Processes each viewpoint individually.
        """
        from src.models.detection import DetectionResult

        parking_lot = StorageService.get_parking_lot(lot_id)
        if not parking_lot:
            return {}

        engine = cls.get_engine_instance(lot_id)
        if not engine:
            return {}

        # Determine viewpoints to process
        if viewpoint_ids is None:
            viewpoints_to_process = parking_lot.get_active_viewpoints()
        else:
            viewpoints_to_process = [v for v in parking_lot.viewpoints if v.id in viewpoint_ids and v.is_active]

        results = {}
        for viewpoint in viewpoints_to_process:
            try:
                # Run detection for this specific viewpoint
                viewpoint_results = engine.detect_viewpoint(lot_id, viewpoint.id)
                if viewpoint_results:
                    results[viewpoint.id] = viewpoint_results
                    LOGGER.debug(f"Individual detection for viewpoint {viewpoint.id}: {len(viewpoint_results)} results")
            except Exception as e:
                LOGGER.error(f"Error in individual detection for viewpoint {viewpoint.id}: {e}")
                results[viewpoint.id] = []

        return results

    @classmethod
    def run_viewpoint_detection(cls, lot_id: str, viewpoint_ids: Optional[List[str]] = None) -> Dict[
        str, Dict[str, bool]]:
        """
        Run detection across specified viewpoints with batch processing.
        
        Args:
            lot_id: ID of the parking lot
            viewpoint_ids: List of viewpoint IDs to process. If None, uses all active viewpoints.
            
        Returns:
            Dict mapping viewpoint_id to slot occupancy results
        """
        parking_lot = StorageService.get_parking_lot(lot_id)
        if not parking_lot:
            LOGGER.debug("Parking lot %s not found", lot_id)
            return {}

        # Get YOLO engine instance
        engine = cls.get_engine_instance(lot_id)
        if not engine:
            LOGGER.debug("No detection engine available for lot %s", lot_id)
            return {}
        if not hasattr(engine, 'detect_batch'):
            LOGGER.warning("Engine does not support batch detection, falling back to single detection")
            # Fallback to single detection for each viewpoint
            results = {}
            if viewpoint_ids:
                for viewpoint_id in viewpoint_ids:
                    # Use regular detect method with viewpoint context
                    single_results = engine.detect(lot_id)
                    # Filter results for this viewpoint
                    parking_lot = StorageService.get_parking_lot(lot_id)
                    if parking_lot:
                        viewpoint_slots = [slot for slot in parking_lot.slots if slot.viewpoint_id == viewpoint_id]
                        viewpoint_slot_ids = [slot.id for slot in viewpoint_slots]
                        filtered_results = {result.slot_id: result.is_occupied
                                            for result in single_results
                                            if result.slot_id in viewpoint_slot_ids}
                        results[viewpoint_id] = filtered_results
            return results

        # Run batch detection - cast to YOLODetectionEngine to access batch methods
        from services.yolo_detection_engine import YOLODetectionEngine
        if isinstance(engine, YOLODetectionEngine):
            batch_results = engine.detect_batch(lot_id, viewpoint_ids)
        else:
            LOGGER.warning("Engine type does not support batch detection")
            return {}

        # Convert to expected format
        viewpoint_occupancy = {}
        for viewpoint_id, detection_results in batch_results.items():
            viewpoint_occupancy[viewpoint_id] = {
                result.slot_id: result.is_occupied
                for result in detection_results
            }

        return viewpoint_occupancy

    @classmethod
    def run_aggregated_detection(cls, lot_id: str, viewpoint_ids: Optional[List[str]] = None) -> Dict[str, bool]:
        """
        Run detection and aggregate results across viewpoints with conflict resolution.
        
        Args:
            lot_id: ID of the parking lot
            viewpoint_ids: List of viewpoint IDs to consider. If None, uses all active viewpoints.
            
        Returns:
            Dict mapping slot_id to aggregated occupancy status
        """
        # Get viewpoint-specific results
        viewpoint_results = cls.run_viewpoint_detection(lot_id, viewpoint_ids)

        if not viewpoint_results:
            return {}

        # Aggregate results with conflict resolution
        aggregated_results = {}

        # Collect all slot IDs across viewpoints
        all_slot_ids = set()
        for viewpoint_data in viewpoint_results.values():
            all_slot_ids.update(viewpoint_data.keys())

        # For each slot, resolve conflicts across viewpoints
        for slot_id in all_slot_ids:
            slot_states = []
            for viewpoint_id, slot_data in viewpoint_results.items():
                if slot_id in slot_data:
                    slot_states.append(slot_data[slot_id])

            if slot_states:
                # Simple majority rule conflict resolution
                occupied_count = sum(1 for state in slot_states if state)
                is_occupied = occupied_count > len(slot_states) / 2
                aggregated_results[slot_id] = is_occupied

        # Update parking lot state using event sourcing
        detection_results = []
        parking_lot = StorageService.get_parking_lot(lot_id)
        if parking_lot:
            for slot_id, is_occupied in aggregated_results.items():
                slot = parking_lot.get_slot(slot_id)
                if slot:
                    detection_results.append(
                        DetectionResult(
                            slot_id=slot_id,
                            slot_name=slot.slot_name,
                            is_occupied=is_occupied,
                            confidence=0.8  # Default confidence for aggregated results
                        )
                    )

            # Update occupancy using event sourcing
            if detection_results:
                OccupancyManager.update_from_detection_results(lot_id, detection_results)

        return aggregated_results

    @classmethod
    def _detection_loop(cls, lot_id: str, stop_event: threading.Event):
        """Background detection loop for a parking lot."""
        while not stop_event.is_set():
            # Get the parking lot with fresh config
            parking_lot = StorageService.get_parking_lot(lot_id)
            if not parking_lot:
                time.sleep(10)  # Default sleep if no config
                continue

            # Get update interval from config
            update_interval = 60  # Default value
            if parking_lot.detection_config:
                config_dict = parking_lot.detection_config
                if isinstance(config_dict, dict):
                    update_interval = config_dict.get("update_interval", 60)
                else:
                    update_interval = config_dict.update_interval

            # Run batch detection for all viewpoints
            try:
                LOGGER.debug(f"Running scheduled batch detection for lot {lot_id}")
                batch_results = cls.run_batch_detection(lot_id)
                if batch_results:
                    total_results = sum(len(results) for results in batch_results.values())
                    LOGGER.info(f"Scheduled batch detection completed for lot {lot_id}: {total_results} results from {len(batch_results)} viewpoints")
                else:
                    LOGGER.warning(f"No results from scheduled batch detection for lot {lot_id}")
            except Exception as e:
                LOGGER.error(f"Error in scheduled batch detection for lot {lot_id}: {e}")

            # Wait for next update interval
            stop_event.wait(timeout=update_interval)
