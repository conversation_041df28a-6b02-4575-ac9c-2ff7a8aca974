"""
Unit tests for event_service.py
"""
import unittest
from unittest.mock import MagicMock, patch
from datetime import datetime, UTC
# Flask import removed - using FastAPI patterns now

from app.src.models.status import SlotStatus
from app.src.models.events import ParkingEvent
from app.src.services.event_service import (
    EventService,
    SlotStatusUpdateEvent,
    emit_parking_event,
    emit_slot_status_update,
    emit_image_update,
    emit_detection_status,
    event_service
)


class TestEventService(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        # Create a mock SocketIO instance for FastAPI
        cls.socketio_mock = MagicMock()

    def setUp(self):
        # Reset mock before each test
        self.socketio_mock.reset_mock()

        # Mock the get_settings function to return our mock SocketIO
        self.settings_patcher = patch('app.src.services.event_service.get_settings')
        self.mock_get_settings = self.settings_patcher.start()

        # Create a mock settings object with our SocketIO mock
        mock_settings = MagicMock()
        mock_settings.socketio = self.socketio_mock
        self.mock_get_settings.return_value = mock_settings

        # Set up for EventService.emit_status_update tests
        self.mock_client = MagicMock()
        # Patch the event clients
        self.client_patcher = patch('app.src.services.event_service.EVENT_CLIENTS', [self.mock_client])
        self.client_patcher.start()

        self.slot_id = "test-slot-1"
        self.lot_id = "test-lot-1"

    def tearDown(self):
        # Stop all patchers
        self.settings_patcher.stop()
        self.client_patcher.stop()
        
    def test_emit_status_update_sends_correct_event(self):
        """Test that emit_status_update sends the right event format"""
        # Arrange
        timestamp = datetime.now()
        status = SlotStatus.OCCUPIED
        
        # Act
        EventService.emit_status_update(
            slot_id=self.slot_id,
            lot_id=self.lot_id,
            status=status,
            timestamp=timestamp
        )
        
        # Assert
        self.mock_client.publish_event.assert_called_once()
        
        # Get the event that was published
        call_args = self.mock_client.publish_event.call_args[0]
        event: SlotStatusUpdateEvent = call_args[0]
        
        # Verify the event has the right data
        self.assertEqual(event.event_type, "slot_status_update")
        self.assertEqual(event.slot_id, self.slot_id)
        self.assertEqual(event.lot_id, self.lot_id)
        self.assertEqual(event.status, status)
        self.assertEqual(event.timestamp, timestamp)
        
    def test_emit_status_update_default_timestamp(self):
        """Test that emit_status_update uses current time if no timestamp provided"""
        # Arrange
        before_call = datetime.now()
        status = SlotStatus.AVAILABLE
        
        # Act
        EventService.emit_status_update(
            slot_id=self.slot_id,
            lot_id=self.lot_id,
            status=status
        )
        after_call = datetime.now()
        
        # Assert
        call_args = self.mock_client.publish_event.call_args[0]
        event: SlotStatusUpdateEvent = call_args[0]
        
        # Verify timestamp is between before and after call times
        self.assertGreaterEqual(event.timestamp, before_call)
        self.assertLessEqual(event.timestamp, after_call)
        
    def test_slot_status_update_event_to_dict(self):
        """Test that SlotStatusUpdateEvent correctly converts to dict"""
        # Arrange
        timestamp = datetime.now()
        status = SlotStatus.OCCUPIED
        
        # Act
        event = SlotStatusUpdateEvent(
            slot_id=self.slot_id,
            lot_id=self.lot_id,
            status=status,
            timestamp=timestamp
        )
        event_dict = event.to_dict()
        
        # Assert
        self.assertEqual(event_dict["event_type"], "slot_status_update")
        self.assertEqual(event_dict["slot_id"], self.slot_id)
        self.assertEqual(event_dict["lot_id"], self.lot_id)
        self.assertEqual(event_dict["status"], status.value)  # Convert enum to string value
        self.assertEqual(event_dict["timestamp"], timestamp.isoformat())  # Convert datetime to ISO string
    
    def test_emit_parking_event(self):
        """Test emit_parking_event function"""
        # Arrange
        lot_id = "test-lot-123"
        event = ParkingEvent(
            lot_id=lot_id,
            event_type="TestType", # Provide event_type for legacy ParkingEvent
            payload={"test": "data"},
            description="Test event description"
        )
        
        # Act
        emit_parking_event(lot_id, event)
        
        # Assert
        self.socketio_mock.emit.assert_called_once()
        args, kwargs = self.socketio_mock.emit.call_args
        self.assertEqual(args[0], 'parking_event')
        self.assertEqual(args[1]['lot_id'], lot_id)
        self.assertEqual(args[1]['event_type'], event.event_type)
        self.assertEqual(kwargs['namespace'], '/dashboard')
    
    def test_emit_slot_status_update(self):
        """Test emit_slot_status_update function"""
        # Arrange
        lot_id = "test-lot-123"
        slot_id = "slot-456"
        is_occupied = True
        
        # Act
        emit_slot_status_update(lot_id, slot_id, is_occupied)
        
        # Assert
        self.socketio_mock.emit.assert_called_once()
        args, kwargs = self.socketio_mock.emit.call_args
        self.assertEqual(args[0], 'slot_status_updated')
        self.assertEqual(args[1]['lot_id'], lot_id)
        self.assertEqual(args[1]['slot_id'], slot_id)
        self.assertEqual(args[1]['is_occupied'], is_occupied)
        self.assertEqual(kwargs['namespace'], '/dashboard')
    
    def test_emit_image_update(self):
        """Test emit_image_update function"""
        # Arrange
        lot_id = "test-lot-123"
        image_path = "/path/to/image.jpg"
        
        # Act
        emit_image_update(lot_id, image_path)
        
        # Assert
        self.socketio_mock.emit.assert_called_once()
        args, kwargs = self.socketio_mock.emit.call_args
        self.assertEqual(args[0], 'image_updated')
        self.assertEqual(args[1]['lot_id'], lot_id)
        self.assertEqual(args[1]['image_path'], image_path)
        self.assertEqual(kwargs['namespace'], '/dashboard')
    
    def test_emit_detection_status_basic(self):
        """Test emit_detection_status function with basic parameters"""
        # Arrange
        lot_id = "test-lot-123"
        status = "processing"
        
        # Act
        emit_detection_status(lot_id, status)
        
        # Assert
        self.socketio_mock.emit.assert_called_once()
        args, kwargs = self.socketio_mock.emit.call_args
        self.assertEqual(args[0], 'detection_status')
        self.assertEqual(args[1]['lot_id'], lot_id)
        self.assertEqual(args[1]['status'], status)
        self.assertEqual(kwargs['namespace'], '/dashboard')
    
    def test_emit_detection_status_with_updates(self):
        """Test emit_detection_status function with updates parameter"""
        # Arrange
        lot_id = "test-lot-123"
        status = "completed"
        updates = {"slots_detected": 5, "occupied_slots": 2}
        
        # Act
        emit_detection_status(lot_id, status, updates=updates)
        
        # Assert
        self.socketio_mock.emit.assert_called_once()
        args, kwargs = self.socketio_mock.emit.call_args
        self.assertEqual(args[0], 'detection_status')
        self.assertEqual(args[1]['lot_id'], lot_id)
        self.assertEqual(args[1]['status'], status)
        self.assertEqual(args[1]['updates'], updates)
        self.assertEqual(kwargs['namespace'], '/dashboard')
    
    def test_emit_detection_status_with_error(self):
        """Test emit_detection_status function with error parameter"""
        # Arrange
        lot_id = "test-lot-123"
        status = "error"
        error = "Failed to process image"
        
        # Act
        emit_detection_status(lot_id, status, error=error)
        
        # Assert
        self.socketio_mock.emit.assert_called_once()
        args, kwargs = self.socketio_mock.emit.call_args
        self.assertEqual(args[0], 'detection_status')
        self.assertEqual(args[1]['lot_id'], lot_id)
        self.assertEqual(args[1]['status'], status)
        self.assertEqual(args[1]['error'], error)
        self.assertEqual(kwargs['namespace'], '/dashboard')
    
    def test_socketio_not_found_graceful_fail(self):
        """Test behavior when socketio is not found in settings"""
        # Arrange
        # Override the settings mock to return None for socketio
        mock_settings = MagicMock()
        mock_settings.socketio = None
        self.mock_get_settings.return_value = mock_settings

        lot_id = "test-lot-123"

        # Act - This should not raise an exception, but log an error
        with patch('app.src.services.event_service.LOGGER') as mock_logger:
            emit_parking_event(lot_id, ParkingEvent(lot_id=lot_id, event_type="Test", payload={"test": "data"}, description="test"))

            # Assert
            mock_logger.error.assert_called_with("SocketIO instance not found in settings.")

    def test_exception_handling_during_emit(self):
        """Test exception handling in emit functions"""
        # Arrange
        self.socketio_mock.emit.side_effect = Exception("Test socket emit exception")
        lot_id = "test-lot-123"
        
        # Act - This should not raise an exception, but log an error
        with patch('app.src.services.event_service.LOGGER') as mock_logger:
            emit_parking_event(lot_id, ParkingEvent(lot_id=lot_id, event_type="Test", payload={"test": "data"}, description="test"))
        
            # Assert
            mock_logger.exception.assert_called_with("Error emitting SocketIO event: %s", 'parking_event')

if __name__ == '__main__':
    unittest.main()