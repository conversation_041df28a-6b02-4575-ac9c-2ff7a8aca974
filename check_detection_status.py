#!/usr/bin/env python3
import sys
import os
sys.path.append('app')

from src.utils.event_sourced_storage import EventSourcedStorage
from src.utils.sqlalchemy_event_store import SQLAlchemyEventStore
from src.services.detection_manager import DetectionManager

def main():
    try:
        # Initialize storage
        event_store = SQLAlchemyEventStore()
        storage = EventSourcedStorage(event_store)

        # Get all parking lots
        lots = storage.get_all_parking_lots()
        print(f'Found {len(lots)} parking lots')

        for lot in lots:
            print(f'\nLot: {lot.name} (ID: {lot.id})')
            
            # Check detection config
            if lot.detection_config:
                print(f'  Detection Config:')
                print(f'    Engine Type: {lot.detection_config.engine_type}')
                print(f'    Enabled: {lot.detection_config.is_enabled}')
                print(f'    Update Interval: {lot.detection_config.update_interval}')
            else:
                print('  No detection config')
                continue
            
            # Check if detection threads are running
            print(f'  Detection Thread Status:')
            if lot.id in DetectionManager._detection_threads:
                thread = DetectionManager._detection_threads[lot.id]
                print(f'    Thread exists: {thread.is_alive()}')
            else:
                print('    No detection thread found')
            
            if lot.id in DetectionManager._stop_events:
                stop_event = DetectionManager._stop_events[lot.id]
                print(f'    Stop event set: {stop_event.is_set()}')
            else:
                print('    No stop event found')
            
            # Try to start detection if it's enabled but not running
            if lot.detection_config.is_enabled:
                if lot.id not in DetectionManager._detection_threads or not DetectionManager._detection_threads[lot.id].is_alive():
                    print('  Detection is enabled but not running. Attempting to start...')
                    success = DetectionManager.start_detection(lot.id)
                    print(f'  Start detection result: {success}')
                else:
                    print('  Detection is already running')
            else:
                print('  Detection is disabled')

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
