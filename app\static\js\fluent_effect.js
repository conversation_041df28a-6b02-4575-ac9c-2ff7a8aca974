class FluentEffect {
    constructor(container) {
        this.container = container;
        this.effect = document.createElement('div');
        this.effect.className = 'fluent-hover-effect';
        this.container.appendChild(this.effect);
        this.isHovered = false;
        this.setupEventListeners();
    }

    setupEventListeners() {
        this.container.addEventListener('mousemove', this.handleMouseMove.bind(this));
        this.container.addEventListener('mouseleave', this.handleMouseLeave.bind(this));
        this.container.addEventListener('mouseenter', this.handleMouseEnter.bind(this));
    }

    handleMouseMove(e) {
        if (!this.isHovered) return;
        
        const rect = this.container.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        requestAnimationFrame(() => {
            this.effect.style.left = `${x}px`;
            this.effect.style.top = `${y}px`;
        });
    }

    handleMouseLeave() {
        this.isHovered = false;
        this.effect.style.opacity = '0';
        this.container.classList.remove('hovered');
    }

    handleMouseEnter() {
        this.isHovered = true;
        this.effect.style.opacity = '1';
        this.container.classList.add('hovered');
    }
}

// Initialize fluent effects on all card image containers
document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('.card-img-container').forEach(container => {
        new FluentEffect(container);
    });
});