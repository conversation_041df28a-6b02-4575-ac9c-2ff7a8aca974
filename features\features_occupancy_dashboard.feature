Feature: Parking Lot Occupancy Dashboard
  As a parking manager
  I want to see a summary of parking lot status
  So that I can make informed decisions

  Scenario: View occupancy statistics
    Given the parking lot "Downtown" has 20 configured parking slots
    And 8 slots are currently occupied
    When the user views the dashboard
    Then the dashboard should show 8 slots occupied
    And the dashboard should show 12 slots available

  Scenario: Filter view by parking slot status
    Given the parking lot has both free and occupied slots
    When the user views the dashboard
    And the user selects the "Show only free slots" filter
    Then the dashboard should only display slots that are currently free