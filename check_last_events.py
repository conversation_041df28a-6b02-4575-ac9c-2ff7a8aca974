#!/usr/bin/env python3
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from src.utils.storage import StorageService

# Get the last few events to see what happened
events = StorageService.get_events_for_lot("2c92e4e0-2503-4a7d-b7b8-88d05eccbae8")

print("Last 10 events:")
for event in events[-10:]:
    print(f"Seq {event.sequence_id}: {event.event_type}")
    if hasattr(event, 'payload'):
        payload = event.payload
        if event.event_type == "ViewpointStreamStatusChanged":
            print(f"  Status: {payload.get('status')}")
            print(f"  Error: {payload.get('error_message')}")
            print(f"  Viewpoint: {payload.get('viewpoint_id')}")
    print()
