{% extends "base_layout.html" %}

{% block title %}Configure Detection Engine - {{ lot.name }}{% endblock %}

{% block extra_css %}
<link href="/static/css/configure_detections.css" rel="stylesheet">
<link href="/static/css/class_autocomplete.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center">
    <h1>Configure Detection Engine - {{ lot.name }}</h1>
    <a href="/dashboard/{{ lot_id }}" class="btn btn-secondary">Back to Dashboard</a>
</div>

<div class="card mt-4" data-lot-id="{{ lot_id }}">
    <div class="card-header">
        <h5 class="mb-0">Detection Engine Configuration</h5>
    </div>
    <div class="card-body">
        <form method="post" action="/dashboard/{{ lot_id }}/configure" id="engine_config_form">
            <div class="config-section">
                <div class="mb-3">
                    <label for="engine_type" class="form-label">Engine Type</label>
                    <select class="form-select" id="engine_type" name="engine_type">
                        {% for engine_type in available_engines %}
                            <option value="{{ engine_type }}"
                                    {% if config.engine_type == engine_type %}selected{% endif %}>
                                {{ engine_type|title }}
                            </option>
                        {% endfor %}
                    </select>
                    <div class="form-text">Select the type of detection engine to use.</div>
                </div>

                <div class="mb-3 form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="is_enabled" name="is_enabled"
                           {% if config.is_enabled %}checked{% endif %}>
                    <label class="form-check-label switch-label" for="is_enabled">Enable Automatic Detection</label>
                    <div class="form-text">When enabled, the system will automatically update parking slot statuses at
                        regular intervals.
                    </div>
                </div>

                <div class="mb-3">
                    <label for="update_interval" class="form-label">Update Interval (seconds)</label>
                    <input type="number" class="form-control" id="update_interval" name="update_interval"
                           value="{{ config.update_interval }}" min="5" max="3600">
                    <div class="form-text">How often the detection engine should run in seconds.</div>
                </div>
            </div>

            <div id="mock_settings" class="engine-specific-settings engine-settings-card"
                 {% if config.engine_type != 'mock' %}style="display: none;"{% endif %}>
                <div class="engine-settings-header">
                    <h5 class="mb-0">Mock Engine Settings</h5>
                </div>
                <div class="engine-settings-body">
                    <div class="mb-3">
                        <label for="random_seed" class="form-label">Random Seed</label>
                        <input type="number" class="form-control" id="random_seed" name="random_seed"
                               value="{{ config.parameters.get('random_seed', 42) }}">
                        <div class="form-text">Seed value for random generation (for predictable testing).</div>
                    </div>

                    <div class="mb-3">
                        <label for="probability_threshold" class="form-label">Probability Threshold</label>
                        <input type="range" class="form-range" id="probability_threshold" name="probability_threshold"
                               value="{{ config.parameters.get('probability_threshold', 0.8) }}" min="0" max="1"
                               step="0.01">
                        <div class="slider-labels">
                            <span>0.0 (All Free)</span>
                            <span class="slider-value-display" id="threshold_value">{{ config.parameters.get('probability_threshold', 0.8) }}</span>
                            <span>1.0 (All Occupied)</span>
                        </div>
                        <div class="form-text">Threshold for determining if a slot is occupied.</div>
                    </div>

                    <div class="mb-3">
                        <label for="min_confidence" class="form-label">Minimum Confidence</label>
                        <input type="number" class="form-control" id="min_confidence" name="min_confidence"
                               value="{{ config.parameters.get('min_confidence', 0.7) }}" min="0" max="1" step="0.01">
                        <div class="form-text">Minimum confidence score for detection results.</div>
                    </div>

                    <div class="mb-3">
                        <label for="max_confidence" class="form-label">Maximum Confidence</label>
                        <input type="number" class="form-control" id="max_confidence" name="max_confidence"
                               value="{{ config.parameters.get('max_confidence', 0.98) }}" min="0" max="1" step="0.01">
                        <div class="form-text">Maximum confidence score for detection results.</div>
                    </div>
                </div>
            </div>

            <div id="yolo_settings" class="engine-specific-settings engine-settings-card"
                 {% if config.engine_type != 'yolo' %}style="display: none;"{% endif %}>
                <div class="engine-settings-header">
                    <h5 class="mb-0">YOLO Engine Settings</h5>
                </div>
                <div class="engine-settings-body">
                    <div class="mb-3">
                        <label for="confidence_threshold" class="form-label">Confidence Threshold</label>
                        <input type="range" class="form-range" id="confidence_threshold" name="confidence_threshold"
                               value="{{ config.parameters.get('confidence_threshold', 0.5) }}" min="0" max="1" step="0.01">
                        <div class="slider-labels">
                            <span>0.0 (Low confidence)</span>
                            <span class="slider-value-display" id="confidence_value">{{ config.parameters.get('confidence_threshold', 0.5) }}</span>
                            <span>1.0 (High confidence)</span>
                        </div>
                        <div class="form-text">Minimum confidence level for vehicle detection.</div>
                    </div>

                    <div class="mb-3">
                        <label for="iou_threshold" class="form-label">IoU Threshold</label>
                        <input type="range" class="form-range" id="iou_threshold" name="iou_threshold"
                               value="{{ config.parameters.get('iou_threshold', 0.45) }}" min="0" max="1" step="0.01">
                        <div class="slider-labels">
                            <span>0.0 (More detections)</span>
                            <span class="slider-value-display" id="iou_value">{{ config.parameters.get('iou_threshold', 0.45) }}</span>
                            <span>1.0 (Fewer duplicates)</span>
                        </div>
                        <div class="form-text">Intersection over Union threshold for Non-Maximum Suppression. Lower values allow more overlapping detections.</div>
                    </div>

                    <div class="mb-3">
                        <label for="overlap_threshold" class="form-label">Overlap Threshold</label>
                        <input type="range" class="form-range" id="overlap_threshold" name="overlap_threshold"
                               value="{{ config.parameters.get('overlap_threshold', 0.30) }}" min="0" max="1" step="0.01">
                        <div class="slider-labels">
                            <span>0.0 (Low overlap)</span>
                            <span class="slider-value-display" id="overlap_value">{{ config.parameters.get('overlap_threshold', 0.30) }}</span>
                            <span>1.0 (High overlap)</span>
                        </div>
                        <div class="form-text">Minimum overlap ratio between vehicle and parking slot to consider occupied.</div>
                    </div>

                    <div class="mb-3">
                        <label for="detection_width" class="form-label">Detection Resolution Width</label>
                        <div class="resolution-control-container">
                            <input type="range" class="form-range" id="detection_width" name="detection_width"
                                   value="{{ config.parameters.get('detection_width', 640) }}" min="320" max="{{ image_width }}" step="32">
                            <button type="button" id="reset_resolution" class="btn btn-sm btn-outline-secondary">Reset to Default</button>
                        </div>
                        <div class="slider-labels">
                            <span>320px (Low)</span>
                            <span class="slider-value-display" id="resolution_value">{{ config.parameters.get('detection_width', 640) }}px</span>
                            <span>{{ image_width }}px (Original)</span>
                        </div>
                        <div class="form-text">Width of the image used for detection (higher = more accurate but slower).</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Detection Classes</label>
                        <div class="form-text">Select which object classes should be detected using the search below.</div>
                    </div>

                    <div class="mb-3 class-autocomplete-container">
                        <label class="form-label">Additional Class Selection</label>
                        <div class="class-input-container">
                            <input type="text" class="form-control" id="class_input" placeholder="Search for more classes...">
                        </div>
                        <div id="classes_list" class="classes-dropdown"></div>
                        <div id="selected_classes" class="selected-classes-container"></div>
                        <input type="hidden" id="selected_classes_input" name="all_classes" value="{{ config.parameters.get('vehicle_classes', []) | tojson }}">
                        <div class="class-autocomplete-help">Type to search and add model classes for detection</div>
                    </div>

                    <div class="mb-3">
                        <label for="model_path" class="form-label">Model Path</label>
                        <input type="text" class="form-control" id="model_path" name="model_path"
                               value="{{ config.parameters.get('model_path', 'yolov8n.pt') }}">
                        <div class="form-text">Path to the YOLO model file (default: yolov8n.pt).</div>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">Save Configuration</button>
                <a href="/dashboard/{{ lot_id }}" class="btn btn-outline-secondary">Cancel</a>
            </div>
            {{ content|safe }}
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="/static/js/configure_detections.js"></script>
<script src="/static/js/class_autocomplete.js"></script>
{% endblock %}
