/* Dashboard-specific styles */

/* Stats section for dashboard overview */
.occupancy-stats {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 24px;
    margin-top: 24px;
}

.stats-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    align-items: center;
}

.stat-box {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

/* Hover animation for stat boxes */
.stat-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    font-size: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
}

.stat-content {
    flex: 1;
}

.stat-box h2 {
    font-size: 2.5rem;
    margin: 0;
    font-weight: bold;
    line-height: 1.2;
}

.stat-box p {
    font-size: 1rem;
    margin: 4px 0 0;
    opacity: 0.9;
}

.stat-box.free {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
    border: 2px solid rgba(25, 135, 84, 0.2);
}

.stat-box.free:hover {
    background-color: rgba(25, 135, 84, 0.15);
}

.stat-box.free .stat-icon {
    color: #198754;
}

.stat-box.occupied {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 2px solid rgba(220, 53, 69, 0.2);
}

.stat-box.occupied:hover {
    background-color: rgba(220, 53, 69, 0.15);
}

.stat-box.occupied .stat-icon {
    color: #dc3545;
}

/* Card styling */
.card {
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
    overflow: hidden;
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem 1.25rem;
    border-radius: 12px 12px 0 0 !important;
}

.card-header h5 {
    font-weight: 600;
    color: #212529;
}

/* Card image container for lot previews */
.card-img-container {
    height: 200px;
    overflow: hidden;
    position: relative;
    display: block;
    cursor: pointer;
}

.card-img-top {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.parking-slots-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

/* Stream indicator for live streams */
.stream-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 10;
}

.stream-indicator .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Add New Lot card */
.border-dashed {
    border-style: dashed !important;
    border-width: 2px !important;
    border-color: #dee2e6 !important;
    background-color: #f8f9fa;
    transition: all 0.2s ease-in-out;
}

.border-dashed:hover {
    border-color: #0d6efd !important;
    background-color: #f1f4ff;
    transform: translateY(-2px);
}

.border-dashed .btn-outline-primary {
    border-width: 2px;
}