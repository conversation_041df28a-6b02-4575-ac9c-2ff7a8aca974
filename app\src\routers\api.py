"""
FastAPI router for API endpoints
Migrated from routes/api.py - maintains 100% compatibility
"""

from fastapi import APIRouter, HTTPException, Depends, Request, File, UploadFile, Form
from fastapi.responses import JSONResponse
from typing import List, Dict, Any, Optional, Union
import threading
import uuid
import logging

# Import business logic (unchanged from Flask)
from src.utils.storage import StorageService
from src.utils.logger import setup_logger
from src.services.image_handler import ImageHandler
from src.services.detection_manager import DetectionManager
from src.services.zone_manager import ZoneManager
from src.dependencies import get_config, get_socketio

# Import Pydantic schemas
from src.schemas import (
    CreateSlotRequest, UpdateSlotStatusRequest, UpdateSlotZoneRequest, DetectionConfigRequest,
    ParkingLotResponse, ParkingSlotResponse, CreateLotResponse, ImageUploadResponse,
    DetectionConfigResponse, DetectionClassesResponse, ParkingLotStatsResponse,
    CreateLotWithStreamRequest,
    convert_parking_lot_to_response, convert_parking_slot_to_response
)

# Initialize logger
LOGGER = setup_logger(__name__)

# Create router
router = APIRouter(prefix="/api", tags=["api"])

@router.get("/lots")
async def get_all_lots() -> List[Dict[str, Any]]:
    """Get all parking lots."""
    LOGGER.info("API request: Get all parking lots")
    try:
        parking_lots = StorageService.get_all_parking_lots()
        LOGGER.debug(f"Retrieved {len(parking_lots)} parking lots")
        return [lot.to_dict() for lot in parking_lots]
    except Exception as e:
        LOGGER.error(f"Error retrieving parking lots: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/lots/{lot_id}")
async def get_parking_lot(lot_id: str) -> Dict[str, Any]:
    """Get details for a specific parking lot."""
    LOGGER.info(f"API request: Get parking lot with ID {lot_id}")
    try:
        lot = StorageService.get_parking_lot(lot_id)
        if not lot:
            LOGGER.warning(f"Parking lot with ID {lot_id} not found")
            raise HTTPException(status_code=404, detail="Parking lot not found")

        LOGGER.debug(f"Retrieved parking lot: {lot.name} (ID: {lot.id})")
        return lot.to_dict()
    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error retrieving parking lot {lot_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Helper function for file validation (matches Flask implementation)
def _api_validate_file(files: Dict[str, UploadFile], lot_name: Optional[str]) -> tuple[Optional[UploadFile], Optional[JSONResponse]]:
    """Validate uploaded file and lot name."""
    if not lot_name:
        return None, JSONResponse(
            content={"error": "Lot name is required"},
            status_code=400
        )

    if 'file' not in files or not files['file']:
        return None, JSONResponse(
            content={"error": "No file uploaded"},
            status_code=400
        )

    file = files['file']
    if not file.filename:
        return None, JSONResponse(
            content={"error": "No file selected"},
            status_code=400
        )

    if not ImageHandler.allowed_file(file.filename):
        return None, JSONResponse(
            content={"error": "Invalid file type. Only JPG and PNG are supported"},
            status_code=400
        )

    return file, None

@router.post("/upload", status_code=201)
async def api_upload_image_and_create_lot(
    name: str = Form(...),
    file: UploadFile = File(...),
    config: dict = Depends(get_config)
) -> CreateLotResponse:
    """Handle parking lot creation with image upload via API."""
    LOGGER.info(f"API request: Create parking lot '{name}' with image upload")

    # Validate file and name
    files = {'file': file}
    validated_file, error_response = _api_validate_file(files, name)
    if error_response:
        raise HTTPException(status_code=400, detail=error_response.body.decode())

    try:
        # Generate unique lot ID
        lot_id = str(uuid.uuid4())
        LOGGER.debug(f"Generated lot ID: {lot_id}")

        # Save the image
        image_handler = ImageHandler(storage_dir=config['UPLOAD_FOLDER'])
        image_bytes = await file.read()
        relative_path = image_handler.save_image(image_bytes, lot_id, file.filename)
        LOGGER.debug(f"Image saved to: {relative_path}")

        # Create parking lot using event sourcing
        created_lot_id = StorageService.create_parking_lot_with_id(
            lot_id=lot_id,
            name=name,
            image_path=relative_path
        )

        if not created_lot_id:
            LOGGER.warning(f'Failed to create parking lot {lot_id} via API event sourcing')
            raise HTTPException(status_code=500, detail="Failed to create parking lot")

        LOGGER.info(f'Image saved and parking lot created successfully for lot {name} via API')
        return CreateLotResponse(
            id=created_lot_id,
            message="Parking lot created successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f'Error creating parking lot: {str(e)}')
        raise HTTPException(status_code=500, detail=f"Error creating parking lot: {str(e)}")


@router.get("/lots/{lot_id}/slots")
async def get_parking_slots(lot_id: str) -> List[ParkingSlotResponse]:
    """Get all parking slots for a specific parking lot."""
    LOGGER.info(f"API request: Get slots for parking lot {lot_id}")

    try:
        lot = StorageService.get_parking_lot(lot_id)
        if not lot:
            LOGGER.warning(f"Parking lot with ID {lot_id} not found when getting slots")
            raise HTTPException(status_code=404, detail="Parking lot not found")

        # Convert slots to response format
        slots_response = [convert_parking_slot_to_response(slot) for slot in lot.slots]

        LOGGER.debug(f"Retrieved {len(slots_response)} slots for lot {lot_id}")
        return slots_response

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error retrieving slots for lot {lot_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/lots/{lot_id}/zones")
async def get_parking_zones(lot_id: str) -> List[Dict[str, Any]]:
    """Get all parking zones for a specific parking lot."""
    LOGGER.info(f"API request: Get zones for parking lot {lot_id}")

    try:
        lot = StorageService.get_parking_lot(lot_id)
        if not lot:
            LOGGER.warning(f"Parking lot with ID {lot_id} not found when getting zones")
            raise HTTPException(status_code=404, detail="Parking lot not found")

        # For now, zones are essentially the same as slots with detection zones
        # This endpoint returns zone data for visualization purposes
        zones = []
        for slot in lot.slots:
            if slot.detection_zone and slot.detection_zone.points:
                zone_data = {
                    "id": slot.id,
                    "slot_name": slot.slot_name,
                    "points": [{"x": p.x, "y": p.y} for p in slot.detection_zone.points],
                    "viewpoint_id": slot.viewpoint_id,
                    "is_occupied": slot.is_occupied
                }
                zones.append(zone_data)

        LOGGER.debug(f"Retrieved {len(zones)} zones for lot {lot_id}")
        return zones

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error retrieving zones for lot {lot_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/lots/{lot_id}/slots", status_code=201)
async def create_slots(
    lot_id: str,
    slot_data: Union[CreateSlotRequest, List[CreateSlotRequest]]
) -> Union[ParkingSlotResponse, List[ParkingSlotResponse]]:
    """Create one or more parking slots using event sourcing."""
    LOGGER.info(f"API request: Create slots for parking lot {lot_id}")

    # Verify the lot exists
    lot = StorageService.get_parking_lot(lot_id)
    if not lot:
        LOGGER.warning(f"Parking lot with ID {lot_id} not found when creating slots")
        raise HTTPException(status_code=404, detail="Parking lot not found")

    # Handle both single slot and list of slots
    if isinstance(slot_data, CreateSlotRequest):
        slot_data = [slot_data]

    created_slots = []

    try:
        for slot_request in slot_data:
            # Generate unique slot ID
            slot_id = str(uuid.uuid4())

            # Convert coordinates to zone points
            zone_points = []
            for coord in slot_request.coordinates:
                if len(coord) >= 2:
                    zone_points.append({"x": coord[0], "y": coord[1]})

            # Create slot using event sourcing
            created_slot_id = StorageService.add_parking_slot(
                lot_id=lot_id,
                slot_name=slot_request.slot_name,
                zone_points=zone_points,
                overlap_threshold=slot_request.overlap_threshold,
                viewpoint_id=slot_request.viewpoint_id
            )

            if not created_slot_id:
                LOGGER.warning(f"Failed to create slot {slot_request.slot_name} for lot {lot_id}")
                raise HTTPException(status_code=500, detail=f"Failed to create slot {slot_request.slot_name}")

            # Get the created slot
            updated_lot = StorageService.get_parking_lot(lot_id)
            created_slot = next((s for s in updated_lot.slots if s.id == created_slot_id), None)

            if created_slot:
                created_slots.append(convert_parking_slot_to_response(created_slot))
                LOGGER.debug(f"Created slot {slot_request.slot_name} with ID {created_slot_id}")

        # Return single slot or list based on input
        if len(created_slots) == 1:
            return created_slots[0]
        return created_slots

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error creating slots for lot {lot_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating slots: {str(e)}")

@router.put("/lots/{lot_id}/slots/{slot_id}")
async def update_slot_status(
    lot_id: str,
    slot_id: str,
    status_update: UpdateSlotStatusRequest,
    socketio = Depends(get_socketio)
) -> ParkingSlotResponse:
    """Update parking slot occupancy status."""
    LOGGER.info(f"API request: Update slot {slot_id} status in lot {lot_id}")

    # Verify the lot exists
    lot = StorageService.get_parking_lot(lot_id)
    if not lot:
        LOGGER.warning(f"Parking lot with ID {lot_id} not found when updating slot")
        raise HTTPException(status_code=404, detail="Parking lot not found")

    # Find the slot
    slot = next((s for s in lot.slots if s.id == slot_id), None)
    if not slot:
        LOGGER.warning(f"Slot with ID {slot_id} not found in lot {lot_id}")
        raise HTTPException(status_code=404, detail="Parking slot not found")

    try:
        # Update slot status using event sourcing
        success = StorageService.update_slot_occupancy(lot_id, slot_id, status_update.is_occupied)
        if not success:
            LOGGER.warning(f"Failed to update slot {slot_id} status in lot {lot_id}")
            raise HTTPException(status_code=500, detail="Failed to update slot status")

        # Get updated slot
        updated_lot = StorageService.get_parking_lot(lot_id)
        updated_slot = next((s for s in updated_lot.slots if s.id == slot_id), None)

        if updated_slot:
            # Emit SocketIO event for real-time updates
            await socketio.emit('slot_status_updated', {
                'lot_id': lot_id,
                'slot_id': slot_id,
                'is_occupied': status_update.is_occupied,
                'payload': {
                    'slot_id': slot_id,
                    'is_occupied': status_update.is_occupied
                }
            }, namespace='/dashboard')

            LOGGER.debug(f"Updated slot {slot_id} status to {status_update.is_occupied}")
            return convert_parking_slot_to_response(updated_slot)
        else:
            raise HTTPException(status_code=500, detail="Failed to retrieve updated slot")

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error updating slot {slot_id} status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error updating slot status: {str(e)}")


@router.put("/lots/{lot_id}/slots/{slot_id}/zone")
async def update_slot_zone(
    lot_id: str,
    slot_id: str,
    zone_update: UpdateSlotZoneRequest
) -> ParkingSlotResponse:
    """Update parking slot detection zone and overlap threshold."""
    LOGGER.info(f"API request: Update slot {slot_id} zone in lot {lot_id}")

    # Verify the lot exists
    lot = StorageService.get_parking_lot(lot_id)
    if not lot:
        LOGGER.warning(f"Parking lot with ID {lot_id} not found when updating slot zone")
        raise HTTPException(status_code=404, detail="Parking lot not found")

    # Find the slot
    slot = next((s for s in lot.slots if s.id == slot_id), None)
    if not slot:
        LOGGER.warning(f"Slot with ID {slot_id} not found in lot {lot_id}")
        raise HTTPException(status_code=404, detail="Parking slot not found")

    try:
        # Convert coordinates to zone points
        zone_points = []
        for coord in zone_update.coordinates:
            if len(coord) >= 2:
                zone_points.append({"x": coord[0], "y": coord[1]})

        # Update slot zone using event sourcing
        success = StorageService.update_slot_zone(lot_id, slot_id, zone_points)
        if not success:
            LOGGER.warning(f"Failed to update slot {slot_id} zone in lot {lot_id}")
            raise HTTPException(status_code=500, detail="Failed to update slot zone")

        # Update overlap threshold if provided
        if zone_update.overlap_threshold is not None:
            threshold_success = StorageService.update_slot_threshold(lot_id, slot_id, zone_update.overlap_threshold)
            if not threshold_success:
                LOGGER.warning(f"Failed to update slot {slot_id} threshold in lot {lot_id}")
                raise HTTPException(status_code=500, detail="Failed to update slot threshold")

        # Get updated slot
        updated_lot = StorageService.get_parking_lot(lot_id)
        updated_slot = next((s for s in updated_lot.slots if s.id == slot_id), None)

        if updated_slot:
            LOGGER.debug(f"Updated slot {slot_id} zone in lot {lot_id}")
            return convert_parking_slot_to_response(updated_slot)
        else:
            raise HTTPException(status_code=500, detail="Failed to retrieve updated slot")

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error updating slot {slot_id} zone: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error updating slot zone: {str(e)}")


@router.delete("/lots/{lot_id}/slots/{slot_id}")
async def delete_slot(lot_id: str, slot_id: str) -> Dict[str, Any]:
    """Delete a parking slot."""
    LOGGER.info(f"API request: Delete slot {slot_id} from lot {lot_id}")

    # Verify the lot exists
    lot = StorageService.get_parking_lot(lot_id)
    if not lot:
        LOGGER.warning(f"Parking lot with ID {lot_id} not found when deleting slot")
        raise HTTPException(status_code=404, detail="Parking lot not found")

    # Find the slot
    slot = next((s for s in lot.slots if s.id == slot_id), None)
    if not slot:
        LOGGER.warning(f"Slot with ID {slot_id} not found in lot {lot_id}")
        raise HTTPException(status_code=404, detail="Parking slot not found")

    try:
        # Delete slot using event sourcing
        success = StorageService.remove_parking_slot(lot_id, slot_id)
        if not success:
            LOGGER.warning(f"Failed to delete slot {slot_id} from lot {lot_id}")
            raise HTTPException(status_code=500, detail="Failed to delete slot")

        LOGGER.debug(f"Deleted slot {slot_id} from lot {lot_id}")
        return {"message": "Slot deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error deleting slot {slot_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting slot: {str(e)}")

# Helper function for async detection (matches Flask implementation)
async def _run_detection_async(lot_id: str, config: dict, socketio):
    """Run detection asynchronously in a separate thread."""
    def run_detection():
        try:
            LOGGER.debug(f"Starting async detection for lot {lot_id}")

            # Get the lot
            lot = StorageService.get_parking_lot(lot_id)
            if not lot or not lot.slots:
                return

            # Store previous states to detect changes
            previous_states = {slot.id: slot.is_occupied for slot in lot.slots}

            # Run batch detection for all viewpoints
            batch_results = DetectionManager.run_batch_detection(lot_id)

            # Process results and emit events for changes
            for viewpoint_id, viewpoint_results in batch_results.items():
                for result in viewpoint_results:
                    if result.slot_id in previous_states:
                        old_state = previous_states[result.slot_id]
                        if old_state != result.is_occupied:
                            # Emit slot status update event
                            import asyncio
                            asyncio.create_task(socketio.emit('slot_status_updated', {
                                'lot_id': lot_id,
                                'slot_id': result.slot_id,
                                'is_occupied': result.is_occupied,
                                'payload': {
                                    'slot_id': result.slot_id,
                                    'is_occupied': result.is_occupied
                                }
                            }, namespace='/dashboard'))

            LOGGER.debug(f"Completed async detection for lot {lot_id}")

        except Exception as e:
            LOGGER.error(f"Error in async detection for lot {lot_id}: {str(e)}")

    # Run in thread
    thread = threading.Thread(target=run_detection, daemon=True)
    thread.start()

@router.post("/lots/{lot_id}/image")
async def upload_lot_image(
    lot_id: str,
    file: UploadFile = File(...),
    config: dict = Depends(get_config),
    socketio = Depends(get_socketio)
) -> ImageUploadResponse:
    """Handle parking lot image upload via API using event sourcing."""
    LOGGER.info(f"API request: Upload image for parking lot {lot_id}")

    lot = StorageService.get_parking_lot(lot_id)
    if not lot:
        LOGGER.warning(f"Parking lot with ID {lot_id} not found when uploading image")
        raise HTTPException(status_code=404, detail="Parking lot not found")

    try:
        # Validate file
        if not file.filename:
            LOGGER.debug(f"No filename provided for lot {lot_id}")
            raise HTTPException(status_code=400, detail="No file selected")

        if not ImageHandler.allowed_file(file.filename):
            LOGGER.debug(f"Invalid file type for lot {lot_id}: {file.filename}")
            raise HTTPException(status_code=400, detail="Invalid file type. Only JPG and PNG are supported")

        # Read file data
        image_bytes = await file.read()
        if not image_bytes:
            LOGGER.debug(f"No image data received for lot {lot_id}")
            raise HTTPException(status_code=400, detail="No image data received")

        LOGGER.debug(f"Updating image for lot {lot_id}")

        # Update the image using ImageHandler
        relative_path = ImageHandler.update_image(
            image_bytes,
            lot.image_path,
            config['UPLOAD_FOLDER']
        )

        # Update lot with new image path using event sourcing
        LOGGER.debug(f"Updating image path in database for lot {lot_id}")
        success = StorageService.update_parking_lot_image(lot_id, relative_path)
        if not success:
            LOGGER.warning(f"Failed to update image path in database for lot {lot_id}")
            raise HTTPException(status_code=500, detail="Failed to update image path in database")

        # Emit WebSocket event for real-time updates
        LOGGER.debug(f"Emitting image update event for lot {lot_id}")
        await socketio.emit('image_updated', {
            'lot_id': lot_id,
            'image_path': relative_path,
            'viewpoint_id': None
        }, namespace='/dashboard')

        # Run detection asynchronously if there are configured slots
        if lot.slots:
            LOGGER.debug(f"Starting asynchronous detection for lot {lot_id} with {len(lot.slots)} slots")
            await _run_detection_async(lot_id, config, socketio)

        LOGGER.info(f"Successfully updated image for parking lot {lot_id}")
        return ImageUploadResponse(
            message="Image updated successfully",
            image_path=relative_path
        )

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f'Error updating image for lot {lot_id}: {str(e)}')
        raise HTTPException(status_code=500, detail=f"Error updating image: {str(e)}")

@router.get("/lots/{lot_id}/detection-config")
async def get_detection_config(lot_id: str) -> DetectionConfigResponse:
    """Get detection engine configuration for a parking lot."""
    LOGGER.info(f"API request: Get detection config for lot {lot_id}")

    lot = StorageService.get_parking_lot(lot_id)
    if not lot:
        LOGGER.warning(f"Parking lot with ID {lot_id} not found when getting detection config")
        raise HTTPException(status_code=404, detail="Parking lot not found")

    try:
        # Get or create default detection config
        config = DetectionManager.get_engine_config(lot_id)
        if not config:
            config = DetectionManager.get_default_config(lot_id)
            DetectionManager.configure_engine(lot_id, config)

        return DetectionConfigResponse(
            engine_type=config.engine_type,
            is_enabled=config.is_enabled,
            update_interval=config.update_interval,
            parameters=config.parameters
        )

    except Exception as e:
        LOGGER.error(f"Error getting detection config for lot {lot_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting detection config: {str(e)}")

@router.put("/lots/{lot_id}/detection-config")
async def update_detection_config(
    lot_id: str,
    config_update: DetectionConfigRequest
) -> DetectionConfigResponse:
    """Update detection engine configuration for a parking lot."""
    LOGGER.info(f"API request: Update detection config for lot {lot_id}")

    lot = StorageService.get_parking_lot(lot_id)
    if not lot:
        LOGGER.warning(f"Parking lot with ID {lot_id} not found when updating detection config")
        raise HTTPException(status_code=404, detail="Parking lot not found")

    try:
        # Create detection config from request
        from models.detection import DetectionEngineConfig
        config = DetectionEngineConfig(
            engine_type=config_update.engine_type,
            is_enabled=config_update.is_enabled,
            update_interval=config_update.update_interval,
            parameters=config_update.parameters
        )

        # Update using event sourcing
        success = DetectionManager.configure_engine(lot_id, config)
        if not success:
            LOGGER.warning(f"Failed to update detection config for lot {lot_id}")
            raise HTTPException(status_code=500, detail="Failed to update detection config")

        LOGGER.debug(f"Updated detection config for lot {lot_id}")
        return DetectionConfigResponse(
            engine_type=config.engine_type,
            is_enabled=config.is_enabled,
            update_interval=config.update_interval,
            parameters=config.parameters
        )

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error updating detection config for lot {lot_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error updating detection config: {str(e)}")

@router.get("/lots/{lot_id}/detection-classes")
async def get_detection_classes(lot_id: str) -> DetectionClassesResponse:
    """Get available detection classes for a parking lot."""
    LOGGER.info(f"API request: Get detection classes for lot {lot_id}")

    lot = StorageService.get_parking_lot(lot_id)
    if not lot:
        LOGGER.warning(f"Parking lot with ID {lot_id} not found when getting detection classes")
        raise HTTPException(status_code=404, detail="Parking lot not found")

    try:
        # If lot does not have a detection engine config, create a new one with default values
        if not lot.detection_config:
            config = DetectionManager.get_default_config(lot_id)
            DetectionManager.configure_engine(lot_id, config)

        engine = DetectionManager.get_engine_instance(lot_id)
        if engine:
            classes = engine.get_classes()
            return DetectionClassesResponse(classes=classes)
        else:
            raise HTTPException(status_code=404, detail="Detection engine not found")

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error getting detection classes for lot {lot_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting detection classes: {str(e)}")

@router.post("/lots/{lot_id}/detect")
async def run_detection(
    lot_id: str,
    socketio = Depends(get_socketio)
) -> ParkingLotStatsResponse:
    """Manually trigger a detection update for a parking lot using event sourcing."""
    LOGGER.info(f"API request: Manual detection for lot {lot_id}")

    lot = StorageService.get_parking_lot(lot_id)
    if not lot:
        LOGGER.warning(f"Parking lot with ID {lot_id} not found when running detection")
        raise HTTPException(status_code=404, detail="Parking lot not found")

    # Emit detection started event
    await socketio.emit('detection_status', {
        'lot_id': lot_id,
        'status': 'started'
    }, namespace='/dashboard')

    try:
        # Store previous states to detect changes
        previous_states = {slot.id: slot.is_occupied for slot in lot.slots}

        # Run batch detection for all viewpoints using the event-sourced method
        LOGGER.info(f"API: Running batch detection for lot {lot_id}")
        batch_results = DetectionManager.run_batch_detection(lot_id)

        # Convert batch results to updates format for compatibility
        updates = {}
        total_results = 0
        for viewpoint_id, viewpoint_results in batch_results.items():
            for result in viewpoint_results:
                updates[result.slot_id] = True  # Indicate slot was processed
                total_results += 1

                # Emit slot status update if changed
                if result.slot_id in previous_states:
                    old_state = previous_states[result.slot_id]
                    if old_state != result.is_occupied:
                        await socketio.emit('slot_status_updated', {
                            'lot_id': lot_id,
                            'slot_id': result.slot_id,
                            'is_occupied': result.is_occupied,
                            'payload': {
                                'slot_id': result.slot_id,
                                'is_occupied': result.is_occupied
                            }
                        }, namespace='/dashboard')

        # Get updated lot for statistics
        updated_lot = StorageService.get_parking_lot(lot_id)

        # Emit detection completed event
        await socketio.emit('detection_status', {
            'lot_id': lot_id,
            'status': 'completed',
            'updates': len(updates) if isinstance(updates, dict) else updates
        }, namespace='/dashboard')

        LOGGER.info(f"API: Detection completed for lot {lot_id} with {total_results} results")

        return ParkingLotStatsResponse(
            success=True,
            updates=len(updates) if isinstance(updates, dict) else updates,
            occupancy_rate=updated_lot.get_occupancy_rate(),
            free_slots=updated_lot.get_free_slot_count(),
            busy_slots=updated_lot.get_busy_slot_count()
        )

    except Exception as e:
        LOGGER.error(f"Error in manual detection for lot {lot_id}: {str(e)}")

        # Emit detection error event
        await socketio.emit('detection_status', {
            'lot_id': lot_id,
            'status': 'error',
            'error': str(e)
        }, namespace='/dashboard')

        raise HTTPException(status_code=500, detail=str(e))


@router.get("/lots/{lot_id}/grouped-stats")
async def get_grouped_slot_stats(lot_id: str) -> Dict[str, Any]:
    """Get grouped slot statistics for a parking lot."""
    LOGGER.info(f"API request: Get grouped slot statistics for lot {lot_id}")

    try:
        lot = StorageService.get_parking_lot(lot_id)
        if not lot:
            LOGGER.warning(f"Parking lot with ID {lot_id} not found when retrieving grouped stats")
            raise HTTPException(status_code=404, detail="Parking lot not found")

        # Get grouped slot statistics
        stats = lot.get_grouped_slot_stats()
        LOGGER.debug(f"Retrieved grouped stats for lot {lot_id}: {stats['total_groups']} groups")

        return stats

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error retrieving grouped stats for lot {lot_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/lots/{lot_id}/stream")
async def create_lot_with_stream(
    lot_id: str,
    stream_request: CreateLotWithStreamRequest,
    config: dict = Depends(get_config),
    socketio = Depends(get_socketio)
) -> CreateLotResponse:
    """Create a parking lot with video stream via API."""
    LOGGER.info(f"API request: Create parking lot with stream for lot {lot_id}")

    try:
        # Create parking lot using event sourcing (without image initially)
        created_lot_id = StorageService.create_parking_lot_with_id(
            lot_id=lot_id,
            name=stream_request.name,
            image_path=None  # Will be set when first frame is captured
        )

        if not created_lot_id:
            LOGGER.warning(f'Failed to create parking lot {lot_id} via API')
            raise HTTPException(status_code=500, detail="Failed to create parking lot")

        # Create a placeholder image for the viewpoint
        from src.services.image_handler import ImageHandler
        image_handler = ImageHandler(storage_dir=config['UPLOAD_FOLDER'])
        placeholder_path = image_handler.create_placeholder_image(lot_id, "stream_placeholder.jpg")

        # Create a viewpoint with stream configuration
        viewpoint_id = StorageService.add_viewpoint(
            lot_id=created_lot_id,
            name=stream_request.viewpoint_name,
            image_path=placeholder_path,
            camera_position=None,
            is_active=True,
            sort_order=0
        )

        if not viewpoint_id:
            LOGGER.warning(f"Failed to create viewpoint for lot {created_lot_id}")
            raise HTTPException(status_code=500, detail="Failed to create viewpoint")

        # Configure stream for the viewpoint
        success = StorageService.configure_viewpoint_stream(
            lot_id=created_lot_id,
            viewpoint_id=viewpoint_id,
            stream_url=stream_request.stream_url,
            capture_fps=stream_request.capture_fps
        )

        if not success:
            LOGGER.warning(f"Failed to configure stream for viewpoint {viewpoint_id}")
            raise HTTPException(status_code=500, detail="Failed to configure stream")

        # Activate stream
        StorageService.activate_viewpoint_stream(created_lot_id, viewpoint_id)

        # Set as default viewpoint
        StorageService.set_default_viewpoint(created_lot_id, viewpoint_id)

        # Start stream capture
        from src.services.stream_capture_service import StreamCaptureService
        capture_started = StreamCaptureService.start_stream_capture(created_lot_id, viewpoint_id)

        if not capture_started:
            LOGGER.warning(f"Failed to start stream capture for viewpoint {viewpoint_id}")
            # Don't fail the entire creation, just log the warning

        LOGGER.info(f'Parking lot created successfully with stream for lot {stream_request.name}')

        # Emit WebSocket event for real-time updates
        await socketio.emit('lot_created', {
            'lot_id': created_lot_id,
            'name': stream_request.name,
            'image_path': placeholder_path,
            'stream_url': stream_request.stream_url
        }, namespace='/dashboard')

        return CreateLotResponse(
            success=True,
            lot_id=created_lot_id,
            message="Parking lot created with video stream successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error creating lot with stream: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating parking lot: {str(e)}")


@router.post("/lots/{lot_id}/viewpoints/{viewpoint_id}/stream/start")
async def start_stream_capture(
    lot_id: str,
    viewpoint_id: str,
    socketio = Depends(get_socketio)
):
    """Start stream capture for a viewpoint."""
    LOGGER.info(f"API request: Start stream capture for viewpoint {viewpoint_id} in lot {lot_id}")

    # Verify the lot and viewpoint exist
    lot = StorageService.get_parking_lot(lot_id)
    if not lot:
        raise HTTPException(status_code=404, detail="Parking lot not found")

    viewpoint = lot.get_viewpoint(viewpoint_id)
    if not viewpoint:
        raise HTTPException(status_code=404, detail="Viewpoint not found")

    if not viewpoint.stream_url:
        raise HTTPException(status_code=400, detail="No stream URL configured for this viewpoint")

    try:
        # Activate stream in database
        success = StorageService.activate_viewpoint_stream(lot_id, viewpoint_id)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to activate stream in database")

        # Start capture service
        from src.services.stream_capture_service import StreamCaptureService
        capture_started = StreamCaptureService.start_stream_capture(lot_id, viewpoint_id)

        if not capture_started:
            # Deactivate in database if capture failed
            StorageService.deactivate_viewpoint_stream(lot_id, viewpoint_id)
            raise HTTPException(status_code=500, detail="Failed to start stream capture")

        # Emit WebSocket event
        await socketio.emit('stream_started', {
            'lot_id': lot_id,
            'viewpoint_id': viewpoint_id
        }, namespace='/dashboard')

        return {"success": True, "message": "Stream capture started successfully"}

    except HTTPException:
        raise
    except Exception as e:
        LOGGER.error(f"Error starting stream capture: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error starting stream: {str(e)}")


@router.post("/lots/{lot_id}/viewpoints/{viewpoint_id}/stream/stop")
async def stop_stream_capture(
    lot_id: str,
    viewpoint_id: str,
    socketio = Depends(get_socketio)
):
    """Stop stream capture for a viewpoint."""
    LOGGER.info(f"API request: Stop stream capture for viewpoint {viewpoint_id} in lot {lot_id}")

    # Verify the lot and viewpoint exist
    lot = StorageService.get_parking_lot(lot_id)
    if not lot:
        raise HTTPException(status_code=404, detail="Parking lot not found")

    viewpoint = lot.get_viewpoint(viewpoint_id)
    if not viewpoint:
        raise HTTPException(status_code=404, detail="Viewpoint not found")

    try:
        # Stop capture service
        from src.services.stream_capture_service import StreamCaptureService
        StreamCaptureService.stop_stream_capture(lot_id, viewpoint_id)

        # Deactivate stream in database
        success = StorageService.deactivate_viewpoint_stream(lot_id, viewpoint_id)
        if not success:
            LOGGER.warning(f"Failed to deactivate stream in database for viewpoint {viewpoint_id}")

        # Emit WebSocket event
        await socketio.emit('stream_stopped', {
            'lot_id': lot_id,
            'viewpoint_id': viewpoint_id
        }, namespace='/dashboard')

        return {"success": True, "message": "Stream capture stopped successfully"}

    except Exception as e:
        LOGGER.error(f"Error stopping stream capture: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error stopping stream: {str(e)}")


@router.get("/streams/status")
async def get_streams_status():
    """Get status of all active stream captures."""
    LOGGER.info("API request: Get streams status")

    try:
        from src.services.stream_capture_service import StreamCaptureService
        status = StreamCaptureService.get_active_streams_status()

        LOGGER.debug(f"Retrieved status for {status['total_active']} active streams")
        return status

    except Exception as e:
        LOGGER.error(f"Error getting streams status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting streams status: {str(e)}")


@router.post("/streams/restart-all")
async def restart_all_streams(socketio = Depends(get_socketio)):
    """Restart all configured streams."""
    LOGGER.info("API request: Restart all streams")

    try:
        from src.services.stream_capture_service import StreamCaptureService

        # Stop all current streams
        StreamCaptureService.stop_all_captures()
        LOGGER.info("Stopped all existing streams")

        # Start all configured streams
        started_count, failed_count = StreamCaptureService.start_all_configured_streams()

        # Emit WebSocket event
        await socketio.emit('streams_restarted', {
            'started_count': started_count,
            'failed_count': failed_count
        }, namespace='/dashboard')

        return {
            "success": True,
            "message": f"Streams restarted: {started_count} started, {failed_count} failed",
            "started_count": started_count,
            "failed_count": failed_count
        }

    except Exception as e:
        LOGGER.error(f"Error restarting streams: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error restarting streams: {str(e)}")
