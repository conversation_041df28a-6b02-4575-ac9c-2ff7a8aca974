"""
Unit tests for the Storage facade class
"""
import pytest
from unittest.mock import Mock, patch
import uuid
from datetime import datetime

from app.src.utils.storage import Storage
from app.src.utils.event_sourced_storage import EventSourcedStorage
from app.src.models.parking import ParkingLot, ParkingSlot
from app.src.models.geometry import <PERSON><PERSON><PERSON>, Point


class TestStorage:
    """Tests for the Storage singleton facade class"""

    @pytest.fixture
    def mock_event_sourced_storage(self):
        """Mock EventSourcedStorage for testing"""
        mock_repo = Mock(spec=EventSourcedStorage)

        # Configure common mock returns
        mock_repo.get_parking_lot.return_value = None
        mock_repo.get_all_parking_lots.return_value = []
        mock_repo.create_parking_lot.return_value = str(uuid.uuid4())
        mock_repo.delete_parking_lot.return_value = True
        mock_repo.update_parking_lot_image.return_value = True
        mock_repo.rename_parking_lot.return_value = True
        mock_repo.add_parking_slot.return_value = str(uuid.uuid4())
        mock_repo.update_slot_zone.return_value = True
        mock_repo.update_slot_threshold.return_value = True
        mock_repo.update_slot_occupancy.return_value = True
        mock_repo.remove_parking_slot.return_value = True
        mock_repo.create_detection_config.return_value = True
        mock_repo.delete_detection_config.return_value = True
        mock_repo._get_events_for_lot.return_value = []

        return mock_repo

    @pytest.fixture
    def reset_storage_singleton(self):
        """Reset Storage singleton before and after test"""
        # Reset before test
        Storage._instance = None
        Storage._initialized = False
        yield
        # Reset after test
        Storage._instance = None
        Storage._initialized = False

    @patch('app.src.utils.storage.SQLAlchemyEventStore')
    def test_storage_initialization(self, mock_sql_event_store, reset_storage_singleton):
        """Test Storage singleton initialization"""
        # Arrange
        mock_event_store = Mock()
        mock_sql_event_store.instance.return_value = mock_event_store

        # Act
        storage = Storage()

        # Assert
        assert storage._initialized is True
        assert mock_sql_event_store.instance.called
        assert storage._repo is not None
        assert isinstance(storage._repo, EventSourcedStorage)

        # Test singleton pattern
        storage2 = Storage()
        assert storage is storage2  # Same instance

    @patch('app.src.utils.storage.SQLAlchemyEventStore')
    def test_get_instance_class_method(self, mock_sql_event_store, reset_storage_singleton):
        """Test get_instance class method returns singleton"""
        # Act
        storage1 = Storage.get_instance()
        storage2 = Storage.get_instance()

        # Assert
        assert storage1 is storage2
        assert storage1._initialized is True

    def test_get_parking_lot(self, mock_event_sourced_storage, reset_storage_singleton):
        """Test get_parking_lot delegates to repo"""
        # Arrange
        mock_lot = Mock(spec=ParkingLot)
        mock_event_sourced_storage.get_parking_lot.return_value = mock_lot

        # Create storage instance and set mock repo
        storage = Storage()
        # Manual patch of the instance attribute
        storage._repo = mock_event_sourced_storage

        lot_id = str(uuid.uuid4())

        # Act
        result = storage.get_parking_lot(lot_id)

        # Assert
        mock_event_sourced_storage.get_parking_lot.assert_called_once_with(lot_id)
        assert result is mock_lot

    def test_get_all_parking_lots(self, mock_event_sourced_storage, reset_storage_singleton):
        """Test get_all_parking_lots delegates to repo"""
        # Arrange
        mock_lots = [Mock(spec=ParkingLot), Mock(spec=ParkingLot)]
        mock_event_sourced_storage.get_all_parking_lots.return_value = mock_lots

        # Create storage instance and set mock repo
        storage = Storage()
        storage._repo = mock_event_sourced_storage

        # Act
        result = storage.get_all_parking_lots()

        # Assert
        mock_event_sourced_storage.get_all_parking_lots.assert_called_once()
        assert result is mock_lots

    def test_create_parking_lot(self, mock_event_sourced_storage, reset_storage_singleton):
        """Test create_parking_lot delegates to repo"""
        # Arrange
        expected_id = str(uuid.uuid4())
        mock_event_sourced_storage.create_parking_lot_with_id.return_value = expected_id

        # Create storage instance and set mock repo
        storage = Storage()
        storage._repo = mock_event_sourced_storage

        name = "Test Parking Lot"
        image_path = "/path/to/image.jpg"

        # Act
        result = storage.create_parking_lot(name, image_path)

        # Assert
        mock_event_sourced_storage.create_parking_lot_with_id.assert_called_once()
        assert result == expected_id

    def test_delete_parking_lot(self, mock_event_sourced_storage, reset_storage_singleton):
        """Test delete_parking_lot delegates to repo"""
        # Arrange
        mock_event_sourced_storage.delete_parking_lot.return_value = True

        # Create storage instance and set mock repo
        storage = Storage()
        storage._repo = mock_event_sourced_storage

        lot_id = str(uuid.uuid4())

        # Act
        result = storage.delete_parking_lot(lot_id)

        # Assert
        mock_event_sourced_storage.delete_parking_lot.assert_called_once_with(lot_id)
        assert result is True

    def test_update_parking_lot_image(self, mock_event_sourced_storage, reset_storage_singleton):
        """Test update_parking_lot_image delegates to repo"""
        # Arrange
        mock_event_sourced_storage.update_parking_lot_image.return_value = True

        # Create storage instance and set mock repo
        storage = Storage()
        storage._repo = mock_event_sourced_storage

        lot_id = str(uuid.uuid4())
        new_image_path = "/path/to/new_image.jpg"

        # Act
        result = storage.update_parking_lot_image(lot_id, new_image_path)

        # Assert
        mock_event_sourced_storage.update_parking_lot_image.assert_called_once_with(lot_id, new_image_path)
        assert result is True

    def test_rename_parking_lot(self, mock_event_sourced_storage, reset_storage_singleton):
        """Test rename_parking_lot delegates to repo"""
        # Arrange
        mock_event_sourced_storage.rename_parking_lot.return_value = True

        # Create storage instance and set mock repo
        storage = Storage()
        storage._repo = mock_event_sourced_storage

        lot_id = str(uuid.uuid4())
        new_name = "New Parking Lot Name"

        # Act
        result = storage.rename_parking_lot(lot_id, new_name)

        # Assert
        mock_event_sourced_storage.rename_parking_lot.assert_called_once_with(lot_id, new_name)
        assert result is True

    def test_add_parking_slot(self, mock_event_sourced_storage, reset_storage_singleton):
        """Test add_parking_slot delegates to repo"""
        # Arrange
        expected_id = str(uuid.uuid4())
        mock_event_sourced_storage.add_parking_slot.return_value = expected_id

        # Create storage instance and set mock repo
        storage = Storage()
        storage._repo = mock_event_sourced_storage

        lot_id = str(uuid.uuid4())
        zone_points = [
            {"x": 10, "y": 10},
            {"x": 20, "y": 10},
            {"x": 20, "y": 20},
            {"x": 10, "y": 20}
        ]
        overlap_threshold = 0.4

        # Act
        result = storage.add_parking_slot(lot_id, "", zone_points, overlap_threshold)
        # Assert
        mock_event_sourced_storage.add_parking_slot.assert_called_once_with(lot_id, "", zone_points, overlap_threshold, None)
        assert result == expected_id

    def test_update_slot_occupancy(self, mock_event_sourced_storage, reset_storage_singleton):
        """Test update_slot_occupancy delegates to repo"""
        # Arrange
        mock_event_sourced_storage.update_slot_occupancy.return_value = True

        # Create storage instance and set mock repo
        storage = Storage()
        storage._repo = mock_event_sourced_storage

        lot_id = str(uuid.uuid4())
        slot_id = str(uuid.uuid4())
        is_occupied = True
        detection_timestamp = datetime.now()

        # Act
        result = storage.update_slot_occupancy(lot_id, slot_id, is_occupied, detection_timestamp)

        # Assert
        mock_event_sourced_storage.update_slot_occupancy.assert_called_once_with(
            lot_id, slot_id, is_occupied, detection_timestamp
        )
        assert result is True

    def test_deprecated_update_parking_lot(self, mock_event_sourced_storage, reset_storage_singleton):
        """Test deprecated update_parking_lot raises NotImplementedError"""
        # Arrange
        storage = Storage()
        storage._repo = mock_event_sourced_storage

        lot_id = str(uuid.uuid4())
        lot = Mock(spec=ParkingLot)

        # Act & Assert
        with pytest.raises(NotImplementedError):
            storage.update_parking_lot(lot_id, lot)
