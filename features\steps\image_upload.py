# type: ignore

from behave import *
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from pathlib import Path
import time

use_step_matcher("re")


@given("the user is on the image upload page")
def step_impl(context):
    context.driver.get(f"{context.base_url}/upload")
    assert "Upload Parking Lot Image" in context.driver.title


@when("the user selects a valid JPEG image file")
def step_impl(context):
    file_input = context.driver.find_element(By.ID, "file")
    file_input.send_keys(str(context.valid_image_path.absolute()))

    name_input = context.driver.find_element(By.ID, "name")
    context.lot_name = "Test Parking Lot"
    name_input.send_keys(context.lot_name)


@step("the user clicks the upload button")
def step_impl(context):
    submit_button = context.driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
    submit_button.click()
    time.sleep(1)  # Brief pause for upload processing


@then("the system should display the image in the zone editor")
def step_impl(context):
    assert "Edit Parking Zones" in context.driver.title
    image = context.driver.find_element(By.ID, "parking-image")
    assert image.is_displayed()
    context.uploaded_image_url = image.get_attribute("src")


@step("the system should save the image to storage")
def step_impl(context):
    image_url = context.uploaded_image_url
    assert "uploads/" in image_url, "Image URL doesn't contain uploads directory"

    # Extract path from URL
    image_path = Path("static") / Path(image_url.split("/static/")[1])
    assert image_path.exists(), f"Uploaded image not found at {image_path}"
    context.uploaded_image_path = image_path


@when("the user selects a file that is not an image")
def step_impl(context):
    file_input = context.driver.find_element(By.ID, "file")
    file_input.send_keys(str(context.invalid_file_path.absolute()))

    name_input = context.driver.find_element(By.ID, "name")
    name_input.send_keys("Test Parking Lot")


@then("the system should show an error message")
def step_impl(context):
    WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.CLASS_NAME, "alert"))
    )
    error_message = context.driver.find_element(By.CLASS_NAME, "alert")
    assert "Invalid file type" in error_message.text


@step("the upload should be rejected")
def step_impl(context):
    assert "Upload Parking Lot Image" in context.driver.title
    # Verify we're still on the upload page