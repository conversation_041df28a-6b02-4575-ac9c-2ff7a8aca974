Feature: Update Parking Lot Image
  As a parking manager
  I want to upload a new image for an existing parking lot
  So that I can update the detection based on physical changes

  Scenario: Successfully update parking lot image
    Given the user is logged in as "parking manager"
    And the parking lot "Downtown" has 10 configured parking slots
    When the user views the dashboard
    And the user clicks the "Update Image" button
    And the user selects a valid JPEG image file
    And the user clicks the "Update Image" button
    Then the system should replace the existing parking lot image
    And the system should preserve the parking lot slots

  Scenario: Attempt to update with invalid file type
    Given the user is logged in as "parking manager"
    And the parking lot "Downtown" has 10 configured parking slots
    When the user clicks the "Update Image" button
    And the user selects a file that is not a supported image
    And the user confirms the new image upload
    Then the system should show an error message
    And the original parking lot image should remain unchanged
