import pytest
from unittest.mock import Mock, patch
import uuid
from datetime import datetime

from app.src.utils.event_store_interface import EventStore
from app.src.utils.event_sourced_storage import EventSourcedStorage
from app.src.models.events import (
    ParkingLotCreated, Parking<PERSON>otDeleted, ParkingLotRenamed,
    ParkingLotImageUpdated, ParkingSlotAdded, ParkingSlotRemoved,
    ParkingSlotZoneUpdated, ParkingSlotThresholdUpdated,
    ParkingSlotOccupancyChanged, DetectionConfigCreated,
    DetectionConfigUpdated, DetectionConfigDeleted
)
from app.src.models.parking import ParkingLot, ParkingSlot
from app.src.models.geometry import Polygon, Point
from app.src.models.detection import DetectionEngineConfig

class TestEventSourcedStorageAdditional:
    """Additional tests for EventSourcedStorage to improve coverage"""
    
    @pytest.fixture
    def mock_event_store(self):
        """Mock event store for testing"""
        store = Mock(spec=EventStore)
        store.append_event.return_value = True
        store.get_raw_events_for_aggregate.return_value = []
        store.get_all_aggregate_ids.return_value = []
        return store
    
    @pytest.fixture
    def storage(self, mock_event_store):
        """EventSourcedStorage instance with mock event store"""
        return EventSourcedStorage(mock_event_store)
    
    def test_update_slot_threshold(self, storage, mock_event_store):
        """Test updating a slot's overlap threshold"""
        # Arrange
        lot_id = str(uuid.uuid4())
        slot_id = str(uuid.uuid4())
        new_threshold = 0.7
          # Mock the get_parking_lot method to return a lot with a slot
        mock_lot = ParkingLot(
            id=lot_id,
            name="Test Lot",
            slots=[ParkingSlot(id=slot_id, slot_name=f"slot_{slot_id}", overlap_threshold=0.3)]
        )
        with patch.object(storage, 'get_parking_lot', return_value=mock_lot):
            # Act
            result = storage.update_slot_threshold(lot_id, slot_id, new_threshold)
            
            # Assert
            assert result is True
            # Verify the event was created with the correct payload
            mock_event_store.append_event.assert_called_once()
            event_orm = mock_event_store.append_event.call_args[0][0]
            assert event_orm.lot_id == lot_id
            assert event_orm.event_type == "ParkingSlotThresholdUpdated"
            assert event_orm.payload["slot_id"] == slot_id
            assert event_orm.payload["new_overlap_threshold"] == new_threshold
    
    def test_update_slot_threshold_nonexistent_lot(self, storage):
        """Test updating threshold with nonexistent lot"""
        # Arrange
        with patch.object(storage, 'get_parking_lot', return_value=None):
            # Act
            result = storage.update_slot_threshold("nonexistent", "slot1", 0.5)
            
            # Assert
            assert result is False
    
    def test_update_slot_threshold_nonexistent_slot(self, storage):
        """Test updating threshold with nonexistent slot"""
        # Arrange
        lot_id = str(uuid.uuid4())
        mock_lot = ParkingLot(id=lot_id, name="Test Lot")
        
        with patch.object(storage, 'get_parking_lot', return_value=mock_lot):
            # Act
            result = storage.update_slot_threshold(lot_id, "nonexistent", 0.5)
            
            # Assert
            assert result is False
    
    def test_create_detection_config(self, storage, mock_event_store):
        """Test creating a detection configuration"""
        # Arrange
        lot_id = str(uuid.uuid4())
        engine_type = "yolo"
        is_enabled = True
        update_interval = 60
        parameters = {"confidence": 0.5}
        
        # Mock the get_parking_lot method to return a lot
        mock_lot = ParkingLot(id=lot_id, name="Test Lot")
        with patch.object(storage, 'get_parking_lot', return_value=mock_lot):
            # Act
            result = storage.create_detection_config(
                lot_id, engine_type, is_enabled, update_interval, parameters
            )
            
            # Assert
            assert result is True
            # Verify the event was created with the correct payload
            mock_event_store.append_event.assert_called_once()
            event_orm = mock_event_store.append_event.call_args[0][0]
            assert event_orm.lot_id == lot_id
            assert event_orm.event_type == "DetectionConfigCreated"
            assert event_orm.payload["engine_type"] == engine_type
            assert event_orm.payload["is_enabled"] == is_enabled
            assert event_orm.payload["update_interval"] == update_interval
            assert event_orm.payload["parameters"] == parameters
    
    def test_delete_detection_config(self, storage, mock_event_store):
        """Test deleting a detection configuration"""
        # Arrange
        lot_id = str(uuid.uuid4())
        
        # Mock the get_parking_lot method to return a lot with detection config
        mock_lot = ParkingLot(
            id=lot_id,
            name="Test Lot",
            detection_config=DetectionEngineConfig(
                engine_type="yolo",
                is_enabled=True,
                update_interval=30
            )
        )
        with patch.object(storage, 'get_parking_lot', return_value=mock_lot):
            # Act
            result = storage.delete_detection_config(lot_id)
            
            # Assert
            assert result is True
            # Verify the event was created correctly
            mock_event_store.append_event.assert_called_once()
            event_orm = mock_event_store.append_event.call_args[0][0]
            assert event_orm.lot_id == lot_id
            assert event_orm.event_type == "DetectionConfigDeleted"
    
    def test_get_events_for_lot(self, storage, mock_event_store):
        """Test getting events for a lot"""
        # Arrange
        lot_id = str(uuid.uuid4())
        
        # Create some mock events
        mock_event1 = ParkingLotCreated(
            lot_id=lot_id,
            payload={"name": "Test Lot", "image_path": None}
        )
        mock_event2 = ParkingLotRenamed(
            lot_id=lot_id,
            payload={"new_name": "Renamed Lot"}
        )
        
        # Return mock events when get_raw_events_for_aggregate is called
        from app.src.utils.mapper import Mapper
        mock_event_store.get_raw_events_for_aggregate.return_value = [
            Mapper.dataclass_to_orm_event(mock_event1),
            Mapper.dataclass_to_orm_event(mock_event2)
        ]
        
        # Act
        events = storage._get_events_for_lot(lot_id)
        
        # Assert
        assert len(events) == 2
        assert events[0].event_type == "ParkingLotCreated"
        assert events[1].event_type == "ParkingLotRenamed"
        mock_event_store.get_raw_events_for_aggregate.assert_called_once_with(lot_id)
    
    def test_nonexistent_slot_operations(self, storage):
        """Test various operations with nonexistent slots"""
        # Arrange
        lot_id = str(uuid.uuid4())
        slot_id = "nonexistent"
        mock_lot = ParkingLot(id=lot_id, name="Test Lot")
        
        with patch.object(storage, 'get_parking_lot', return_value=mock_lot):
            # Act & Assert - Update slot zone
            zone_points = [{"x": 10, "y": 10}, {"x": 20, "y": 10}, 
                           {"x": 20, "y": 20}, {"x": 10, "y": 20}]
            assert storage.update_slot_zone(lot_id, slot_id, zone_points) is False
            
            # Act & Assert - Update slot occupancy
            assert storage.update_slot_occupancy(lot_id, slot_id, True) is False
            
            # Act & Assert - Remove slot
            assert storage.remove_parking_slot(lot_id, slot_id) is False
    
    def test_get_all_parking_lots(self, storage, mock_event_store):
        """Test retrieving all parking lots"""
        # Arrange
        lot_id1 = str(uuid.uuid4())
        lot_id2 = str(uuid.uuid4())
        mock_event_store.get_all_aggregate_ids.return_value = [lot_id1, lot_id2]
        
        # Mock get_parking_lot to return different lots for different IDs
        lot1 = ParkingLot(id=lot_id1, name="Lot 1")
        lot2 = ParkingLot(id=lot_id2, name="Lot 2")
        
        with patch.object(storage, 'get_parking_lot', side_effect=[lot1, lot2]):
            # Act
            lots = storage.get_all_parking_lots()
            
            # Assert
            assert len(lots) == 2
            assert lots[0].id == lot_id1
            assert lots[1].id == lot_id2
            assert lots[0].name == "Lot 1"
            assert lots[1].name == "Lot 2"
            mock_event_store.get_all_aggregate_ids.assert_called_once()
    
    def test_get_all_parking_lots_with_deleted(self, storage, mock_event_store):
        """Test retrieving all parking lots including deleted ones"""
        # Arrange
        lot_id1 = str(uuid.uuid4())
        lot_id2 = str(uuid.uuid4())
        mock_event_store.get_all_aggregate_ids.return_value = [lot_id1, lot_id2]
        
        # Mock get_parking_lot to return one lot and one None (deleted)
        lot1 = ParkingLot(id=lot_id1, name="Lot 1")
        
        with patch.object(storage, 'get_parking_lot', side_effect=[lot1, None]):
            # Act
            lots = storage.get_all_parking_lots()
            
            # Assert
            assert len(lots) == 1
            assert lots[0].id == lot_id1
            assert lots[0].name == "Lot 1"
    
    def test_failed_event_persistence(self, storage, mock_event_store):
        """Test handling failed event persistence"""
        # Arrange
        lot_id = str(uuid.uuid4())
        name = "Test Lot"
        
        # Make append_event return False to simulate failure
        mock_event_store.append_event.return_value = False
        
        # Act
        result = storage.create_parking_lot(name)
        
        # Assert
        assert result is None
        mock_event_store.append_event.assert_called_once()
    
    def test_update_operations_with_event_failures(self, storage, mock_event_store):
        """Test update operations when event persistence fails"""
        # Arrange
        lot_id = str(uuid.uuid4())
        mock_lot = ParkingLot(id=lot_id, name="Test Lot")
        
        with patch.object(storage, 'get_parking_lot', return_value=mock_lot):
            # Make append_event return False to simulate failure
            mock_event_store.append_event.return_value = False
            
            # Act & Assert - Rename
            assert storage.rename_parking_lot(lot_id, "New Name") is False
            
            # Act & Assert - Update image
            assert storage.update_parking_lot_image(lot_id, "/path/new.jpg") is False
            
            # Reset append_event between tests
            mock_event_store.append_event.reset_mock()
            mock_event_store.append_event.return_value = False