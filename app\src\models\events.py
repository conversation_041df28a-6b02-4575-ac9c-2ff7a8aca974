from dataclasses import dataclass, field
from datetime import datetime, UTC  # Add UTC import
from typing import Optional, Dict, Any, List

# Base Event Structure (Aligns with ORM changes)
@dataclass
class BaseEvent:
    lot_id: str
    payload: Dict[str, Any] # Moved before fields with defaults
    event_type: str = field(init=False) # Set by subclasses
    timestamp: datetime = field(default_factory=lambda: datetime.now(UTC))  # Use datetime.now(UTC)
    sequence_id: Optional[int] = None # Will be set during persistence

    def to_dict(self) -> dict:
        return {
            'lot_id': self.lot_id,
            'event_type': self.event_type,
            'payload': self.payload,
            'timestamp': self.timestamp.isoformat(),
            'sequence_id': self.sequence_id
        }

    @classmethod
    def from_dict(cls, data: dict):
        # Note: This generic from_dict might need refinement
        # if specific event types require different payload handling.
        # It's often better to have specific from_dict on subclasses
        # or use a factory based on event_type.
        instance = cls(
            lot_id=data['lot_id'],
            payload=data['payload'],
            timestamp=datetime.fromisoformat(data['timestamp'])
        )
        instance.sequence_id = data.get('sequence_id')
        # event_type is usually set by the subclass __init__ or class attribute
        if hasattr(instance, 'event_type') and 'event_type' in data:
             assert instance.event_type == data['event_type'] # Sanity check
        return instance

# --- Specific Event Definitions ---

@dataclass
class ParkingLotCreated(BaseEvent):
    event_type: str = "ParkingLotCreated"
    # Payload: { name: str, image_path: Optional[str] }

@dataclass
class ParkingLotDeleted(BaseEvent):
    event_type: str = "ParkingLotDeleted"
    # Payload: {} # lot_id is already part of BaseEvent

@dataclass
class ParkingLotRenamed(BaseEvent):
    event_type: str = "ParkingLotRenamed"
    # Payload: { new_name: str }

@dataclass
class ParkingLotImageUpdated(BaseEvent):
    event_type: str = "ParkingLotImageUpdated"
    # Payload: { new_image_path: Optional[str] }

@dataclass
class ParkingSlotAdded(BaseEvent):
    event_type: str = "ParkingSlotAdded"
    # Payload: { slot_id: str, slot_name: str, overlap_threshold: float, zone_points: List[Dict[str, float]], viewpoint_id: Optional[str] }

@dataclass
class ParkingSlotRemoved(BaseEvent):
    event_type: str = "ParkingSlotRemoved"
    # Payload: { slot_id: str, viewpoint_id: Optional[str] }

@dataclass
class ParkingSlotZoneUpdated(BaseEvent):
    event_type: str = "ParkingSlotZoneUpdated"
    # Payload: { slot_id: str, new_zone_points: List[Dict[str, float]], viewpoint_id: Optional[str] }

@dataclass
class ParkingSlotThresholdUpdated(BaseEvent):
    event_type: str = "ParkingSlotThresholdUpdated"
    # Payload: { slot_id: str, new_overlap_threshold: float }

@dataclass
class ParkingSlotOccupancyChanged(BaseEvent):
    event_type: str = "ParkingSlotOccupancyChanged"
    # Payload: { slot_id: str, is_occupied: bool, detection_timestamp: Optional[str] } # ISO format timestamp

@dataclass
class DetectionConfigCreated(BaseEvent):
    event_type: str = "DetectionConfigCreated"
    # Payload: { engine_type: str, is_enabled: bool, update_interval: int, parameters: Dict[str, Any] }

@dataclass
class DetectionConfigUpdated(BaseEvent):
    event_type: str = "DetectionConfigUpdated"
    # Payload: { engine_type: str, is_enabled: bool, update_interval: int, parameters: Dict[str, Any] }

@dataclass
class DetectionConfigDeleted(BaseEvent):
    event_type: str = "DetectionConfigDeleted"
    # Payload: {}

# --- Viewpoint Management Events ---
# Design Decision: Use dedicated viewpoint zone events rather than reusing ParkingSlot events
# Rationale:
# 1. Separation of Concerns: ParkingSlot events manage global lot state, ViewpointZone events manage camera-specific state
# 2. Different Lifecycles: A parking slot can exist without viewpoint zones, and viewpoint zones are camera-specific
# 3. Event Sourcing Best Practice: Each event represents a single atomic business operation
# 4. Data Model Clarity: ParkingSlot and ViewpointZone have different semantics and properties

@dataclass
class ViewpointAdded(BaseEvent):
    event_type: str = "ViewpointAdded"
    # Payload: { viewpoint_id: str, name: str, image_path: str, camera_position: Optional[Dict], is_active: bool, sort_order: int }

@dataclass
class ViewpointRemoved(BaseEvent):
    event_type: str = "ViewpointRemoved"
    # Payload: { viewpoint_id: str }

@dataclass
class ViewpointUpdated(BaseEvent):
    event_type: str = "ViewpointUpdated"
    # Payload: { viewpoint_id: str, name: Optional[str], image_path: Optional[str], camera_position: Optional[Dict], is_active: Optional[bool], sort_order: Optional[int] }

@dataclass
class ViewpointStreamConfigured(BaseEvent):
    event_type: str = "ViewpointStreamConfigured"
    # Payload: { viewpoint_id: str, stream_url: str, capture_fps: float }

@dataclass
class ViewpointStreamActivated(BaseEvent):
    event_type: str = "ViewpointStreamActivated"
    # Payload: { viewpoint_id: str }

@dataclass
class ViewpointStreamDeactivated(BaseEvent):
    event_type: str = "ViewpointStreamDeactivated"
    # Payload: { viewpoint_id: str }

@dataclass
class ViewpointStreamStatusChanged(BaseEvent):
    event_type: str = "ViewpointStreamStatusChanged"
    # Payload: { viewpoint_id: str, status: str, error_message: Optional[str], last_frame_time: Optional[float] }

@dataclass
class ViewpointZoneAdded(BaseEvent):
    event_type: str = "ViewpointZoneAdded"
    # Payload: { viewpoint_id: str, slot_id: str, zone_points: List[Dict[str, float]] }

@dataclass
class ViewpointZoneUpdated(BaseEvent):
    event_type: str = "ViewpointZoneUpdated"
    # Payload: { viewpoint_id: str, slot_id: str, zone_points: List[Dict[str, float]] }

@dataclass
class ViewpointZoneRemoved(BaseEvent):
    event_type: str = "ViewpointZoneRemoved"
    # Payload: { viewpoint_id: str, slot_id: str }

@dataclass
class DefaultViewpointSet(BaseEvent):
    event_type: str = "DefaultViewpointSet"
    # Payload: { viewpoint_id: str }

# --- Deprecate old ParkingEvent ---
# Keep for reference or gradual migration if needed, but new code should use specific events.
@dataclass
class ParkingEvent: # Consider renaming to LegacyParkingEvent or removing
    lot_id: str
    event_type: str
    description: str # Less structured, replaced by specific event types/payloads
    payload: Dict[str, Any] = field(default_factory=dict)
    timestamp: Optional[datetime] = None
    sequence_id: Optional[int] = None # Added for compatibility if needed

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now(UTC)

    def to_dict(self):
        return {
            'lot_id': self.lot_id,
            'event_type': self.event_type,
            'description': self.description,
            'payload': self.payload,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'sequence_id': self.sequence_id
        }

    @classmethod
    def from_dict(cls, data):
        timestamp = None
        if data.get('timestamp'):
            try:
                timestamp = datetime.fromisoformat(data['timestamp'])
            except (ValueError, TypeError):
                # Handle potential legacy formats or nulls if necessary
                pass
        return cls(
            lot_id=data['lot_id'],
            event_type=data['event_type'],
            description=data.get('description', ''), # Ensure description exists
            payload=data.get('payload', {}),
            timestamp=timestamp,
            sequence_id=data.get('sequence_id')
        )